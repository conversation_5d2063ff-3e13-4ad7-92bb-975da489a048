// Advanced Restaurant Profile Management Types

export interface RestaurantProfile {
  id: string;
  name: string;
  description: string;
  tagline?: string;
  establishedDate: Date;
  
  // Media
  coverPhoto: string;
  profilePicture: string;
  gallery: MediaItem[];
  
  // Basic Information
  cuisineTypes: CuisineType[];
  priceRange: PriceRange;
  rating: Rating;
  
  // Contact & Location
  contact: ContactInfo;
  location: LocationInfo;
  
  // Business Details
  operatingHours: OperatingHours;
  businessInfo: BusinessInfo;
  
  // Social & Online Presence
  socialMedia: SocialMediaLinks;
  website?: string;
  
  // Settings & Preferences
  settings: RestaurantSettings;
  
  // Analytics & Performance
  analytics: RestaurantAnalytics;
  
  // Team & Staff
  team: TeamMember[];
  
  // Achievements & Certifications
  achievements: Achievement[];
  certifications: Certification[];
}

export interface MediaItem {
  id: string;
  type: 'image' | 'video';
  url: string;
  thumbnail?: string;
  title?: string;
  description?: string;
  category: 'food' | 'interior' | 'exterior' | 'team' | 'events' | 'menu';
  uploadDate: Date;
  featured: boolean;
  order: number;
}

export interface CuisineType {
  id: string;
  name: string;
  color: string;
  icon: string;
}

export interface PriceRange {
  level: 1 | 2 | 3 | 4; // $ $$ $$$ $$$$
  currency: string;
  averagePrice: number;
  minPrice: number;
  maxPrice: number;
}

export interface Rating {
  overall: number;
  totalReviews: number;
  breakdown: {
    food: number;
    service: number;
    ambiance: number;
    value: number;
  };
  recentTrend: 'up' | 'down' | 'stable';
}

export interface ContactInfo {
  primaryPhone: string;
  secondaryPhone?: string;
  email: string;
  whatsapp?: string;
  emergencyContact?: string;
}

export interface LocationInfo {
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  landmarks?: string[];
  deliveryZones: DeliveryZone[];
}

export interface DeliveryZone {
  id: string;
  name: string;
  radius: number; // in km
  deliveryFee: number;
  minimumOrder: number;
  estimatedTime: number; // in minutes
}

export interface OperatingHours {
  [key: string]: DaySchedule; // monday, tuesday, etc.
  specialHours: SpecialHour[];
  timezone: string;
  breakTimes: BreakTime[];
}

export interface DaySchedule {
  isOpen: boolean;
  openTime: string; // "09:00"
  closeTime: string; // "22:00"
  is24Hours: boolean;
}

export interface SpecialHour {
  date: Date;
  reason: string;
  schedule: DaySchedule | null; // null for closed
}

export interface BreakTime {
  startTime: string;
  endTime: string;
  days: string[]; // ['monday', 'tuesday']
  reason: string;
}

export interface BusinessInfo {
  registrationNumber: string;
  taxId: string;
  licenseNumber: string;
  ownerName: string;
  businessType: 'sole_proprietorship' | 'partnership' | 'corporation' | 'llc';
  yearEstablished: number;
  employeeCount: number;
  seatingCapacity: number;
}

export interface SocialMediaLinks {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  youtube?: string;
  tiktok?: string;
  linkedin?: string;
}

export interface RestaurantSettings {
  theme: ThemeSettings;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  features: FeatureSettings;
  language: string;
  currency: string;
}

export interface ThemeSettings {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  darkMode: boolean;
  customTheme?: string;
}

export interface NotificationSettings {
  orderAlerts: boolean;
  reviewAlerts: boolean;
  promotionAlerts: boolean;
  systemUpdates: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'limited';
  showContactInfo: boolean;
  showRatings: boolean;
  allowReviews: boolean;
  dataSharing: boolean;
}

export interface FeatureSettings {
  onlineOrdering: boolean;
  tableReservations: boolean;
  deliveryService: boolean;
  takeawayService: boolean;
  loyaltyProgram: boolean;
  giftCards: boolean;
}

export interface RestaurantAnalytics {
  overview: AnalyticsOverview;
  revenue: RevenueAnalytics;
  orders: OrderAnalytics;
  customers: CustomerAnalytics;
  performance: PerformanceMetrics;
  trends: TrendData[];
}

export interface AnalyticsOverview {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  averageOrderValue: number;
  growthRate: number;
  period: 'today' | 'week' | 'month' | 'year';
}

export interface RevenueAnalytics {
  daily: ChartDataPoint[];
  weekly: ChartDataPoint[];
  monthly: ChartDataPoint[];
  yearly: ChartDataPoint[];
  byCategory: CategoryRevenue[];
  projections: ChartDataPoint[];
}

export interface OrderAnalytics {
  totalOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  averagePreparationTime: number;
  peakHours: PeakHour[];
  orderSources: OrderSource[];
}

export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
  customerRetentionRate: number;
  averageCustomerLifetime: number;
  topCustomers: TopCustomer[];
}

export interface PerformanceMetrics {
  efficiency: number;
  customerSatisfaction: number;
  orderAccuracy: number;
  deliveryTime: number;
  responseTime: number;
  qualityScore: number;
}

export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface CategoryRevenue {
  category: string;
  revenue: number;
  percentage: number;
  growth: number;
}

export interface PeakHour {
  hour: number;
  orderCount: number;
  revenue: number;
}

export interface OrderSource {
  source: string;
  count: number;
  percentage: number;
}

export interface TopCustomer {
  id: string;
  name: string;
  totalOrders: number;
  totalSpent: number;
  lastOrder: Date;
}

export interface TrendData {
  metric: string;
  current: number;
  previous: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  email: string;
  phone: string;
  joinDate: Date;
  isActive: boolean;
  permissions: string[];
  schedule?: WorkSchedule;
}

export interface WorkSchedule {
  [key: string]: {
    startTime: string;
    endTime: string;
    isWorking: boolean;
  };
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  dateEarned: Date;
  category: 'quality' | 'service' | 'growth' | 'community';
  level: 'bronze' | 'silver' | 'gold' | 'platinum';
}

export interface Certification {
  id: string;
  name: string;
  issuingAuthority: string;
  issueDate: Date;
  expiryDate?: Date;
  certificateUrl?: string;
  verified: boolean;
}

// Animation and UI Types
export interface AnimationConfig {
  duration: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
  delay?: number;
}

export interface GestureConfig {
  enabled: boolean;
  threshold: number;
  direction: 'horizontal' | 'vertical' | 'both';
}

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}
