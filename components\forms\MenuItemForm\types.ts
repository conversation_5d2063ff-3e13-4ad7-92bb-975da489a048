// TypeScript interfaces and types for the Menu Item Management Form

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  category: string;
  subCategory?: string;
  price: PricingTier[];
  preparationTime: number;
  ingredients: Ingredient[];
  allergens: string[];
  nutritionalInfo: NutritionalInfo;
  images: MenuItemImage[];
  availability: AvailabilitySchedule;
  customizations: CustomizationGroup[];
  tags: string[];
  isActive: boolean;
  isDraft: boolean;
  version: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  seoOptimized: boolean;
  popularityScore: number;
  profitMargin: number;
}

export interface PricingTier {
  id: string;
  name: string; // Small, Medium, Large, etc.
  price: number;
  currency: string;
  taxRate: number;
  isDefault: boolean;
  description?: string;
  dynamicPricing?: DynamicPricing;
}

export interface DynamicPricing {
  timeBasedPricing: TimeBasedPrice[];
  dayBasedPricing: DayBasedPrice[];
  seasonalPricing: SeasonalPrice[];
}

export interface TimeBasedPrice {
  startTime: string;
  endTime: string;
  priceMultiplier: number;
}

export interface DayBasedPrice {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  priceMultiplier: number;
}

export interface SeasonalPrice {
  startDate: Date;
  endDate: Date;
  priceMultiplier: number;
  name: string;
}

export interface Ingredient {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  cost: number;
  supplier?: string;
  allergens: string[];
  nutritionalValue: NutritionalInfo;
  isOptional: boolean;
  category: string;
}

export interface Allergen {
  id: string;
  name: string;
  severity: 'mild' | 'moderate' | 'severe';
  icon: string;
  description: string;
}

export interface NutritionalInfo {
  calories: number;
  protein: number;
  carbohydrates: number;
  fat: number;
  fiber: number;
  sugar: number;
  sodium: number;
  cholesterol: number;
  servingSize: string;
  servingUnit?: string;
  servingsPerContainer: number;
}

export interface MenuItemImage {
  id: string;
  url: string;
  thumbnailUrl: string;
  alt: string;
  order: number;
  isPrimary: boolean;
  filters: ImageFilters;
  metadata: ImageMetadata;
}

export interface ImageFilters {
  brightness: number;
  contrast: number;
  saturation: number;
  blur: number;
}

export interface ImageMetadata {
  width: number;
  height: number;
  size: number;
  format: string;
  uploadedAt: Date;
}

export interface AvailabilitySchedule {
  isAvailable: boolean;
  schedules: WeeklySchedule[];
  specialDates: SpecialDateSchedule[];
  stockLevel?: number;
  lowStockThreshold?: number;
  isAlwaysAvailable?: boolean;
  schedule?: WeeklySchedule[];
  specialHours?: SpecialDateSchedule[];
  isSeasonalItem?: boolean;
}

export interface WeeklySchedule {
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
}

export interface SpecialDateSchedule {
  date: Date;
  isAvailable: boolean;
  specialPrice?: number;
  note?: string;
}

export interface CustomizationGroup {
  id: string;
  name: string;
  description: string;
  isRequired: boolean;
  allowMultiple: boolean;
  minSelections: number;
  maxSelections: number;
  options: CustomizationOption[];
  conditionalLogic?: ConditionalLogic[];
  displayOrder: number;
}

export interface CustomizationOption {
  id: string;
  name: string;
  description: string;
  priceAdjustment: number;
  isDefault?: boolean;
  isAvailable: boolean;
  ingredients?: string[];
  allergens?: string[];
  nutritionalImpact?: Partial<NutritionalInfo>;
  displayOrder: number;
}

export interface ConditionalLogic {
  condition: string; // JSON string representing the condition
  action: 'show' | 'hide' | 'require' | 'disable';
  targetOptionIds: string[];
}

export interface Category {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  parentId?: string;
  order: number;
  isActive: boolean;
  suggestedPriceRange: {
    min: number;
    max: number;
  };
}

export interface FormValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

export interface FormStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  isCompleted: boolean;
  isValid: boolean;
  errors: FormValidationError[];
  progress: number;
}

export interface MenuItemTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  baseData: Partial<MenuItem>;
  isPublic: boolean;
  usageCount: number;
  createdBy: string;
  tags: string[];
}

export interface AnalyticsData {
  popularityTrend: number[];
  salesForecast: number[];
  profitMargin: number;
  competitorPrices: CompetitorPrice[];
  seasonalDemand: SeasonalDemand[];
  customerPreferences: CustomerPreference[];
}

export interface CompetitorPrice {
  restaurantName: string;
  itemName: string;
  price: number;
  similarity: number;
  source: string;
}

export interface SeasonalDemand {
  month: number;
  demandMultiplier: number;
  confidence: number;
}

export interface CustomerPreference {
  preference: string;
  score: number;
  trend: 'increasing' | 'decreasing' | 'stable';
}

export interface FormState {
  currentStep: number;
  steps: FormStep[];
  data: Partial<MenuItem>;
  isDirty: boolean;
  isSubmitting: boolean;
  lastSaved: Date | null;
  validationErrors: FormValidationError[];
  analytics: AnalyticsData | null;
}

export interface FormActions {
  updateField: (field: string, value: any) => void;
  validateField: (field: string) => Promise<FormValidationError[]>;
  validateStep: (stepId: string) => Promise<boolean>;
  nextStep: () => void;
  previousStep: () => void;
  goToStep: (stepIndex: number) => void;
  saveDraft: () => Promise<void>;
  submitForm: () => Promise<void>;
  resetForm: () => void;
  loadTemplate: (templateId: string) => void;
  generateAIDescription: () => Promise<string>;
  optimizeForSEO: () => Promise<void>;
}
