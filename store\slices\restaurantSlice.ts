import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Restaurant, RestaurantState } from '../../types';

export const fetchRestaurantInfo = createAsyncThunk(
  'restaurant/fetchRestaurantInfo',
  async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock restaurant data - replace with actual API call
    const mockRestaurant: Restaurant = {
      id: 'rest1',
      name: 'Delicious Bites Restaurant',
      description: 'Authentic cuisine with fresh ingredients and exceptional taste',
      address: {
        id: 'addr1',
        street: '456 Restaurant Ave',
        city: 'New York',
        state: 'NY',
        coordinates: { latitude: 40.7589, longitude: -73.9851 }
      },
      phone: '+1234567890',
      email: '<EMAIL>',
      website: 'www.deliciousbites.com',
      operatingHours: [
        { dayOfWeek: 1, openTime: '09:00', closeTime: '22:00', isClosed: false },
        { dayOfWeek: 2, openTime: '09:00', closeTime: '22:00', isClosed: false },
        { dayOfWeek: 3, openTime: '09:00', closeTime: '22:00', isClosed: false },
        { dayOfWeek: 4, openTime: '09:00', closeTime: '22:00', isClosed: false },
        { dayOfWeek: 5, openTime: '09:00', closeTime: '23:00', isClosed: false },
        { dayOfWeek: 6, openTime: '10:00', closeTime: '23:00', isClosed: false },
        { dayOfWeek: 0, openTime: '10:00', closeTime: '21:00', isClosed: false }
      ],
      isOpen: true,
      acceptingOrders: true,
      averagePreparationTime: 25,
      minimumOrderAmount: 15.00,
      deliveryRadius: 10,
      deliveryFee: 3.99,
      totalOrders: 1250,
      rating: 4.7,
      reviewCount: 324
    };
    
    return mockRestaurant;
  }
);

export const updateRestaurantStatus = createAsyncThunk(
  'restaurant/updateRestaurantStatus',
  async ({ isOpen, acceptingOrders }: { isOpen?: boolean; acceptingOrders?: boolean }) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return { isOpen, acceptingOrders };
  }
);

const initialState: RestaurantState = {
  restaurant: null,
  loading: false,
  error: null
};

const restaurantSlice = createSlice({
  name: 'restaurant',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateRestaurantInfo: (state, action: PayloadAction<Partial<Restaurant>>) => {
      if (state.restaurant) {
        state.restaurant = { ...state.restaurant, ...action.payload };
      }
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchRestaurantInfo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRestaurantInfo.fulfilled, (state, action) => {
        state.loading = false;
        state.restaurant = action.payload;
      })
      .addCase(fetchRestaurantInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch restaurant info';
      })
      .addCase(updateRestaurantStatus.fulfilled, (state, action) => {
        if (state.restaurant) {
          const { isOpen, acceptingOrders } = action.payload;
          if (isOpen !== undefined) state.restaurant.isOpen = isOpen;
          if (acceptingOrders !== undefined) state.restaurant.acceptingOrders = acceptingOrders;
        }
      });
  }
});

export const { clearError, updateRestaurantInfo } = restaurantSlice.actions;
export default restaurantSlice.reducer;
