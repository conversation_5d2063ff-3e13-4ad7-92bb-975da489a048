/**
 * Premium Orange & Black Design System
 * Restaurant App Theme Configuration
 */

export const PremiumColors = {
  // Primary Orange Palette
  primary: '#FF6B35',      // Main orange
  primaryLight: '#FFA726', // Light orange
  primaryDark: '#E65100',  // Dark orange
  primaryGradient: ['#FF6B35', '#FFA726'], // Orange gradient
  
  // Black & Gray Palette
  black: '#1A1A1A',        // Primary black
  blackLight: '#2D2D2D',   // Secondary black
  white: '#FFFFFF',        // Pure white
  gray: '#F5F5F5',         // Light gray
  grayMedium: '#E0E0E0',   // Medium gray
  grayDark: '#666666',     // Dark gray
  
  // Status Colors
  success: '#00C851',      // Green
  warning: '#FFB800',      // Yellow
  danger: '#FF4444',       // Red
  info: '#2196F3',         // Blue
  
  // Time-Based Order Status Colors
  orderUrgent: '#FF4444',    // RED (30+ minutes): Urgent/Delayed
  orderProgress: '#FFB800',  // YELLOW (15-29 minutes): In Progress
  orderOnTime: '#00C851',    // GREEN (0-14 minutes): On Time/Fresh
  
  // Transparency Variants
  primaryAlpha: (alpha: number) => `${PremiumColors.primary}${Math.round(alpha * 255).toString(16).padStart(2, '0')}`,
  blackAlpha: (alpha: number) => `${PremiumColors.black}${Math.round(alpha * 255).toString(16).padStart(2, '0')}`,
  whiteAlpha: (alpha: number) => `${PremiumColors.white}${Math.round(alpha * 255).toString(16).padStart(2, '0')}`,
};

export const PremiumShadows = {
  small: {
    shadowColor: PremiumColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  medium: {
    shadowColor: PremiumColors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  large: {
    shadowColor: PremiumColors.black,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  orange: {
    shadowColor: PremiumColors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
};

export const PremiumSpacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const PremiumBorderRadius = {
  small: 8,
  medium: 12,
  large: 16,
  xl: 24,
  round: 50,
};

export const PremiumTypography = {
  // Font Sizes
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  
  // Font Weights
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
  extrabold: '800' as const,
};

export const PremiumComponents = {
  // Button Styles
  primaryButton: {
    backgroundColor: PremiumColors.primary,
    borderRadius: PremiumBorderRadius.medium,
    paddingVertical: PremiumSpacing.md,
    paddingHorizontal: PremiumSpacing.lg,
    ...PremiumShadows.medium,
  },
  
  secondaryButton: {
    backgroundColor: PremiumColors.white,
    borderWidth: 2,
    borderColor: PremiumColors.black,
    borderRadius: PremiumBorderRadius.medium,
    paddingVertical: PremiumSpacing.md,
    paddingHorizontal: PremiumSpacing.lg,
    ...PremiumShadows.small,
  },
  
  // Card Styles
  primaryCard: {
    backgroundColor: PremiumColors.white,
    borderRadius: PremiumBorderRadius.large,
    padding: PremiumSpacing.lg,
    ...PremiumShadows.medium,
  },
  
  featuredCard: {
    backgroundColor: PremiumColors.white,
    borderRadius: PremiumBorderRadius.large,
    borderLeftWidth: 4,
    borderLeftColor: PremiumColors.primary,
    padding: PremiumSpacing.lg,
    ...PremiumShadows.medium,
  },
  
  premiumCard: {
    backgroundColor: PremiumColors.primary,
    borderRadius: PremiumBorderRadius.large,
    padding: PremiumSpacing.lg,
    ...PremiumShadows.orange,
  },
  
  // Input Styles
  textInput: {
    backgroundColor: PremiumColors.white,
    borderWidth: 2,
    borderColor: PremiumColors.grayMedium,
    borderRadius: PremiumBorderRadius.medium,
    paddingVertical: PremiumSpacing.md,
    paddingHorizontal: PremiumSpacing.lg,
    fontSize: PremiumTypography.md,
    color: PremiumColors.black,
  },
  
  focusedInput: {
    borderColor: PremiumColors.primary,
    ...PremiumShadows.orange,
  },
};

// Utility Functions
export const getOrderStatusColor = (minutes: number) => {
  if (minutes >= 30) return PremiumColors.orderUrgent;
  if (minutes >= 15) return PremiumColors.orderProgress;
  return PremiumColors.orderOnTime;
};

export const getOrderStatusText = (minutes: number) => {
  if (minutes >= 30) return 'Urgent';
  if (minutes >= 15) return 'In Progress';
  return 'On Time';
};

// Theme Configuration
export const PremiumTheme = {
  colors: PremiumColors,
  shadows: PremiumShadows,
  spacing: PremiumSpacing,
  borderRadius: PremiumBorderRadius,
  typography: PremiumTypography,
  components: PremiumComponents,
  utils: {
    getOrderStatusColor,
    getOrderStatusText,
  },
};

export default PremiumTheme;
