import { IconSymbol } from '@/components/ui/IconSymbol';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Theme';
import { MediaItem } from '@/types/profile';
import React, { useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';

const { width: screenWidth } = Dimensions.get('window');

interface ImageCarouselProps {
  images: MediaItem[];
  height?: number;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showIndicators?: boolean;
  showControls?: boolean;
  onImagePress?: (image: MediaItem, index: number) => void;
  borderRadius?: number;
}

export const ImageCarousel: React.FC<ImageCarouselProps> = ({
  images,
  height = 200,
  autoPlay = true,
  autoPlayInterval = 3000,
  showIndicators = true,
  showControls = true,
  onImagePress,
  borderRadius = BorderRadius.md,
}) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || images.length <= 1) return;

    const interval = setInterval(() => {
      const nextIndex = (currentIndex + 1) % images.length;
      goToSlide(nextIndex);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [currentIndex, isAutoPlaying, images.length, autoPlayInterval]);

  const goToSlide = (index: number) => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: index * screenWidth,
        animated: true,
      });
      setCurrentIndex(index);
    }
  };

  const handleScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / screenWidth);
    setCurrentIndex(index);
    translateX.value = contentOffsetX;
  };

  const nextSlide = () => {
    const nextIndex = (currentIndex + 1) % images.length;
    goToSlide(nextIndex);
  };

  const prevSlide = () => {
    const prevIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;
    goToSlide(prevIndex);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying);
  };

  const handleImagePress = (image: MediaItem, index: number) => {
    if (onImagePress) {
      onImagePress(image, index);
    }
  };

  const animatedControlsStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(opacity.value, { duration: 300 }),
    };
  });

  if (!images || images.length === 0) {
    return (
      <View style={[styles.container, { height, borderRadius }]}>
        <View style={styles.emptyState}>
          <IconSymbol name="photo" size={48} color={Colors.light.textSecondary} />
          <Text style={styles.emptyText}>No images available</Text>
        </View>
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={[styles.container, { height, borderRadius }]}>
      <View style={styles.carouselContainer}>
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          onScrollBeginDrag={() => setIsAutoPlaying(false)}
          onScrollEndDrag={() => setIsAutoPlaying(autoPlay)}
          style={styles.scrollView}
        >
          {images.map((image, index) => (
            <TouchableOpacity
              key={image.id}
              style={[styles.imageContainer, { width: screenWidth }]}
              onPress={() => handleImagePress(image, index)}
              activeOpacity={0.9}
            >
              <Image
                source={{ uri: image.url }}
                style={[styles.image, { borderRadius }]}
                resizeMode="cover"
              />
              
              {/* Image Overlay with Info */}
              <View style={styles.imageOverlay}>
                <View style={styles.imageInfo}>
                  {image.title && (
                    <Text style={styles.imageTitle} numberOfLines={1}>
                      {image.title}
                    </Text>
                  )}
                  {image.description && (
                    <Text style={styles.imageDescription} numberOfLines={2}>
                      {image.description}
                    </Text>
                  )}
                </View>
                
                {image.featured && (
                  <View style={styles.featuredBadge}>
                    <IconSymbol name="star.fill" size={12} color={Colors.light.textInverse} />
                    <Text style={styles.featuredText}>Featured</Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Navigation Controls */}
        {showControls && images.length > 1 && (
          <Animated.View style={[styles.controls, animatedControlsStyle]}>
            <TouchableOpacity
              style={[styles.controlButton, styles.prevButton]}
              onPress={prevSlide}
            >
              <IconSymbol name="chevron.left" size={20} color={Colors.light.textInverse} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, styles.nextButton]}
              onPress={nextSlide}
            >
              <IconSymbol name="chevron.right" size={20} color={Colors.light.textInverse} />
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Auto-play Control */}
        {autoPlay && images.length > 1 && (
          <TouchableOpacity
            style={styles.autoPlayButton}
            onPress={toggleAutoPlay}
          >
            <IconSymbol
              name={isAutoPlaying ? "pause.fill" : "play.fill"}
              size={16}
              color={Colors.light.textInverse}
            />
          </TouchableOpacity>
        )}

        {/* Page Indicators */}
        {showIndicators && images.length > 1 && (
          <View style={styles.indicators}>
            {images.map((_, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.indicator,
                  {
                    backgroundColor: index === currentIndex
                      ? Colors.light.textInverse
                      : 'rgba(255, 255, 255, 0.5)',
                  },
                ]}
                onPress={() => goToSlide(index)}
              />
            ))}
          </View>
        )}

        {/* Image Counter */}
        <View style={styles.counter}>
          <Text style={styles.counterText}>
            {currentIndex + 1} / {images.length}
          </Text>
        </View>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    overflow: 'hidden',
  },
  carouselContainer: {
    flex: 1,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.5)', // Replaced linear-gradient with solid color
    padding: Spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  imageInfo: {
    flex: 1,
    marginRight: Spacing.sm,
  },
  imageTitle: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.light.textInverse,
    marginBottom: Spacing.xs,
  },
  imageDescription: {
    fontSize: Typography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.sm,
  },
  featuredBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  featuredText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.light.textInverse,
    marginLeft: Spacing.xs,
  },
  controls: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    transform: [{ translateY: -20 }],
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  prevButton: {},
  nextButton: {},
  autoPlayButton: {
    position: 'absolute',
    top: Spacing.md,
    right: Spacing.md,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  indicators: {
    position: 'absolute',
    bottom: Spacing.md,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  counter: {
    position: 'absolute',
    top: Spacing.md,
    left: Spacing.md,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  counterText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.light.textInverse,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
  },
  emptyText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.light.textSecondary,
    marginTop: Spacing.sm,
  },
});
