import { MaterialCommunityIcons } from '@expo/vector-icons';
import React, { memo } from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';

// Optimized icon sizes for consistent performance
export const IconSizes = {
  xs: 12,
  sm: 16,
  md: 24,
  lg: 32,
  xl: 48,
  xxl: 64,
} as const;

// Restaurant-themed icon mappings for better performance
export const RestaurantIcons = {
  // Authentication
  login: 'login',
  logout: 'logout',
  register: 'account-plus',
  
  // Menu Management
  menu: 'food',
  addItem: 'plus-circle',
  editItem: 'pencil',
  deleteItem: 'delete',
  category: 'tag',
  
  // Orders
  orders: 'clipboard-list',
  newOrder: 'bell-plus',
  orderReady: 'check-circle',
  orderDelay: 'clock-alert',
  
  // Dashboard
  dashboard: 'view-dashboard',
  analytics: 'chart-line',
  revenue: 'cash',
  customers: 'account-group',
  
  // Restaurant
  restaurant: 'store',
  location: 'map-marker',
  phone: 'phone',
  email: 'email',
  
  // Settings
  settings: 'cog',
  profile: 'account',
  notifications: 'bell',
  theme: 'palette',
  
  // Actions
  save: 'content-save',
  cancel: 'close',
  edit: 'pencil',
  delete: 'delete',
  search: 'magnify',
  filter: 'filter',
  
  // Status
  success: 'check-circle',
  error: 'alert-circle',
  warning: 'alert',
  info: 'information',
  
  // Navigation
  back: 'arrow-left',
  forward: 'arrow-right',
  up: 'arrow-up',
  down: 'arrow-down',
  
  // Food Categories
  fastFood: 'hamburger',
  cafe: 'coffee',
  bbq: 'grill',
  bakery: 'cupcake',
  seafood: 'fish',
  pizza: 'pizza',
  pasta: 'pasta',
  chinese: 'noodles',
  indian: 'chili-hot',
  
} as const;

interface OptimizedIconProps {
  name: keyof typeof RestaurantIcons | string;
  size?: keyof typeof IconSizes | number;
  color?: string;
  style?: ViewStyle;
  backgroundColor?: string;
  borderRadius?: number;
  padding?: number;
}

const OptimizedIcon: React.FC<OptimizedIconProps> = memo(({
  name,
  size = 'md',
  color = '#1A1A1A',
  style,
  backgroundColor,
  borderRadius,
  padding
}) => {
  // Get icon name from mapping or use as-is
  const iconName = RestaurantIcons[name as keyof typeof RestaurantIcons] || name;
  
  // Get size value
  const iconSize = typeof size === 'number' ? size : IconSizes[size];
  
  // Container style for background and padding
  const containerStyle = [
    backgroundColor && {
      backgroundColor,
      borderRadius: borderRadius || iconSize / 2,
      padding: padding || iconSize * 0.2,
    },
    style
  ];

  if (backgroundColor || padding) {
    return (
      <View style={containerStyle}>
        <MaterialCommunityIcons
          name={iconName as any}
          size={iconSize}
          color={color}
        />
      </View>
    );
  }

  return (
    <MaterialCommunityIcons
      name={iconName as any}
      size={iconSize}
      color={color}
      style={style}
    />
  );
});

OptimizedIcon.displayName = 'OptimizedIcon';

export default OptimizedIcon;

// Icon preloader for better performance
export class IconPreloader {
  private static preloadedIcons = new Set<string>();

  static preloadIcon(iconName: string): void {
    if (!this.preloadedIcons.has(iconName)) {
      // Icons are vector-based, so no actual preloading needed
      // But we can track which icons are used for analytics
      this.preloadedIcons.add(iconName);
    }
  }

  static preloadRestaurantIcons(): void {
    Object.values(RestaurantIcons).forEach(iconName => {
      this.preloadIcon(iconName);
    });
  }

  static getPreloadedCount(): number {
    return this.preloadedIcons.size;
  }
}

// Icon theme provider for consistent styling
export const IconTheme = {
  primary: '#FF6B35',
  secondary: '#1A1A1A',
  success: '#00C851',
  warning: '#FFB800',
  danger: '#FF4444',
  light: '#FFFFFF',
  dark: '#1A1A1A',
  gray: '#666666',
};

// Utility function for themed icons
export const ThemedIcon: React.FC<OptimizedIconProps & { theme?: keyof typeof IconTheme }> = memo(({
  theme = 'secondary',
  ...props
}) => {
  return (
    <OptimizedIcon
      {...props}
      color={props.color || IconTheme[theme]}
    />
  );
});

ThemedIcon.displayName = 'ThemedIcon';

// Icon button component for better touch targets
export const IconButton: React.FC<OptimizedIconProps & {
  onPress?: () => void;
  disabled?: boolean;
  hitSlop?: number;
}> = memo(({
  onPress,
  disabled = false,
  hitSlop = 8,
  ...iconProps
}) => {
  const buttonStyle = [
    styles.iconButton,
    disabled && styles.disabled,
    iconProps.style
  ];

  return (
    <View
      style={buttonStyle}
      onTouchEnd={disabled ? undefined : onPress}
      hitSlop={{ top: hitSlop, bottom: hitSlop, left: hitSlop, right: hitSlop }}
    >
      <OptimizedIcon
        {...iconProps}
        color={disabled ? IconTheme.gray : iconProps.color}
      />
    </View>
  );
});

IconButton.displayName = 'IconButton';

const styles = StyleSheet.create({
  iconButton: {
    padding: 8,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
});
