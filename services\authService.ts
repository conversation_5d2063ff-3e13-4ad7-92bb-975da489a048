import api, { ApiResponse, handleApiError } from './api';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Types for authentication
export interface LoginRequest {
  username: string;
  password: string;
  role: 'owner' | 'admin' | 'staff';
}

export interface RegisterRequest {
  restaurantName: string;
  ownerName: string;
  email: string;
  phone: string;
  address: string;
  username: string;
  password: string;
  restaurantType: string[];
  documents: {
    businessLicense?: string;
    foodLicense?: string;
    taxCertificate?: string;
  };
}

export interface User {
  id: string;
  username: string;
  role: 'owner' | 'admin' | 'staff';
  email?: string;
  name?: string;
  restaurantId?: string;
  verificationStatus: 'pending' | 'verified' | 'rejected';
  isDemo?: boolean;
  createdAt: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  restaurant?: {
    id: string;
    name: string;
    verificationStatus: 'pending' | 'verified' | 'rejected';
    documents: {
      businessLicense?: { url: string; status: string; };
      foodLicense?: { url: string; status: string; };
      taxCertificate?: { url: string; status: string; };
    };
  };
}

class AuthService {
  // Register new restaurant owner
  async register(data: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await api.post<ApiResponse<AuthResponse>>('/auth/register', data);
      
      if (response.data.success && response.data.data.token) {
        // Store auth data
        await AsyncStorage.setItem('authToken', response.data.data.token);
        await AsyncStorage.setItem('userData', JSON.stringify(response.data.data.user));
        
        if (response.data.data.restaurant) {
          await AsyncStorage.setItem('restaurantData', JSON.stringify(response.data.data.restaurant));
        }
      }
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Login user
  async login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await api.post<ApiResponse<AuthResponse>>('/auth/login', data);
      
      if (response.data.success && response.data.data.token) {
        // Store auth data
        await AsyncStorage.setItem('authToken', response.data.data.token);
        await AsyncStorage.setItem('userData', JSON.stringify(response.data.data.user));
        
        if (response.data.data.restaurant) {
          await AsyncStorage.setItem('restaurantData', JSON.stringify(response.data.data.restaurant));
        }
      }
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Check verification status
  async checkVerificationStatus(): Promise<ApiResponse<{ verificationStatus: string; restaurant: any }>> {
    try {
      const response = await api.get<ApiResponse<{ verificationStatus: string; restaurant: any }>>('/auth/verification-status');
      
      if (response.data.success && response.data.data.restaurant) {
        // Update stored restaurant data
        await AsyncStorage.setItem('restaurantData', JSON.stringify(response.data.data.restaurant));
      }
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // Clear local storage regardless of API response
      await AsyncStorage.multiRemove(['authToken', 'userData', 'restaurantData']);
    }
  }

  // Get stored user data
  async getStoredUser(): Promise<User | null> {
    try {
      const userData = await AsyncStorage.getItem('userData');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting stored user:', error);
      return null;
    }
  }

  // Get stored restaurant data
  async getStoredRestaurant(): Promise<any | null> {
    try {
      const restaurantData = await AsyncStorage.getItem('restaurantData');
      return restaurantData ? JSON.parse(restaurantData) : null;
    } catch (error) {
      console.error('Error getting stored restaurant:', error);
      return null;
    }
  }

  // Check if user can access restaurant (verification check)
  async canAccessRestaurant(): Promise<{ canAccess: boolean; reason?: string }> {
    try {
      const user = await this.getStoredUser();
      const restaurant = await this.getStoredRestaurant();

      // Demo accounts can always access
      if (user?.isDemo) {
        return { canAccess: true };
      }

      // Check if user exists
      if (!user) {
        return { canAccess: false, reason: 'User not found' };
      }

      // Check if restaurant exists (for owners)
      if (user.role === 'owner' && !restaurant) {
        return { canAccess: false, reason: 'Restaurant not found' };
      }

      // Check verification status for owners
      if (user.role === 'owner') {
        if (restaurant.verificationStatus === 'pending') {
          return { 
            canAccess: false, 
            reason: 'Your restaurant documents are still under verification. Please wait for approval.' 
          };
        }
        
        if (restaurant.verificationStatus === 'rejected') {
          return { 
            canAccess: false, 
            reason: 'Your restaurant verification was rejected. Please contact support or resubmit documents.' 
          };
        }
      }

      // Admin and staff can access if their account is verified
      if (user.role === 'admin' || user.role === 'staff') {
        if (user.verificationStatus === 'pending') {
          return { 
            canAccess: false, 
            reason: 'Your account is still under verification. Please wait for approval.' 
          };
        }
        
        if (user.verificationStatus === 'rejected') {
          return { 
            canAccess: false, 
            reason: 'Your account verification was rejected. Please contact the restaurant owner.' 
          };
        }
      }

      return { canAccess: true };
    } catch (error) {
      console.error('Error checking restaurant access:', error);
      return { canAccess: false, reason: 'Error checking verification status' };
    }
  }
}

export default new AuthService();
