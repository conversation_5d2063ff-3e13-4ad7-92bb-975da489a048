import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { PremiumColors } from '@/constants/PremiumTheme';
import { GeneratedUser } from '@/types/auth';
import * as Clipboard from 'expo-clipboard';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useState } from 'react';
import {
    Alert,
    Dimensions,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

const { width } = Dimensions.get('window');

export default function StaffManagementScreen() {
  return (
    <ProtectedRoute requiredRole="owner">
      <StaffManagementContent />
    </ProtectedRoute>
  );
}

function StaffManagementContent() {
  const [generatedUsers, setGeneratedUsers] = useState<GeneratedUser[]>([]);
  const [selectedRole, setSelectedRole] = useState<'admin' | 'staff'>('staff');

  const generateRandomPassword = (): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  };

  const generateUserId = (): string => {
    return Math.floor(1000 + Math.random() * 9000).toString();
  };

  const generateAccount = () => {
    const userId = generateUserId();
    const username = `${selectedRole}${userId}`;
    const password = generateRandomPassword();
    const currentDate = new Date().toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    });

    const newUser: GeneratedUser = {
      id: `user_${userId}`,
      username,
      password,
      role: selectedRole,
      createdAt: currentDate
    };

    setGeneratedUsers(prev => [newUser, ...prev]);

    Alert.alert(
      'Account Generated',
      `New ${selectedRole} account created successfully!`,
      [{ text: 'OK' }]
    );
  };

  const copyToClipboard = async (text: string, type: string) => {
    await Clipboard.setStringAsync(text);
    Alert.alert('Copied', `${type} copied to clipboard!`);
  };

  const deleteUser = (userId: string) => {
    Alert.alert(
      'Delete User',
      'Are you sure you want to delete this user account?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setGeneratedUsers(prev => prev.filter(user => user.id !== userId));
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={PremiumColors.primary} translucent={false} />

      {/* Enhanced Header with Navigation */}
      <LinearGradient
        colors={[PremiumColors.primary, PremiumColors.secondary]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol name="chevron.left" size={24} color="#FFFFFF" />
          </TouchableOpacity>

          <View style={styles.headerCenter}>
            <View style={styles.headerIconContainer}>
              <IconSymbol name="person.3.fill" size={32} color="#FFFFFF" />
            </View>
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>Staff Management</Text>
              <Text style={styles.headerSubtitle}>Generate and manage team accounts</Text>
            </View>
          </View>

          <View style={styles.headerRight}>
            <View style={styles.statsContainer}>
              <Text style={styles.statsNumber}>{generatedUsers.length}</Text>
              <Text style={styles.statsLabel}>Accounts</Text>
            </View>
          </View>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Enhanced Account Generator */}
        <View style={styles.generatorCard}>
          <View style={styles.cardHeader}>
            <View style={styles.cardHeaderIcon}>
              <IconSymbol name="plus-circle" size={28} color={PremiumColors.primary} />
            </View>
            <View style={styles.cardHeaderText}>
              <Text style={styles.sectionTitle}>Generate New Account</Text>
              <Text style={styles.sectionSubtitle}>Create staff or admin accounts with secure credentials</Text>
            </View>
          </View>

          {/* Enhanced Role Selection */}
          <View style={styles.roleSelection}>
            <Text style={styles.label}>Select Account Type</Text>
            <View style={styles.roleButtons}>
              <TouchableOpacity
                style={[
                  styles.roleButton,
                  selectedRole === 'staff' && styles.roleButtonActive
                ]}
                onPress={() => setSelectedRole('staff')}
              >
                <View style={styles.roleButtonContent}>
                  <IconSymbol
                    name="account"
                    size={28}
                    color={selectedRole === 'staff' ? '#FFFFFF' : Colors.light.textSecondary}
                  />
                  <Text style={[
                    styles.roleButtonText,
                    selectedRole === 'staff' && styles.roleButtonTextActive
                  ]}>
                    Staff Member
                  </Text>
                  <Text style={[
                    styles.roleButtonDesc,
                    selectedRole === 'staff' && styles.roleButtonDescActive
                  ]}>
                    Basic access to orders & menu
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.roleButton,
                  selectedRole === 'admin' && styles.roleButtonActive
                ]}
                onPress={() => setSelectedRole('admin')}
              >
                <View style={styles.roleButtonContent}>
                  <IconSymbol
                    name="shield-account"
                    size={28}
                    color={selectedRole === 'admin' ? '#FFFFFF' : Colors.light.textSecondary}
                  />
                  <Text style={[
                    styles.roleButtonText,
                    selectedRole === 'admin' && styles.roleButtonTextActive
                  ]}>
                    Administrator
                  </Text>
                  <Text style={[
                    styles.roleButtonDesc,
                    selectedRole === 'admin' && styles.roleButtonDescActive
                  ]}>
                    Full access to all features
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* Enhanced Generate Button */}
          <TouchableOpacity style={styles.generateButton} onPress={generateAccount}>
            <LinearGradient
              colors={[PremiumColors.primary, PremiumColors.secondary]}
              style={styles.generateButtonGradient}
            >
              <IconSymbol name="plus" size={20} color="#FFFFFF" />
              <Text style={styles.generateButtonText}>Generate {selectedRole} Account</Text>
              <IconSymbol name="arrow-right" size={16} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Enhanced Generated Users List */}
        <View style={styles.usersSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionHeaderIcon}>
              <IconSymbol name="account-group" size={24} color={PremiumColors.primary} />
            </View>
            <View>
              <Text style={styles.sectionTitle}>Team Accounts</Text>
              <Text style={styles.sectionSubtitle}>{generatedUsers.length} accounts created</Text>
            </View>
          </View>

          {generatedUsers.length === 0 ? (
            <View style={styles.emptyState}>
              <View style={styles.emptyStateIcon}>
                <IconSymbol name="account-plus" size={64} color={Colors.light.textTertiary} />
              </View>
              <Text style={styles.emptyStateText}>No team accounts yet</Text>
              <Text style={styles.emptyStateSubtext}>
                Generate your first staff or admin account using the form above
              </Text>
              <View style={styles.emptyStateFeatures}>
                <View style={styles.featureItem}>
                  <IconSymbol name="check-circle" size={16} color={PremiumColors.primary} />
                  <Text style={styles.featureText}>Secure auto-generated passwords</Text>
                </View>
                <View style={styles.featureItem}>
                  <IconSymbol name="check-circle" size={16} color={PremiumColors.primary} />
                  <Text style={styles.featureText}>Role-based access control</Text>
                </View>
                <View style={styles.featureItem}>
                  <IconSymbol name="check-circle" size={16} color={PremiumColors.primary} />
                  <Text style={styles.featureText}>Easy credential sharing</Text>
                </View>
              </View>
            </View>
          ) : (
            generatedUsers.map((user) => (
              <View key={user.id} style={styles.userCard}>
                <LinearGradient
                  colors={['rgba(255, 255, 255, 0.9)', 'rgba(255, 255, 255, 0.7)']}
                  style={styles.userCardGradient}
                >
                  <View style={styles.userHeader}>
                    <View style={styles.userAvatar}>
                      <IconSymbol
                        name={user.role === 'admin' ? 'shield-account' : 'account'}
                        size={32}
                        color={user.role === 'admin' ? '#EF4444' : '#DC143C'}
                      />
                    </View>

                    <View style={styles.userInfo}>
                      <View style={styles.userTitleRow}>
                        <Text style={styles.username}>{user.username}</Text>
                        <LinearGradient
                          colors={user.role === 'admin' ? ['#EF4444', '#DC143C'] : ['#DC143C', '#B91C1C']}
                          style={styles.roleBadge}
                        >
                          <Text style={styles.roleBadgeText}>{user.role.toUpperCase()}</Text>
                        </LinearGradient>
                      </View>
                      <Text style={styles.userDate}>Created {user.createdAt}</Text>
                      <Text style={styles.userPermissions}>
                        {user.role === 'admin' ? 'Full system access' : 'Orders & menu access'}
                      </Text>
                    </View>

                    <TouchableOpacity
                      style={styles.deleteButton}
                      onPress={() => deleteUser(user.id)}
                    >
                      <IconSymbol name="trash" size={18} color="#B91C1C" />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.credentialsSection}>
                    <Text style={styles.credentialsTitle}>Login Credentials</Text>

                    <View style={styles.credentialRow}>
                      <View style={styles.credentialInfo}>
                        <Text style={styles.credentialLabel}>Username</Text>
                        <Text style={styles.credentialValue}>{user.username}</Text>
                      </View>
                      <TouchableOpacity
                        style={styles.copyButton}
                        onPress={() => copyToClipboard(user.username, 'Username')}
                      >
                        <IconSymbol name="content-copy" size={16} color={PremiumColors.primary} />
                      </TouchableOpacity>
                    </View>

                    <View style={styles.credentialRow}>
                      <View style={styles.credentialInfo}>
                        <Text style={styles.credentialLabel}>Password</Text>
                        <Text style={styles.credentialValue}>{user.password}</Text>
                      </View>
                      <TouchableOpacity
                        style={styles.copyButton}
                        onPress={() => copyToClipboard(user.password, 'Password')}
                      >
                        <IconSymbol name="content-copy" size={16} color={PremiumColors.primary} />
                      </TouchableOpacity>
                    </View>

                    <View style={styles.credentialActions}>
                      <TouchableOpacity
                        style={styles.shareButton}
                        onPress={() => copyToClipboard(`Username: ${user.username}\nPassword: ${user.password}`, 'Credentials')}
                      >
                        <IconSymbol name="share" size={16} color="#FFFFFF" />
                        <Text style={styles.shareButtonText}>Share Credentials</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </LinearGradient>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingTop: 40,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  headerIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  headerRight: {
    alignItems: 'center',
  },
  statsContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  statsNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  statsLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  generatorCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  cardHeaderIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: `${PremiumColors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  cardHeaderText: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  roleSelection: {
    marginBottom: 32,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  roleButtons: {
    gap: 16,
  },
  roleButton: {
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
  },
  roleButtonActive: {
    borderColor: PremiumColors.primary,
    backgroundColor: PremiumColors.primary,
  },
  roleButtonContent: {
    padding: 20,
    alignItems: 'center',
    minHeight: 120,
    justifyContent: 'center',
  },
  roleButtonGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.9,
  },
  roleButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.text,
    marginTop: 12,
    marginBottom: 4,
  },
  roleButtonTextActive: {
    color: '#FFFFFF',
  },
  roleButtonDesc: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  roleButtonDescActive: {
    color: 'rgba(255, 255, 255, 0.9)',
  },
  generateButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginTop: 8,
  },
  generateButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 24,
    gap: 12,
  },
  generateButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  usersSection: {
    flex: 1,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionHeaderIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${PremiumColors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  emptyState: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  emptyStateIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F5F5F5',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyStateFeatures: {
    gap: 12,
    alignSelf: 'stretch',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  userCard: {
    borderRadius: 20,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  userCardGradient: {
    padding: 20,
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  userAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#F5F5F5',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  userInfo: {
    flex: 1,
  },
  userTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  username: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
  },
  roleBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  roleBadgeText: {
    fontSize: 11,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  userDate: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  userPermissions: {
    fontSize: 12,
    color: Colors.light.textTertiary,
  },
  deleteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  credentialsSection: {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: 16,
    padding: 16,
  },
  credentialsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  credentialRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  credentialInfo: {
    flex: 1,
  },
  credentialLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  credentialValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  copyButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${PremiumColors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
  },
  credentialActions: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)',
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: PremiumColors.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  shareButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
