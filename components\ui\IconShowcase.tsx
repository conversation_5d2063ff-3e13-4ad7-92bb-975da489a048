import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, TextInput } from 'react-native';
import { AppIcon, RestaurantIcons, IconSizes, IconColors } from './AppIcon';
import { PremiumColors } from '../../constants/PremiumTheme';

// Icon showcase component for testing and documentation
export const IconShowcase: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<keyof typeof RestaurantIcons>('auth');
  const [selectedSize, setSelectedSize] = useState<keyof typeof IconSizes>('md');

  // Filter icons based on search query
  const getFilteredIcons = () => {
    const categoryIcons = RestaurantIcons[selectedCategory];
    if (!searchQuery) return categoryIcons;
    
    return Object.fromEntries(
      Object.entries(categoryIcons).filter(([key]) =>
        key.toLowerCase().includes(searchQuery.toLowerCase())
      )
    );
  };

  const categories = Object.keys(RestaurantIcons) as Array<keyof typeof RestaurantIcons>;
  const sizes = Object.keys(IconSizes) as Array<keyof typeof IconSizes>;
  const colors = Object.keys(IconColors) as Array<keyof typeof IconColors>;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Restaurant Icon System</Text>
        <Text style={styles.subtitle}>Comprehensive icon showcase and testing</Text>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <AppIcon name="magnify" size="sm" color="gray" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search icons..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Category Selector */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Categories</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryButton,
                selectedCategory === category && styles.categoryButtonActive
              ]}
              onPress={() => setSelectedCategory(category)}
            >
              <Text style={[
                styles.categoryText,
                selectedCategory === category && styles.categoryTextActive
              ]}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Size Selector */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Size: {selectedSize} ({IconSizes[selectedSize]}px)</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.sizeScroll}>
          {sizes.map((size) => (
            <TouchableOpacity
              key={size}
              style={[
                styles.sizeButton,
                selectedSize === size && styles.sizeButtonActive
              ]}
              onPress={() => setSelectedSize(size)}
            >
              <Text style={[
                styles.sizeText,
                selectedSize === size && styles.sizeTextActive
              ]}>
                {size}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Color Palette */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Color Palette</Text>
        <View style={styles.colorGrid}>
          {colors.map((color) => (
            <View key={color} style={styles.colorItem}>
              <View style={[styles.colorSwatch, { backgroundColor: IconColors[color] }]} />
              <Text style={styles.colorName}>{color}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Icon Grid */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>
          {selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} Icons
        </Text>
        <View style={styles.iconGrid}>
          {Object.entries(getFilteredIcons()).map(([key, iconName]) => (
            <View key={key} style={styles.iconItem}>
              <View style={styles.iconContainer}>
                <AppIcon
                  name={iconName}
                  size={selectedSize}
                  color="primary"
                />
              </View>
              <Text style={styles.iconName}>{key}</Text>
              <Text style={styles.iconCode}>{iconName}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Usage Examples */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Usage Examples</Text>
        
        {/* Basic Usage */}
        <View style={styles.exampleCard}>
          <Text style={styles.exampleTitle}>Basic Usage</Text>
          <View style={styles.exampleRow}>
            <AppIcon name="store" size="lg" color="primary" />
            <Text style={styles.exampleText}>
              {'<AppIcon name="store" size="lg" color="primary" />'}
            </Text>
          </View>
        </View>

        {/* Restaurant Icons */}
        <View style={styles.exampleCard}>
          <Text style={styles.exampleTitle}>Restaurant Icons</Text>
          <View style={styles.exampleRow}>
            <AppIcon name={RestaurantIcons.auth.restaurant} size="lg" color="primary" />
            <Text style={styles.exampleText}>
              {'<AppIcon name={RestaurantIcons.auth.restaurant} size="lg" color="primary" />'}
            </Text>
          </View>
        </View>

        {/* Different Sizes */}
        <View style={styles.exampleCard}>
          <Text style={styles.exampleTitle}>Size Variations</Text>
          <View style={styles.exampleRow}>
            <AppIcon name="star" size="xs" color="warning" />
            <AppIcon name="star" size="sm" color="warning" />
            <AppIcon name="star" size="md" color="warning" />
            <AppIcon name="star" size="lg" color="warning" />
            <AppIcon name="star" size="xl" color="warning" />
          </View>
        </View>

        {/* Color Variations */}
        <View style={styles.exampleCard}>
          <Text style={styles.exampleTitle}>Color Variations</Text>
          <View style={styles.exampleRow}>
            <AppIcon name="heart" size="lg" color="primary" />
            <AppIcon name="heart" size="lg" color="success" />
            <AppIcon name="heart" size="lg" color="warning" />
            <AppIcon name="heart" size="lg" color="danger" />
          </View>
        </View>
      </View>

      {/* Performance Info */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Performance Features</Text>
        <View style={styles.featureList}>
          <View style={styles.featureItem}>
            <AppIcon name="check-circle" size="sm" color="success" />
            <Text style={styles.featureText}>Vector icons for crisp rendering</Text>
          </View>
          <View style={styles.featureItem}>
            <AppIcon name="check-circle" size="sm" color="success" />
            <Text style={styles.featureText}>Consistent sizing system</Text>
          </View>
          <View style={styles.featureItem}>
            <AppIcon name="check-circle" size="sm" color="success" />
            <Text style={styles.featureText}>Theme-integrated colors</Text>
          </View>
          <View style={styles.featureItem}>
            <AppIcon name="check-circle" size="sm" color="success" />
            <Text style={styles.featureText}>Restaurant-specific mappings</Text>
          </View>
          <View style={styles.featureItem}>
            <AppIcon name="check-circle" size="sm" color="success" />
            <Text style={styles.featureText}>MaterialCommunityIcons library</Text>
          </View>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Total Icons: {Object.values(RestaurantIcons).reduce((acc, category) => acc + Object.keys(category).length, 0)}
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PremiumColors.white,
  },
  header: {
    padding: 20,
    backgroundColor: PremiumColors.primary,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: PremiumColors.white,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: PremiumColors.white,
    opacity: 0.9,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: PremiumColors.grayLight,
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: PremiumColors.black,
  },
  sectionContainer: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: PremiumColors.black,
    marginBottom: 12,
  },
  categoryScroll: {
    marginBottom: 8,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    backgroundColor: PremiumColors.grayLight,
    borderRadius: 20,
  },
  categoryButtonActive: {
    backgroundColor: PremiumColors.primary,
  },
  categoryText: {
    fontSize: 14,
    color: PremiumColors.black,
  },
  categoryTextActive: {
    color: PremiumColors.white,
  },
  sizeScroll: {
    marginBottom: 8,
  },
  sizeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    backgroundColor: PremiumColors.grayLight,
    borderRadius: 16,
  },
  sizeButtonActive: {
    backgroundColor: PremiumColors.primary,
  },
  sizeText: {
    fontSize: 12,
    color: PremiumColors.black,
  },
  sizeTextActive: {
    color: PremiumColors.white,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorItem: {
    alignItems: 'center',
    marginBottom: 8,
  },
  colorSwatch: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginBottom: 4,
    borderWidth: 1,
    borderColor: PremiumColors.grayLight,
  },
  colorName: {
    fontSize: 10,
    color: PremiumColors.grayDark,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  iconItem: {
    alignItems: 'center',
    width: 80,
    marginBottom: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: PremiumColors.grayLight,
    borderRadius: 8,
    marginBottom: 4,
  },
  iconName: {
    fontSize: 10,
    fontWeight: '500',
    color: PremiumColors.black,
    textAlign: 'center',
  },
  iconCode: {
    fontSize: 8,
    color: PremiumColors.grayDark,
    textAlign: 'center',
  },
  exampleCard: {
    backgroundColor: PremiumColors.grayLight,
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  exampleTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: PremiumColors.black,
    marginBottom: 8,
  },
  exampleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  exampleText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: PremiumColors.grayDark,
    flex: 1,
  },
  featureList: {
    gap: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 14,
    color: PremiumColors.black,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: PremiumColors.grayLight,
    marginTop: 20,
  },
  footerText: {
    fontSize: 12,
    color: PremiumColors.grayDark,
  },
});

export default IconShowcase;
