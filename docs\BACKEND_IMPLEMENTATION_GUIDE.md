# Backend Implementation Guide

## 🎯 Quick Start Checklist

### Phase 1: Setup & Authentication (Week 1)
- [ ] Setup database with provided schema
- [ ] Implement JWT authentication
- [ ] Create user registration endpoint
- [ ] Create login endpoint
- [ ] Implement verification system
- [ ] Setup file upload for documents

### Phase 2: Core Features (Week 2)
- [ ] Restaurant management endpoints
- [ ] Menu management (categories & items)
- [ ] Basic order processing
- [ ] Image upload for menu items

### Phase 3: Advanced Features (Week 3)
- [ ] Staff management system
- [ ] Analytics endpoints
- [ ] Notification system
- [ ] Real-time order updates

### Phase 4: Production Ready (Week 4)
- [ ] Error handling & validation
- [ ] Rate limiting
- [ ] Performance optimization
- [ ] Documentation & testing

## 🗄️ Database Setup Scripts

### PostgreSQL Setup
```sql
-- Create database
CREATE DATABASE restaurant_app;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create ENUM types
CREATE TYPE user_role AS ENUM ('owner', 'admin', 'staff');
CREATE TYPE verification_status AS ENUM ('pending', 'verified', 'rejected');
CREATE TYPE restaurant_status AS ENUM ('active', 'inactive', 'suspended');
CREATE TYPE document_type AS ENUM ('business_license', 'food_license', 'tax_certificate');
CREATE TYPE document_status AS ENUM ('pending', 'approved', 'rejected');
CREATE TYPE customization_type AS ENUM ('single', 'multiple');
CREATE TYPE spice_level AS ENUM ('mild', 'medium', 'hot', 'extra-hot');
CREATE TYPE order_status AS ENUM ('pending', 'confirmed', 'preparing', 'ready', 'out-for-delivery', 'delivered', 'cancelled', 'refunded');
CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'failed', 'refunded');
CREATE TYPE payment_method AS ENUM ('cash', 'card', 'online');
CREATE TYPE order_type AS ENUM ('delivery', 'pickup', 'dine-in');
CREATE TYPE notification_priority AS ENUM ('low', 'medium', 'high', 'urgent');
CREATE TYPE platform_type AS ENUM ('ios', 'android');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role user_role NOT NULL,
    verification_status verification_status DEFAULT 'pending',
    is_demo BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    restaurant_id UUID,
    created_by UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    
    CONSTRAINT fk_users_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id),
    CONSTRAINT fk_users_created_by FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Restaurants table
CREATE TABLE restaurants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT NOT NULL,
    city VARCHAR(100),
    state VARCHAR(100),
    zip_code VARCHAR(20),
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    type JSONB NOT NULL,
    logo_url VARCHAR(500),
    cover_image_url VARCHAR(500),
    gallery_images JSONB,
    verification_status verification_status DEFAULT 'pending',
    is_open BOOLEAN DEFAULT false,
    status restaurant_status DEFAULT 'active',
    owner_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_restaurants_owner FOREIGN KEY (owner_id) REFERENCES users(id)
);

-- Add foreign key constraint after restaurants table is created
ALTER TABLE users ADD CONSTRAINT fk_users_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id);

-- Restaurant settings table
CREATE TABLE restaurant_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    restaurant_id UUID NOT NULL,
    is_open BOOLEAN DEFAULT false,
    opening_hours JSONB NOT NULL,
    delivery_radius DECIMAL(5,2) DEFAULT 5.00,
    minimum_order_amount DECIMAL(10,2) DEFAULT 0.00,
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    estimated_delivery_time INTEGER DEFAULT 30,
    accepting_orders BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_restaurant_settings_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id)
);

-- Documents table
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    restaurant_id UUID NOT NULL,
    type document_type NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    status document_status DEFAULT 'pending',
    rejection_reason TEXT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP,
    reviewed_by UUID,
    
    CONSTRAINT fk_documents_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id),
    CONSTRAINT fk_documents_reviewed_by FOREIGN KEY (reviewed_by) REFERENCES users(id)
);

-- Menu categories table
CREATE TABLE menu_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    restaurant_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_menu_categories_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id)
);

-- Menu items table
CREATE TABLE menu_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    restaurant_id UUID NOT NULL,
    category_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    images JSONB,
    is_available BOOLEAN DEFAULT true,
    is_vegetarian BOOLEAN DEFAULT false,
    is_vegan BOOLEAN DEFAULT false,
    is_gluten_free BOOLEAN DEFAULT false,
    spice_level spice_level DEFAULT 'mild',
    preparation_time INTEGER DEFAULT 15,
    calories INTEGER,
    ingredients JSONB,
    allergens JSONB,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_menu_items_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id),
    CONSTRAINT fk_menu_items_category FOREIGN KEY (category_id) REFERENCES menu_categories(id)
);

-- Menu item customizations table
CREATE TABLE menu_item_customizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    menu_item_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    type customization_type NOT NULL,
    required BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_customizations_menu_item FOREIGN KEY (menu_item_id) REFERENCES menu_items(id) ON DELETE CASCADE
);

-- Customization options table
CREATE TABLE customization_options (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customization_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) DEFAULT 0.00,
    is_default BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_options_customization FOREIGN KEY (customization_id) REFERENCES menu_item_customizations(id) ON DELETE CASCADE
);

-- Orders table
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    restaurant_id UUID NOT NULL,
    customer_id UUID,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    customer_email VARCHAR(255),
    subtotal DECIMAL(10,2) NOT NULL,
    tax DECIMAL(10,2) DEFAULT 0.00,
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    discount DECIMAL(10,2) DEFAULT 0.00,
    total DECIMAL(10,2) NOT NULL,
    status order_status DEFAULT 'pending',
    payment_status payment_status DEFAULT 'pending',
    payment_method payment_method NOT NULL,
    order_type order_type NOT NULL,
    delivery_address JSONB,
    estimated_delivery_time TIMESTAMP,
    actual_delivery_time TIMESTAMP,
    special_instructions TEXT,
    cancellation_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_orders_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id)
);

-- Order items table
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL,
    menu_item_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL,
    customizations JSONB,
    special_instructions TEXT,
    total DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_order_items_order FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    CONSTRAINT fk_order_items_menu_item FOREIGN KEY (menu_item_id) REFERENCES menu_items(id)
);

-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    restaurant_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    priority notification_priority DEFAULT 'medium',
    data JSONB,
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_notifications_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_notifications_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id)
);

-- Push tokens table
CREATE TABLE push_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    token VARCHAR(500) NOT NULL,
    platform platform_type NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_push_tokens_user FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE(user_id, device_id)
);

-- Create indexes for performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_restaurant_id ON users(restaurant_id);
CREATE INDEX idx_users_role ON users(role);

CREATE INDEX idx_restaurants_owner_id ON restaurants(owner_id);
CREATE INDEX idx_restaurants_verification_status ON restaurants(verification_status);

CREATE INDEX idx_documents_restaurant_id ON documents(restaurant_id);
CREATE INDEX idx_documents_type ON documents(type);
CREATE INDEX idx_documents_status ON documents(status);

CREATE INDEX idx_menu_categories_restaurant_id ON menu_categories(restaurant_id);
CREATE INDEX idx_menu_categories_display_order ON menu_categories(display_order);

CREATE INDEX idx_menu_items_restaurant_id ON menu_items(restaurant_id);
CREATE INDEX idx_menu_items_category_id ON menu_items(category_id);
CREATE INDEX idx_menu_items_is_available ON menu_items(is_available);
CREATE INDEX idx_menu_items_display_order ON menu_items(display_order);

CREATE INDEX idx_orders_restaurant_id ON orders(restaurant_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_customer_phone ON orders(customer_phone);
CREATE INDEX idx_orders_order_number ON orders(order_number);

CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_menu_item_id ON order_items(menu_item_id);

CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_restaurant_id ON notifications(restaurant_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_type ON notifications(type);

CREATE INDEX idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX idx_push_tokens_device_id ON push_tokens(device_id);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_restaurants_updated_at BEFORE UPDATE ON restaurants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_restaurant_settings_updated_at BEFORE UPDATE ON restaurant_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_menu_categories_updated_at BEFORE UPDATE ON menu_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_menu_items_updated_at BEFORE UPDATE ON menu_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_push_tokens_updated_at BEFORE UPDATE ON push_tokens FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert demo data
INSERT INTO users (id, username, email, password_hash, name, role, verification_status, is_demo, is_active) VALUES
('550e8400-e29b-41d4-a716-446655440000', 'demo_owner', '<EMAIL>', '$2b$10$demo_hash', 'Demo Owner', 'owner', 'verified', true, true),
('550e8400-e29b-41d4-a716-446655440001', 'demo_admin', '<EMAIL>', '$2b$10$demo_hash', 'Demo Admin', 'admin', 'verified', true, true),
('550e8400-e29b-41d4-a716-446655440002', 'demo_staff', '<EMAIL>', '$2b$10$demo_hash', 'Demo Staff', 'staff', 'verified', true, true);
```

## 🚀 Node.js/Express Implementation Examples

### Project Structure
```
backend/
├── src/
│   ├── controllers/
│   │   ├── authController.js
│   │   ├── restaurantController.js
│   │   ├── menuController.js
│   │   ├── ordersController.js
│   │   ├── staffController.js
│   │   └── analyticsController.js
│   ├── middleware/
│   │   ├── auth.js
│   │   ├── validation.js
│   │   ├── upload.js
│   │   └── rateLimiter.js
│   ├── models/
│   │   ├── User.js
│   │   ├── Restaurant.js
│   │   ├── MenuItem.js
│   │   └── Order.js
│   ├── routes/
│   │   ├── auth.js
│   │   ├── restaurant.js
│   │   ├── menu.js
│   │   ├── orders.js
│   │   └── staff.js
│   ├── services/
│   │   ├── emailService.js
│   │   ├── uploadService.js
│   │   └── notificationService.js
│   ├── utils/
│   │   ├── database.js
│   │   ├── jwt.js
│   │   └── helpers.js
│   └── app.js
├── package.json
└── .env
```

### Package.json Dependencies
```json
{
  "name": "restaurant-api",
  "version": "1.0.0",
  "dependencies": {
    "express": "^4.18.2",
    "pg": "^8.8.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "multer": "^1.4.5-lts.1",
    "joi": "^17.7.0",
    "cors": "^2.8.5",
    "helmet": "^6.0.1",
    "express-rate-limit": "^6.7.0",
    "dotenv": "^16.0.3",
    "uuid": "^9.0.0",
    "nodemailer": "^6.8.0",
    "aws-sdk": "^2.1284.0"
  },
  "devDependencies": {
    "nodemon": "^2.0.20",
    "jest": "^29.3.1"
  }
}
```

### Environment Variables (.env)
```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/restaurant_app
DB_HOST=localhost
DB_PORT=5432
DB_NAME=restaurant_app
DB_USER=username
DB_PASSWORD=password

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# File Upload
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=restaurant-app-uploads
AWS_REGION=us-east-1

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# App
PORT=3000
NODE_ENV=development
API_BASE_URL=http://localhost:3000
```

### Database Connection (utils/database.js)
```javascript
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Test connection
pool.on('connect', () => {
  console.log('Connected to PostgreSQL database');
});

pool.on('error', (err) => {
  console.error('Database connection error:', err);
});

module.exports = {
  query: (text, params) => pool.query(text, params),
  pool
};
```

### JWT Utilities (utils/jwt.js)
```javascript
const jwt = require('jsonwebtoken');

const generateToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN
  });
};

const verifyToken = (token) => {
  return jwt.verify(token, process.env.JWT_SECRET);
};

module.exports = {
  generateToken,
  verifyToken
};
```

### Authentication Middleware (middleware/auth.js)
```javascript
const { verifyToken } = require('../utils/jwt');
const db = require('../utils/database');

const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access token required',
        error: 'UNAUTHORIZED'
      });
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);

    // Get user from database
    const userQuery = `
      SELECT u.*, r.id as restaurant_id, r.verification_status as restaurant_verification_status
      FROM users u
      LEFT JOIN restaurants r ON u.restaurant_id = r.id
      WHERE u.id = $1 AND u.is_active = true
    `;

    const { rows } = await db.query(userQuery, [decoded.sub]);

    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token',
        error: 'INVALID_TOKEN'
      });
    }

    req.user = rows[0];
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
      error: 'INVALID_TOKEN'
    });
  }
};

const authorize = (roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        error: 'FORBIDDEN'
      });
    }
    next();
  };
};

const checkVerification = (req, res, next) => {
  // Skip verification for demo accounts
  if (req.user.is_demo) {
    return next();
  }

  // Check if user/restaurant is verified
  if (req.user.role === 'owner' && req.user.restaurant_verification_status !== 'verified') {
    return res.status(403).json({
      success: false,
      message: 'Restaurant verification pending',
      error: 'VERIFICATION_PENDING',
      details: {
        verificationStatus: req.user.restaurant_verification_status
      }
    });
  }

  if (req.user.verification_status !== 'verified') {
    return res.status(403).json({
      success: false,
      message: 'Account verification pending',
      error: 'VERIFICATION_PENDING',
      details: {
        verificationStatus: req.user.verification_status
      }
    });
  }

  next();
};

module.exports = {
  authenticate,
  authorize,
  checkVerification
};
```

### Validation Middleware (middleware/validation.js)
```javascript
const Joi = require('joi');

const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        error: 'VALIDATION_ERROR',
        details: {
          field: error.details[0].path.join('.'),
          message: error.details[0].message
        }
      });
    }

    next();
  };
};

// Validation schemas
const schemas = {
  register: Joi.object({
    restaurantName: Joi.string().min(2).max(255).required(),
    restaurantType: Joi.array().items(Joi.string()).min(1).required(),
    description: Joi.string().max(1000).optional(),
    ownerName: Joi.string().min(2).max(255).required(),
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).required(),
    address: Joi.string().min(10).max(500).required(),
    city: Joi.string().max(100).optional(),
    state: Joi.string().max(100).optional(),
    zipCode: Joi.string().max(20).optional(),
    username: Joi.string().alphanum().min(3).max(50).required(),
    password: Joi.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required()
  }),

  login: Joi.object({
    username: Joi.string().required(),
    password: Joi.string().required(),
    role: Joi.string().valid('owner', 'admin', 'staff').required()
  }),

  menuItem: Joi.object({
    name: Joi.string().min(2).max(255).required(),
    description: Joi.string().min(10).max(1000).required(),
    price: Joi.number().positive().precision(2).required(),
    categoryId: Joi.string().uuid().required(),
    isVegetarian: Joi.boolean().optional(),
    isVegan: Joi.boolean().optional(),
    isGlutenFree: Joi.boolean().optional(),
    spiceLevel: Joi.string().valid('mild', 'medium', 'hot', 'extra-hot').optional(),
    preparationTime: Joi.number().integer().min(1).max(120).required(),
    calories: Joi.number().integer().positive().optional(),
    ingredients: Joi.array().items(Joi.string()).optional(),
    allergens: Joi.array().items(Joi.string()).optional()
  }),

  updateOrderStatus: Joi.object({
    status: Joi.string().valid('pending', 'confirmed', 'preparing', 'ready', 'out-for-delivery', 'delivered', 'cancelled', 'refunded').required()
  })
};

module.exports = {
  validate,
  schemas
};
```

### Authentication Controller (controllers/authController.js)
```javascript
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const db = require('../utils/database');
const { generateToken } = require('../utils/jwt');

const register = async (req, res) => {
  const client = await db.pool.connect();

  try {
    await client.query('BEGIN');

    const {
      restaurantName,
      restaurantType,
      description,
      ownerName,
      email,
      phone,
      address,
      city,
      state,
      zipCode,
      username,
      password
    } = req.body;

    // Check if username or email already exists
    const existingUser = await client.query(
      'SELECT id FROM users WHERE username = $1 OR email = $2',
      [username, email]
    );

    if (existingUser.rows.length > 0) {
      await client.query('ROLLBACK');
      return res.status(409).json({
        success: false,
        message: 'Username or email already exists',
        error: 'CONFLICT'
      });
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create restaurant
    const restaurantId = uuidv4();
    const restaurantQuery = `
      INSERT INTO restaurants (id, name, description, address, city, state, zip_code, phone, email, type, owner_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;

    // Create user first to get user ID for restaurant owner_id
    const userId = uuidv4();
    const userQuery = `
      INSERT INTO users (id, username, email, password_hash, name, phone, role, restaurant_id)
      VALUES ($1, $2, $3, $4, $5, $6, 'owner', $7)
      RETURNING id, username, email, name, role, verification_status, is_demo, created_at
    `;

    const userResult = await client.query(userQuery, [
      userId, username, email, passwordHash, ownerName, phone, restaurantId
    ]);

    const restaurantResult = await client.query(restaurantQuery, [
      restaurantId, restaurantName, description, address, city, state, zipCode, phone, email, JSON.stringify(restaurantType), userId
    ]);

    // Create default restaurant settings
    await client.query(`
      INSERT INTO restaurant_settings (restaurant_id, opening_hours)
      VALUES ($1, $2)
    `, [restaurantId, JSON.stringify({
      monday: { open: "09:00", close: "22:00", closed: false },
      tuesday: { open: "09:00", close: "22:00", closed: false },
      wednesday: { open: "09:00", close: "22:00", closed: false },
      thursday: { open: "09:00", close: "22:00", closed: false },
      friday: { open: "09:00", close: "23:00", closed: false },
      saturday: { open: "10:00", close: "23:00", closed: false },
      sunday: { open: "10:00", close: "21:00", closed: false }
    })]);

    // Handle document uploads if files are present
    const documents = {};
    if (req.files) {
      // Process uploaded documents (implementation depends on your file upload service)
      // This is a placeholder - implement actual file upload logic
      for (const [fieldName, files] of Object.entries(req.files)) {
        if (files && files.length > 0) {
          const file = files[0];
          // Upload to cloud storage and get URL
          const fileUrl = await uploadFile(file); // Implement this function

          // Save document record
          await client.query(`
            INSERT INTO documents (restaurant_id, type, file_url, file_name, file_size, mime_type)
            VALUES ($1, $2, $3, $4, $5, $6)
          `, [restaurantId, fieldName, fileUrl, file.originalname, file.size, file.mimetype]);

          documents[fieldName] = {
            url: fileUrl,
            status: 'pending'
          };
        }
      }
    }

    await client.query('COMMIT');

    // Generate JWT token
    const token = generateToken({
      sub: userId,
      username,
      role: 'owner',
      restaurantId
    });

    res.status(201).json({
      success: true,
      message: 'Registration successful. Your documents are being reviewed.',
      data: {
        user: userResult.rows[0],
        restaurant: {
          id: restaurantId,
          name: restaurantName,
          verificationStatus: 'pending',
          documents
        },
        token
      }
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: 'INTERNAL_ERROR'
    });
  } finally {
    client.release();
  }
};

const login = async (req, res) => {
  try {
    const { username, password, role } = req.body;

    // Get user with restaurant info
    const userQuery = `
      SELECT u.*, r.id as restaurant_id, r.name as restaurant_name,
             r.verification_status as restaurant_verification_status, r.is_open, r.status
      FROM users u
      LEFT JOIN restaurants r ON u.restaurant_id = r.id
      WHERE u.username = $1 AND u.is_active = true
    `;

    const { rows } = await db.query(userQuery, [username]);

    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid username or password',
        error: 'INVALID_CREDENTIALS'
      });
    }

    const user = rows[0];

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid username or password',
        error: 'INVALID_CREDENTIALS'
      });
    }

    // Check role
    if (user.role !== role) {
      return res.status(401).json({
        success: false,
        message: `These credentials are not valid for ${role} role`,
        error: 'INVALID_CREDENTIALS'
      });
    }

    // Update last login
    await db.query('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1', [user.id]);

    // Generate token
    const token = generateToken({
      sub: user.id,
      username: user.username,
      role: user.role,
      restaurantId: user.restaurant_id
    });

    // Prepare response
    const responseData = {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role,
        verificationStatus: user.verification_status,
        restaurantId: user.restaurant_id,
        isDemo: user.is_demo,
        lastLogin: new Date().toISOString()
      },
      token
    };

    if (user.restaurant_id) {
      responseData.restaurant = {
        id: user.restaurant_id,
        name: user.restaurant_name,
        verificationStatus: user.restaurant_verification_status,
        isOpen: user.is_open,
        status: user.status
      };
    }

    res.json({
      success: true,
      message: 'Login successful',
      data: responseData
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: 'INTERNAL_ERROR'
    });
  }
};

module.exports = {
  register,
  login
};
```

This covers the core authentication implementation. The document is getting quite comprehensive - would you like me to continue with more controller examples or focus on specific areas?
