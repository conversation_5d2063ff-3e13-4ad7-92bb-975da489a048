import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Switch, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { router } from 'expo-router';

export default function NotificationsSettings() {
  const [settings, setSettings] = useState({
    orderNotifications: true,
    pushNotifications: true,
    emailNotifications: false,
    smsNotifications: true,
    soundEnabled: true,
    vibrationEnabled: true,
    newOrderAlert: true,
    orderStatusUpdates: true,
    customerMessages: true,
    inventoryAlerts: false,
    staffNotifications: true,
    promotionalEmails: false,
  });

  const toggleSetting = (key: keyof typeof settings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const renderToggleItem = (
    title: string,
    description: string,
    key: keyof typeof settings,
    icon: string
  ) => (
    <View style={styles.settingItem}>
      <View style={styles.settingLeft}>
        <View style={styles.iconContainer}>
          <IconSymbol name={icon} size={20} color="#DC143C" />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingDescription}>{description}</Text>
        </View>
      </View>
      <Switch
        value={settings[key]}
        onValueChange={() => toggleSetting(key)}
        trackColor={{ false: '#E5E7EB', true: '#22C55E' }}
        thumbColor={settings[key] ? '#FFFFFF' : '#9CA3AF'}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#DC143C', '#B91C3C']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <IconSymbol name="chevron.left" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notification Settings</Text>
          <View style={styles.headerRight} />
        </View>
      </LinearGradient>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* General Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>General Notifications</Text>
          <View style={styles.card}>
            {renderToggleItem(
              'Push Notifications',
              'Receive notifications on your device',
              'pushNotifications',
              'bell'
            )}
            {renderToggleItem(
              'Email Notifications',
              'Receive notifications via email',
              'emailNotifications',
              'envelope'
            )}
            {renderToggleItem(
              'SMS Notifications',
              'Receive notifications via text message',
              'smsNotifications',
              'message'
            )}
          </View>
        </View>

        {/* Order Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Notifications</Text>
          <View style={styles.card}>
            {renderToggleItem(
              'New Order Alerts',
              'Get notified when new orders arrive',
              'newOrderAlert',
              'bag'
            )}
            {renderToggleItem(
              'Order Status Updates',
              'Updates when order status changes',
              'orderStatusUpdates',
              'clock'
            )}
            {renderToggleItem(
              'Customer Messages',
              'Messages from customers',
              'customerMessages',
              'message.circle'
            )}
          </View>
        </View>

        {/* Sound & Vibration */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sound & Vibration</Text>
          <View style={styles.card}>
            {renderToggleItem(
              'Sound Enabled',
              'Play sound for notifications',
              'soundEnabled',
              'speaker.wave.2'
            )}
            {renderToggleItem(
              'Vibration Enabled',
              'Vibrate for notifications',
              'vibrationEnabled',
              'iphone.radiowaves.left.and.right'
            )}
          </View>
        </View>

        {/* Business Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Business Notifications</Text>
          <View style={styles.card}>
            {renderToggleItem(
              'Inventory Alerts',
              'Low stock and inventory warnings',
              'inventoryAlerts',
              'cube.box'
            )}
            {renderToggleItem(
              'Staff Notifications',
              'Staff-related updates and messages',
              'staffNotifications',
              'person.2'
            )}
            {renderToggleItem(
              'Promotional Emails',
              'Marketing and promotional content',
              'promotionalEmails',
              'megaphone'
            )}
          </View>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F5E8',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerRight: {
    width: 40,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  card: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(220, 20, 60, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  bottomSpacing: {
    height: 40,
  },
});
