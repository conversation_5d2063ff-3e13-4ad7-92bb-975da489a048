import { useApi, useApiMutation, usePaginatedApi, usePolling<PERSON>pi } from './useApi';
import { ordersService, Order, OrderStatus, OrderFilters } from '@/services';

// Orders list hooks
export function useOrders(page: number = 1, limit: number = 20, filters?: OrderFilters) {
  return usePaginatedApi((p, l) => ordersService.getOrders(p, l, filters), limit);
}

export function useOrder(id: string) {
  return useApi(() => ordersService.getOrder(id), [id]);
}

// Order management hooks
export function useUpdateOrderStatus() {
  return useApiMutation(({ id, status }: { id: string; status: OrderStatus }) => 
    ordersService.updateOrderStatus(id, status)
  );
}

export function useUpdateEstimatedDeliveryTime() {
  return useApiMutation(({ id, estimatedTime }: { id: string; estimatedTime: string }) => 
    ordersService.updateEstimatedDeliveryTime(id, estimatedTime)
  );
}

export function useCancelOrder() {
  return useApiMutation(({ id, reason }: { id: string; reason: string }) => 
    ordersService.cancelOrder(id, reason)
  );
}

export function useRefundOrder() {
  return useApiMutation(({ id, amount, reason }: { id: string; amount?: number; reason?: string }) => 
    ordersService.refundOrder(id, amount, reason)
  );
}

// Statistics hooks
export function useOrderStats(period?: 'today' | 'week' | 'month' | 'year') {
  return useApi(() => ordersService.getOrderStats(period), [period]);
}

// Real-time active orders
export function useActiveOrders(enabled: boolean = true) {
  return usePollingApi(() => ordersService.getActiveOrders(), 10000, enabled); // Poll every 10 seconds
}

// Search hooks
export function useSearchOrders() {
  return useApiMutation((query: string) => 
    ordersService.searchOrders(query)
  );
}

export function useOrdersByCustomer(customerId: string) {
  return useApi(() => ordersService.getOrdersByCustomer(customerId), [customerId]);
}

// Bulk operations hooks
export function useBulkUpdateOrders() {
  return useApiMutation(({ orderIds, updates }: { 
    orderIds: string[]; 
    updates: { status?: OrderStatus; estimatedDeliveryTime?: string } 
  }) => 
    ordersService.bulkUpdateOrders(orderIds, updates)
  );
}

// Analytics hooks
export function useOrderAnalytics(
  startDate: string, 
  endDate: string, 
  groupBy: 'hour' | 'day' | 'week' | 'month' = 'day'
) {
  return useApi(() => ordersService.getOrderAnalytics(startDate, endDate, groupBy), [startDate, endDate, groupBy]);
}

// Print hook
export function usePrintOrder() {
  return useApiMutation((id: string) => 
    ordersService.printOrder(id)
  );
}
