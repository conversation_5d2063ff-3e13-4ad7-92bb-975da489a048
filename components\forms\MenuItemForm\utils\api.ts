// 🚨 MOCK MODE FOR TESTING - All API calls return simulated responses
// API client for Menu Item Management Form
// This handles all external API calls for the form functionality

import { MenuItem } from '../types';

// API configuration
const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'https://api.restaurant-app.com';
const API_VERSION = 'v1';

class ApiClient {
  private static instance: ApiClient;
  private baseUrl: string;
  private authToken: string | null = null;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/${API_VERSION}`;
  }

  static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  setAuthToken(token: string) {
    this.authToken = token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.authToken) {
      defaultHeaders.Authorization = `Bearer ${this.authToken}`;
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      // console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  async uploadFile(endpoint: string, file: File | Blob, additionalData?: any): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
    }

    const headers: HeadersInit = {};
    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`;
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Upload failed: ${response.statusText}`);
    }

    return await response.json();
  }
}

const apiClient = ApiClient.getInstance();

// Menu Item API functions - MOCK VERSION FOR TESTING
export async function saveMenuItemDraft(data: Partial<MenuItem>): Promise<void> {
  // Mock implementation - simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  // console.log('Mock: Saving draft:', data);
  // In testing mode, we just log the data instead of making API calls
  // You could also save to AsyncStorage for persistence during testing
}

export async function submitMenuItem(data: MenuItem): Promise<MenuItem> {
  // Mock implementation - simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // console.log('Mock: Submitting menu item:', data);

  // Return mock response with generated ID
  const mockResponse: MenuItem = {
    ...data,
    id: `mock-${Date.now()}`,
    isDraft: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    version: 1,
  };

  return mockResponse;
}

export async function updateMenuItem(id: string, data: Partial<MenuItem>): Promise<MenuItem> {
  try {
    const response = await apiClient.put<MenuItem>(`/menu-items/${id}`, {
      ...data,
      updatedAt: new Date().toISOString(),
    });
    return response;
  } catch (error) {
    console.error('Failed to update menu item:', error);
    throw new Error('Failed to update menu item. Please try again.');
  }
}

export async function deleteMenuItem(id: string): Promise<void> {
  try {
    await apiClient.delete(`/menu-items/${id}`);
  } catch (error) {
    console.error('Failed to delete menu item:', error);
    throw new Error('Failed to delete menu item. Please try again.');
  }
}

export async function getMenuItem(id: string): Promise<MenuItem> {
  // Mock implementation - simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('Mock: Fetching menu item:', id);

  // Return mock menu item data
  const mockMenuItem: MenuItem = {
    id,
    name: 'Mock Menu Item',
    description: 'This is a mock menu item for testing purposes',
    category: 'Main Courses',
    price: [{
      id: 'default',
      name: 'Standard',
      price: 15.99,
      currency: 'USD',
      taxRate: 0.08,
      isDefault: true,
    }],
    preparationTime: 20,
    ingredients: [],
    allergens: [],
    nutritionalInfo: {
      calories: 350,
      protein: 25,
      carbohydrates: 30,
      fat: 15,
      fiber: 5,
      sugar: 8,
      sodium: 600,
      cholesterol: 45,
      servingSize: '1 serving',
      servingsPerContainer: 1,
    },
    images: [],
    availability: {
      isAvailable: true,
      schedules: [],
      specialDates: [],
      stockLevel: 100,
      lowStockThreshold: 10,
    },
    customizations: [],
    tags: ['mock', 'testing'],
    isActive: true,
    isDraft: false,
    version: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'mock-user',
    seoOptimized: false,
    popularityScore: 75,
    profitMargin: 0.35,
  };

  return mockMenuItem;
}

export async function getMenuItems(filters?: any): Promise<MenuItem[]> {
  // Mock implementation - simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));

  console.log('Mock: Fetching menu items with filters:', filters);

  // Return mock menu items array
  const mockMenuItems: MenuItem[] = [
    {
      id: 'mock-1',
      name: 'Mock Burger',
      description: 'A delicious mock burger for testing',
      category: 'Main Courses',
      price: [{ id: 'default', name: 'Standard', price: 12.99, currency: 'USD', taxRate: 0.08, isDefault: true }],
      preparationTime: 15,
      ingredients: [],
      allergens: [],
      nutritionalInfo: { calories: 450, protein: 30, carbohydrates: 35, fat: 20, fiber: 3, sugar: 5, sodium: 800, cholesterol: 75, servingSize: '1 burger', servingsPerContainer: 1 },
      images: [],
      availability: { isAvailable: true, schedules: [], specialDates: [], stockLevel: 50, lowStockThreshold: 5 },
      customizations: [],
      tags: ['burger', 'popular'],
      isActive: true,
      isDraft: false,
      version: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'mock-user',
      seoOptimized: true,
      popularityScore: 85,
      profitMargin: 0.4,
    },
    {
      id: 'mock-2',
      name: 'Mock Salad',
      description: 'A fresh mock salad for testing',
      category: 'Salads',
      price: [{ id: 'default', name: 'Standard', price: 9.99, currency: 'USD', taxRate: 0.08, isDefault: true }],
      preparationTime: 10,
      ingredients: [],
      allergens: [],
      nutritionalInfo: { calories: 250, protein: 15, carbohydrates: 20, fat: 8, fiber: 8, sugar: 12, sodium: 400, cholesterol: 0, servingSize: '1 salad', servingsPerContainer: 1 },
      images: [],
      availability: { isAvailable: true, schedules: [], specialDates: [], stockLevel: 30, lowStockThreshold: 3 },
      customizations: [],
      tags: ['healthy', 'vegetarian'],
      isActive: true,
      isDraft: false,
      version: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'mock-user',
      seoOptimized: false,
      popularityScore: 70,
      profitMargin: 0.6,
    },
  ];

  return mockMenuItems;
}

// Image upload functions - MOCK VERSION FOR TESTING
export async function uploadMenuItemImage(
  file: File | Blob,
  menuItemId?: string
): Promise<{ url: string; thumbnailUrl: string; metadata: any }> {
  // Mock implementation - simulate upload delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log('Mock: Uploading image for menu item:', menuItemId, 'File size:', file.size);

  // Return mock image URLs
  const mockImageId = `mock-img-${Date.now()}`;
  return {
    url: `https://via.placeholder.com/400x300/FF6B35/FFFFFF?text=Mock+Image+${mockImageId}`,
    thumbnailUrl: `https://via.placeholder.com/150x150/FF6B35/FFFFFF?text=Thumb+${mockImageId}`,
    metadata: {
      width: 400,
      height: 300,
      size: 125000, // 125KB
      format: 'jpg',
      uploadedAt: new Date().toISOString(),
    },
  };
}

export async function deleteMenuItemImage(imageId: string): Promise<void> {
  // Mock implementation - simulate delete delay
  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('Mock: Deleting image:', imageId);
  // In mock mode, we just log the deletion
}

// Category functions - MOCK VERSION FOR TESTING
export async function getCategories(): Promise<any[]> {
  // Mock implementation - simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));

  console.log('Mock: Fetching categories');

  // Return comprehensive mock categories
  return [
    { id: '1', name: 'Appetizers', color: '#FF6B6B', icon: 'restaurant', description: 'Start your meal right' },
    { id: '2', name: 'Main Courses', color: '#4ECDC4', icon: 'restaurant-menu', description: 'Hearty main dishes' },
    { id: '3', name: 'Desserts', color: '#45B7D1', icon: 'cake', description: 'Sweet endings' },
    { id: '4', name: 'Beverages', color: '#96CEB4', icon: 'local-drink', description: 'Refreshing drinks' },
    { id: '5', name: 'Salads', color: '#FFEAA7', icon: 'eco', description: 'Fresh and healthy' },
    { id: '6', name: 'Soups', color: '#DDA0DD', icon: 'soup-kitchen', description: 'Warm and comforting' },
    { id: '7', name: 'Pizza', color: '#FF8A65', icon: 'local-pizza', description: 'Italian classics' },
    { id: '8', name: 'Burgers', color: '#A1887F', icon: 'lunch-dining', description: 'American favorites' },
    { id: '9', name: 'Seafood', color: '#4DB6AC', icon: 'set-meal', description: 'Fresh from the ocean' },
    { id: '10', name: 'Vegetarian', color: '#81C784', icon: 'eco', description: 'Plant-based options' },
  ];
}

export async function createCategory(data: any): Promise<any> {
  try {
    return await apiClient.post<any>('/categories', data);
  } catch (error) {
    console.error('Failed to create category:', error);
    throw new Error('Failed to create category. Please try again.');
  }
}

// Ingredient functions - MOCK VERSION FOR TESTING
export async function searchIngredients(query: string): Promise<any[]> {
  // Mock implementation - simulate search delay
  await new Promise(resolve => setTimeout(resolve, 400));

  console.log('Mock: Searching ingredients for:', query);

  // Comprehensive mock ingredients database
  const mockIngredients = [
    { id: '1', name: 'Tomato', category: 'Vegetable', allergens: [], calories: 18, protein: 0.9, carbs: 3.9, fat: 0.2 },
    { id: '2', name: 'Chicken Breast', category: 'Protein', allergens: [], calories: 165, protein: 31, carbs: 0, fat: 3.6 },
    { id: '3', name: 'Mozzarella Cheese', category: 'Dairy', allergens: ['dairy'], calories: 280, protein: 22, carbs: 2.2, fat: 22 },
    { id: '4', name: 'Olive Oil', category: 'Oil', allergens: [], calories: 884, protein: 0, carbs: 0, fat: 100 },
    { id: '5', name: 'Basil', category: 'Herb', allergens: [], calories: 22, protein: 3.2, carbs: 2.6, fat: 0.6 },
    { id: '6', name: 'Onion', category: 'Vegetable', allergens: [], calories: 40, protein: 1.1, carbs: 9.3, fat: 0.1 },
    { id: '7', name: 'Garlic', category: 'Vegetable', allergens: [], calories: 149, protein: 6.4, carbs: 33, fat: 0.5 },
    { id: '8', name: 'Bell Pepper', category: 'Vegetable', allergens: [], calories: 31, protein: 1, carbs: 7, fat: 0.3 },
    { id: '9', name: 'Mushrooms', category: 'Vegetable', allergens: [], calories: 22, protein: 3.1, carbs: 3.3, fat: 0.3 },
    { id: '10', name: 'Lettuce', category: 'Vegetable', allergens: [], calories: 15, protein: 1.4, carbs: 2.9, fat: 0.2 },
    { id: '11', name: 'Beef', category: 'Protein', allergens: [], calories: 250, protein: 26, carbs: 0, fat: 15 },
    { id: '12', name: 'Salmon', category: 'Protein', allergens: ['fish'], calories: 208, protein: 22, carbs: 0, fat: 13 },
    { id: '13', name: 'Cheddar Cheese', category: 'Dairy', allergens: ['dairy'], calories: 403, protein: 25, carbs: 1.3, fat: 33 },
    { id: '14', name: 'Bread', category: 'Grain', allergens: ['gluten'], calories: 265, protein: 9, carbs: 49, fat: 3.2 },
    { id: '15', name: 'Rice', category: 'Grain', allergens: [], calories: 130, protein: 2.7, carbs: 28, fat: 0.3 },
  ];

  // Filter ingredients based on query
  return mockIngredients.filter(ingredient =>
    ingredient.name.toLowerCase().includes(query.toLowerCase()) ||
    ingredient.category.toLowerCase().includes(query.toLowerCase())
  );
}

export async function getIngredient(id: string): Promise<any> {
  try {
    return await apiClient.get<any>(`/ingredients/${id}`);
  } catch (error) {
    console.error('Failed to fetch ingredient:', error);
    throw new Error('Failed to load ingredient details. Please try again.');
  }
}

// Allergen functions
export async function getAllergens(): Promise<any[]> {
  try {
    return await apiClient.get<any[]>('/allergens');
  } catch (error) {
    console.error('Failed to fetch allergens:', error);
    // Return mock data for development
    return [
      { id: '1', name: 'Dairy', severity: 'moderate', icon: 'milk', description: 'Contains milk products' },
      { id: '2', name: 'Gluten', severity: 'severe', icon: 'grain', description: 'Contains wheat, barley, or rye' },
      { id: '3', name: 'Nuts', severity: 'severe', icon: 'nut', description: 'Contains tree nuts' },
      { id: '4', name: 'Shellfish', severity: 'severe', icon: 'seafood', description: 'Contains shellfish' },
      { id: '5', name: 'Eggs', severity: 'mild', icon: 'egg', description: 'Contains eggs' },
      { id: '6', name: 'Soy', severity: 'moderate', icon: 'soy', description: 'Contains soy products' },
    ];
  }
}

// Analytics functions
export async function getMenuItemAnalytics(id: string): Promise<any> {
  try {
    return await apiClient.get<any>(`/menu-items/${id}/analytics`);
  } catch (error) {
    console.error('Failed to fetch analytics:', error);
    // Return mock data for development
    return {
      popularityTrend: [65, 70, 68, 75, 80, 85, 90],
      salesForecast: [120, 135, 140, 155, 160, 175, 180],
      profitMargin: 68.5,
      competitorPrices: [
        { restaurantName: 'Competitor A', itemName: 'Similar Item', price: 18.99, similarity: 85 },
        { restaurantName: 'Competitor B', itemName: 'Related Dish', price: 22.50, similarity: 72 },
      ],
      seasonalDemand: [
        { month: 1, demandMultiplier: 0.8, confidence: 75 },
        { month: 2, demandMultiplier: 0.9, confidence: 80 },
        { month: 3, demandMultiplier: 1.1, confidence: 85 },
      ],
      customerPreferences: [
        { preference: 'Spicy Food', score: 78, trend: 'increasing' },
        { preference: 'Healthy Options', score: 85, trend: 'stable' },
        { preference: 'Large Portions', score: 65, trend: 'decreasing' },
      ],
    };
  }
}

// Template functions - MOCK VERSION FOR TESTING
export async function getMenuItemTemplates(): Promise<any[]> {
  // Mock implementation - simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));

  // console.log('Mock: Fetching menu item templates');

  // Return comprehensive mock templates
  return [
    {
      id: '1',
      name: 'Basic Pizza Template',
      description: 'Standard pizza with customizable toppings',
      category: 'Pizza',
      usageCount: 45,
      tags: ['pizza', 'italian', 'customizable'],
      thumbnail: 'https://via.placeholder.com/150x150/FF6B35/FFFFFF?text=Pizza',
      estimatedTime: 25,
      difficulty: 'Medium',
    },
    {
      id: '2',
      name: 'Fresh Salad Template',
      description: 'Healthy salad with various fresh ingredients',
      category: 'Salads',
      usageCount: 32,
      tags: ['healthy', 'fresh', 'vegetarian'],
      thumbnail: 'https://via.placeholder.com/150x150/4CAF50/FFFFFF?text=Salad',
      estimatedTime: 10,
      difficulty: 'Easy',
    },
    {
      id: '3',
      name: 'Gourmet Burger Template',
      description: 'Premium burger with artisanal ingredients',
      category: 'Burgers',
      usageCount: 28,
      tags: ['burger', 'gourmet', 'american'],
      thumbnail: 'https://via.placeholder.com/150x150/8D6E63/FFFFFF?text=Burger',
      estimatedTime: 20,
      difficulty: 'Medium',
    },
    {
      id: '4',
      name: 'Pasta Dish Template',
      description: 'Classic pasta with sauce variations',
      category: 'Main Courses',
      usageCount: 38,
      tags: ['pasta', 'italian', 'comfort'],
      thumbnail: 'https://via.placeholder.com/150x150/FF9800/FFFFFF?text=Pasta',
      estimatedTime: 18,
      difficulty: 'Easy',
    },
    {
      id: '5',
      name: 'Dessert Template',
      description: 'Sweet dessert with customizable toppings',
      category: 'Desserts',
      usageCount: 22,
      tags: ['dessert', 'sweet', 'customizable'],
      thumbnail: 'https://via.placeholder.com/150x150/E91E63/FFFFFF?text=Dessert',
      estimatedTime: 15,
      difficulty: 'Hard',
    },
  ];
}

export async function getMenuItemTemplate(id: string): Promise<any> {
  // Mock implementation - simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  console.log('Mock: Fetching template:', id);

  // Return mock template data based on ID
  const mockTemplates: Record<string, any> = {
    '1': {
      id: '1',
      name: 'Basic Pizza Template',
      description: 'A classic pizza base with customizable toppings',
      category: 'Pizza',
      price: [{ id: 'default', name: 'Medium', price: 16.99, currency: 'USD', taxRate: 0.08, isDefault: true }],
      preparationTime: 25,
      ingredients: [
        { id: '1', name: 'Pizza Dough', quantity: 1, unit: 'piece', isOptional: false, allergens: ['gluten'] },
        { id: '2', name: 'Tomato Sauce', quantity: 0.5, unit: 'cup', isOptional: false, allergens: [] },
        { id: '3', name: 'Mozzarella Cheese', quantity: 200, unit: 'g', isOptional: false, allergens: ['dairy'] },
      ],
      customizations: [
        {
          id: 'toppings',
          name: 'Toppings',
          description: 'Choose your favorite toppings',
          isRequired: false,
          allowMultiple: true,
          options: [
            { id: 'pepperoni', name: 'Pepperoni', priceAdjustment: 2.00 },
            { id: 'mushrooms', name: 'Mushrooms', priceAdjustment: 1.50 },
            { id: 'olives', name: 'Olives', priceAdjustment: 1.00 },
          ],
        },
      ],
      tags: ['pizza', 'italian', 'customizable'],
    },
    '2': {
      id: '2',
      name: 'Fresh Salad Template',
      description: 'A healthy mix of fresh greens and vegetables',
      category: 'Salads',
      price: [{ id: 'default', name: 'Regular', price: 12.99, currency: 'USD', taxRate: 0.08, isDefault: true }],
      preparationTime: 10,
      ingredients: [
        { id: '1', name: 'Mixed Greens', quantity: 100, unit: 'g', isOptional: false, allergens: [] },
        { id: '2', name: 'Cherry Tomatoes', quantity: 50, unit: 'g', isOptional: false, allergens: [] },
        { id: '3', name: 'Cucumber', quantity: 30, unit: 'g', isOptional: false, allergens: [] },
      ],
      customizations: [
        {
          id: 'dressing',
          name: 'Dressing',
          description: 'Choose your dressing',
          isRequired: true,
          allowMultiple: false,
          options: [
            { id: 'ranch', name: 'Ranch', priceAdjustment: 0 },
            { id: 'italian', name: 'Italian', priceAdjustment: 0 },
            { id: 'balsamic', name: 'Balsamic Vinaigrette', priceAdjustment: 0.50 },
          ],
        },
      ],
      tags: ['healthy', 'fresh', 'vegetarian'],
    },
  };

  const template = mockTemplates[id];
  if (!template) {
    throw new Error(`Template with ID ${id} not found`);
  }

  return template;
}

// Utility function to set auth token
export function setApiAuthToken(token: string) {
  apiClient.setAuthToken(token);
}
