import { MenuItemForm } from '@/components/forms/MenuItemForm/MenuItemForm';
import { MenuItem as FormMenuItem } from '@/components/forms/MenuItemForm/types';
import { Colors } from '@/constants/Theme';
import { useAppDispatch } from '@/store';
import { addMenuItem } from '@/store/slices/menuSlice';
import { MenuItem as AppMenuItem } from '@/types';
import { convertFormMenuItemToAppMenuItem, validateFormMenuItemForConversion } from '@/utils/menuItemAdapter';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  SafeAreaView,
  StyleSheet,
  View,
} from 'react-native';

export default function AddMenuItemScreen() {
  const dispatch = useAppDispatch();
  const params = useLocalSearchParams();
  const [initialData, setInitialData] = useState<FormMenuItem | undefined>(undefined);
  const [mode, setMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    if (params.mode === 'edit' && params.itemData) {
      try {
        const itemData = JSON.parse(params.itemData as string);
        setInitialData(itemData);
        setMode('edit');
      } catch (error) {
        console.error('Failed to parse item data:', error);
      }
    }
  }, [params]);

  const handleSubmit = async (formMenuItem: FormMenuItem) => {
    try {
      // Validate the form data
      const validationErrors = validateFormMenuItemForConversion(formMenuItem);
      if (validationErrors.length > 0) {
        Alert.alert(
          'Validation Error',
          validationErrors.join('\n'),
          [{ text: 'OK' }]
        );
        return;
      }

      // Convert form data to app format
      const appMenuItemData = convertFormMenuItemToAppMenuItem(formMenuItem);

      // Generate a unique ID for the new menu item
      const newMenuItem: AppMenuItem = {
        ...appMenuItemData,
        id: Date.now().toString(), // Simple ID generation for demo
      };

      // Add to Redux store
      dispatch(addMenuItem(newMenuItem));

      // Show success message
      Alert.alert(
        'Success!',
        'Menu item has been added successfully.',
        [
          {
            text: 'OK',
            onPress: () => {
              // Navigate back to menu screen
              router.back();
            },
          },
        ]
      );
    } catch (error) {
      // console.error('Error adding menu item:', error); 
      Alert.alert(
        'Error',
        'Failed to add menu item. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleCancel = () => {
    Alert.alert(
      'Cancel',
      'Are you sure you want to cancel? Any unsaved changes will be lost.',
      [
        {
          text: 'Continue Editing',
          style: 'cancel',
        },
        {
          text: 'Cancel',
          style: 'destructive',
          onPress: () => {
            router.back();
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" backgroundColor={Colors.light.background} translucent={false} />
      <View style={styles.content}>
        <MenuItemForm
          initialData={initialData}
          mode={mode}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  content: {
    flex: 1,
  },
});
