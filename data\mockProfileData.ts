import { RestaurantProfile } from '../types/profile';

export const mockRestaurantProfile: RestaurantProfile = {
  id: 'rest_001',
  name: 'Spice Garden Restaurant',
  description: 'Authentic Pakistani and Indian cuisine with a modern twist. Experience the rich flavors of traditional spices in a contemporary setting.',
  tagline: 'Where Tradition Meets Innovation',
  establishedDate: new Date('2018-03-15'),
  
  coverPhoto: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800&h=400&fit=crop',
  profilePicture: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=200&h=200&fit=crop',
  
  gallery: [
    {
      id: 'img_001',
      type: 'image',
      url: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
      title: 'Signature Biryani',
      description: 'Our famous chicken biryani with aromatic basmati rice',
      category: 'food',
      uploadDate: new Date('2024-01-15'),
      featured: true,
      order: 1
    },
    {
      id: 'img_002',
      type: 'image',
      url: 'https://images.unsplash.com/photo-1574484284002-952d92456975?w=400&h=300&fit=crop',
      title: 'Restaurant Interior',
      description: 'Modern dining area with traditional touches',
      category: 'interior',
      uploadDate: new Date('2024-01-10'),
      featured: false,
      order: 2
    },
    {
      id: 'img_003',
      type: 'image',
      url: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=300&fit=crop',
      title: 'Grilled Specialties',
      description: 'Fresh grilled items prepared daily',
      category: 'food',
      uploadDate: new Date('2024-01-08'),
      featured: true,
      order: 3
    }
  ],
  
  cuisineTypes: [
    { id: 'pak', name: 'Pakistani', color: '#00A86B', icon: '🇵🇰' },
    { id: 'ind', name: 'Indian', color: '#FF9933', icon: '🇮🇳' },
    { id: 'bbq', name: 'BBQ/Grilled', color: '#8B4513', icon: '🔥' },
    { id: 'hal', name: 'Halal', color: '#228B22', icon: '✅' }
  ],
  
  priceRange: {
    level: 2,
    currency: 'PKR',
    averagePrice: 1200,
    minPrice: 500,
    maxPrice: 2500
  },
  
  rating: {
    overall: 4.6,
    totalReviews: 1247,
    breakdown: {
      food: 4.7,
      service: 4.5,
      ambiance: 4.6,
      value: 4.4
    },
    recentTrend: 'up'
  },
  
  contact: {
    primaryPhone: '+92-300-1234567',
    secondaryPhone: '+92-42-35123456',
    email: '<EMAIL>',
    whatsapp: '+92-300-1234567',
    emergencyContact: '+92-300-7654321'
  },
  
  location: {
    address: '123 Main Boulevard, Gulberg III',
    city: 'Lahore',
    state: 'Punjab',
    zipCode: '54000',
    country: 'Pakistan',
    coordinates: {
      latitude: 31.5204,
      longitude: 74.3587
    },
    landmarks: ['Near Liberty Market', 'Opposite MM Alam Road'],
    deliveryZones: [
      {
        id: 'zone_1',
        name: 'Gulberg',
        radius: 3,
        deliveryFee: 150,
        minimumOrder: 800,
        estimatedTime: 25
      },
      {
        id: 'zone_2',
        name: 'DHA',
        radius: 5,
        deliveryFee: 200,
        minimumOrder: 1000,
        estimatedTime: 35
      }
    ]
  },
  
  operatingHours: {
    monday: { isOpen: true, openTime: '11:00', closeTime: '23:00', is24Hours: false },
    tuesday: { isOpen: true, openTime: '11:00', closeTime: '23:00', is24Hours: false },
    wednesday: { isOpen: true, openTime: '11:00', closeTime: '23:00', is24Hours: false },
    thursday: { isOpen: true, openTime: '11:00', closeTime: '23:00', is24Hours: false },
    friday: { isOpen: true, openTime: '12:00', closeTime: '00:00', is24Hours: false },
    saturday: { isOpen: true, openTime: '11:00', closeTime: '00:00', is24Hours: false },
    sunday: { isOpen: true, openTime: '11:00', closeTime: '23:00', is24Hours: false },
    specialHours: [
      {
        date: new Date('2024-12-25'),
        reason: 'Christmas Day',
        schedule: null
      }
    ],
    timezone: 'Asia/Karachi',
    breakTimes: [
      {
        startTime: '15:00',
        endTime: '17:00',
        days: ['monday', 'tuesday', 'wednesday', 'thursday'],
        reason: 'Afternoon Break'
      }
    ]
  },
  
  businessInfo: {
    registrationNumber: 'REG-2018-001234',
    taxId: 'TAX-567890',
    licenseNumber: 'LIC-2018-5678',
    ownerName: 'Ahmed Hassan',
    businessType: 'llc',
    yearEstablished: 2018,
    employeeCount: 25,
    seatingCapacity: 80
  },
  
  socialMedia: {
    facebook: 'https://facebook.com/spicegardenrest',
    instagram: 'https://instagram.com/spicegarden_official',
    youtube: 'https://youtube.com/spicegardenrestaurant'
  },
  
  website: 'https://spicegarden.com',
  
  settings: {
    theme: {
      primaryColor: '#FF6B35',
      secondaryColor: '#2E8B57',
      accentColor: '#FFD700',
      darkMode: false
    },
    notifications: {
      orderAlerts: true,
      reviewAlerts: true,
      promotionAlerts: false,
      systemUpdates: true,
      emailNotifications: true,
      smsNotifications: true,
      pushNotifications: true
    },
    privacy: {
      profileVisibility: 'public',
      showContactInfo: true,
      showRatings: true,
      allowReviews: true,
      dataSharing: false
    },
    features: {
      onlineOrdering: true,
      tableReservations: true,
      deliveryService: true,
      takeawayService: true,
      loyaltyProgram: true,
      giftCards: false
    },
    language: 'en',
    currency: 'PKR'
  },
  
  analytics: {
    overview: {
      totalRevenue: 2450000,
      totalOrders: 1847,
      totalCustomers: 892,
      averageOrderValue: 1327,
      growthRate: 15.3,
      period: 'month'
    },
    revenue: {
      daily: [
        { date: '2024-01-01', value: 45000 },
        { date: '2024-01-02', value: 52000 },
        { date: '2024-01-03', value: 48000 },
        { date: '2024-01-04', value: 61000 },
        { date: '2024-01-05', value: 58000 },
        { date: '2024-01-06', value: 72000 },
        { date: '2024-01-07', value: 68000 }
      ],
      weekly: [],
      monthly: [],
      yearly: [],
      byCategory: [
        { category: 'Main Courses', revenue: 1200000, percentage: 49, growth: 12 },
        { category: 'Appetizers', revenue: 490000, percentage: 20, growth: 8 },
        { category: 'Beverages', revenue: 368000, percentage: 15, growth: 15 },
        { category: 'Desserts', revenue: 245000, percentage: 10, growth: 22 },
        { category: 'Specials', revenue: 147000, percentage: 6, growth: 35 }
      ],
      projections: []
    },
    orders: {
      totalOrders: 1847,
      completedOrders: 1756,
      cancelledOrders: 91,
      averagePreparationTime: 18,
      peakHours: [
        { hour: 13, orderCount: 45, revenue: 67500 },
        { hour: 14, orderCount: 52, revenue: 78000 },
        { hour: 19, orderCount: 68, revenue: 102000 },
        { hour: 20, orderCount: 71, revenue: 106500 },
        { hour: 21, orderCount: 58, revenue: 87000 }
      ],
      orderSources: [
        { source: 'Mobile App', count: 923, percentage: 50 },
        { source: 'Website', count: 554, percentage: 30 },
        { source: 'Phone', count: 277, percentage: 15 },
        { source: 'Walk-in', count: 93, percentage: 5 }
      ]
    },
    customers: {
      totalCustomers: 892,
      newCustomers: 156,
      returningCustomers: 736,
      customerRetentionRate: 82.5,
      averageCustomerLifetime: 8.5,
      topCustomers: [
        { id: 'cust_001', name: 'Sarah Ahmed', totalOrders: 47, totalSpent: 62350, lastOrder: new Date('2024-01-15') },
        { id: 'cust_002', name: 'Ali Hassan', totalOrders: 39, totalSpent: 51870, lastOrder: new Date('2024-01-14') }
      ]
    },
    performance: {
      efficiency: 94.2,
      customerSatisfaction: 4.6,
      orderAccuracy: 96.8,
      deliveryTime: 28.5,
      responseTime: 2.3,
      qualityScore: 4.7
    },
    trends: [
      { metric: 'Revenue', current: 2450000, previous: 2130000, change: 15.0, trend: 'up' },
      { metric: 'Orders', current: 1847, previous: 1623, change: 13.8, trend: 'up' },
      { metric: 'Customers', current: 892, previous: 798, change: 11.8, trend: 'up' }
    ]
  },
  
  team: [
    {
      id: 'team_001',
      name: 'Ahmed Hassan',
      role: 'Owner & Head Chef',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop',
      email: '<EMAIL>',
      phone: '+92-300-1234567',
      joinDate: new Date('2018-03-15'),
      isActive: true,
      permissions: ['all']
    },
    {
      id: 'team_002',
      name: 'Fatima Khan',
      role: 'Restaurant Manager',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop',
      email: '<EMAIL>',
      phone: '+92-300-2345678',
      joinDate: new Date('2019-01-10'),
      isActive: true,
      permissions: ['orders', 'staff', 'inventory']
    }
  ],
  
  achievements: [
    {
      id: 'ach_001',
      title: 'Customer Favorite',
      description: 'Maintained 4.5+ rating for 6 consecutive months',
      icon: '⭐',
      dateEarned: new Date('2023-12-01'),
      category: 'quality',
      level: 'gold'
    },
    {
      id: 'ach_002',
      title: 'Growth Champion',
      description: 'Achieved 20% revenue growth in Q4 2023',
      icon: '📈',
      dateEarned: new Date('2024-01-01'),
      category: 'growth',
      level: 'platinum'
    }
  ],
  
  certifications: [
    {
      id: 'cert_001',
      name: 'Food Safety Certification',
      issuingAuthority: 'Punjab Food Authority',
      issueDate: new Date('2023-06-01'),
      expiryDate: new Date('2024-06-01'),
      verified: true
    },
    {
      id: 'cert_002',
      name: 'Halal Certification',
      issuingAuthority: 'Halal Development Council',
      issueDate: new Date('2023-01-15'),
      expiryDate: new Date('2025-01-15'),
      verified: true
    }
  ]
};
