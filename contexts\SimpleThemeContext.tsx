import AsyncStorage from '@react-native-async-storage/async-storage';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { useColorScheme } from 'react-native';

// Simple Light/Dark Theme Colors - Red & White Theme
const lightTheme = {
  // Primary Colors - Red Theme
  primary: '#DC143C',      // Crimson Red
  primaryDark: '#B91C1C',  // Dark Red
  primaryLight: '#EF4444', // Light Red

  // Background Colors - White Theme
  background: '#FFFFFF',
  backgroundSecondary: '#FAFAFA',
  surface: '#FFFFFF',
  surfaceSecondary: '#F5F5F5',

  // Text Colors
  text: '#1F2937',         // Dark gray
  textSecondary: '#6B7280', // Medium gray
  textTertiary: '#9CA3AF',  // Light gray
  textInverse: '#FFFFFF',   // White

  // Border Colors
  border: '#E5E7EB',       // Light gray
  borderLight: '#F3F4F6',  // Very light gray

  // Status Colors - Red Theme
  success: '#DC143C',      // Red for success
  warning: '#DC143C',      // Red for warning
  error: '#B91C1C',        // Dark red for error
  info: '#DC143C',         // Red for info

  // Order Status Colors (Time-based) - Red Theme
  orderOnTime: '#EF4444',     // Light Red (0-14 min)
  orderProgress: '#DC143C',   // Red (15-29 min)
  orderUrgent: '#B91C1C',     // Dark Red (30+ min)
};

const darkTheme = {
  // Primary Colors - Red Theme for Dark Mode
  primary: '#EF4444',      // Bright red for dark mode
  primaryDark: '#DC2626',  // Darker red
  primaryLight: '#F87171', // Lighter red

  // Background Colors - Dark Theme
  background: '#111827',   // Very dark gray
  backgroundSecondary: '#1F2937', // Dark gray
  surface: '#1F2937',      // Dark gray
  surfaceSecondary: '#374151', // Medium dark gray

  // Text Colors
  text: '#F9FAFB',         // Very light gray
  textSecondary: '#D1D5DB', // Light gray
  textTertiary: '#9CA3AF',  // Medium gray
  textInverse: '#111827',   // Very dark gray

  // Border Colors
  border: '#4B5563',       // Medium gray
  borderLight: '#374151',  // Dark gray

  // Status Colors - Red Theme for Dark Mode
  success: '#EF4444',      // Red for success
  warning: '#EF4444',      // Red for warning
  error: '#DC2626',        // Dark red for error
  info: '#EF4444',         // Red for info

  // Order Status Colors (Time-based) - Red Theme for Dark Mode
  orderOnTime: '#F87171',     // Light Red (0-14 min)
  orderProgress: '#EF4444',   // Red (15-29 min)
  orderUrgent: '#DC2626',     // Dark Red (30+ min)
};

type ThemeMode = 'light' | 'dark' | 'system';
type ThemeColors = typeof lightTheme;

interface ThemeContextType {
  theme: ThemeMode;
  colors: ThemeColors;
  isDark: boolean;
  setTheme: (theme: ThemeMode) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export function SimpleThemeProvider({ children }: ThemeProviderProps) {
  const systemColorScheme = useColorScheme();
  const [theme, setThemeState] = useState<ThemeMode>('system');
  
  // Determine if dark mode should be active
  const isDark = theme === 'dark' || (theme === 'system' && systemColorScheme === 'dark');
  const colors = isDark ? darkTheme : lightTheme;

  // Load saved theme preference
  useEffect(() => {
    loadThemePreference();
  }, []);

  // Save theme preference when it changes
  useEffect(() => {
    saveThemePreference(theme);
  }, [theme]);

  const loadThemePreference = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('theme_preference');
      if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
        setThemeState(savedTheme as ThemeMode);
      }
    } catch (error) {
      console.log('Error loading theme preference:', error);
    }
  };

  const saveThemePreference = async (themeMode: ThemeMode) => {
    try {
      await AsyncStorage.setItem('theme_preference', themeMode);
    } catch (error) {
      console.log('Error saving theme preference:', error);
    }
  };

  const setTheme = (newTheme: ThemeMode) => {
    setThemeState(newTheme);
  };

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else if (theme === 'dark') {
      setTheme('system');
    } else {
      setTheme('light');
    }
  };

  const value: ThemeContextType = {
    theme,
    colors,
    isDark,
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

// Hook to use theme
export function useSimpleTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useSimpleTheme must be used within a SimpleThemeProvider');
  }
  return context;
}

// Convenience hook to get just colors
export function useThemeColors() {
  const { colors } = useSimpleTheme();
  return colors;
}

// Utility function for order status colors
export function getOrderStatusColor(minutes: number, colors: ThemeColors) {
  if (minutes >= 30) return colors.orderUrgent;
  if (minutes >= 15) return colors.orderProgress;
  return colors.orderOnTime;
}

// Utility function for order status text
export function getOrderStatusText(minutes: number) {
  if (minutes >= 30) return 'Urgent';
  if (minutes >= 15) return 'In Progress';
  return 'On Time';
}
