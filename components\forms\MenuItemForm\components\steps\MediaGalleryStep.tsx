import { Colors } from '@/constants/Theme';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { FormValidationError, MenuItem, MenuItemImage } from '../../types';

const { width: screenWidth } = Dimensions.get('window');
const imageSize = (screenWidth - 48) / 2; // 2 columns with padding

interface MediaGalleryStepProps {
  formData: Partial<MenuItem>;
  onUpdateField: (field: string, value: any) => void;
  onValidateField: (field: string) => Promise<FormValidationError[]>;
  errors: FormValidationError[];
}

export function MediaGalleryStep({
  formData,
  onUpdateField,
  onValidateField,
  errors,
}: MediaGalleryStepProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  
  const fadeAnimation = useSharedValue(0);
  const slideAnimation = useSharedValue(0);

  useEffect(() => {
    fadeAnimation.value = withTiming(1, { duration: 500 });
    slideAnimation.value = withSpring(1, { damping: 20, stiffness: 100 });
    
    // Initialize images array if not set
    if (!formData.images) {
      onUpdateField('images', []);
    }
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
    transform: [{ translateY: (1 - slideAnimation.value) * 50 }],
  }));

  const getFieldError = (fieldName: string) => {
    return errors.find(error => error.field === fieldName);
  };

  const handleImagePicker = async () => {
    try {
      // Request media library permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Gallery permission is required to select photos.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images' as any,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        await processSelectedImage(result.assets[0], 'gallery');
      }
    } catch (error) {
      console.error('Gallery error:', error);
      Alert.alert('Error', 'Failed to open gallery. Please try again.');
    }
  };

  const processSelectedImage = async (asset: ImagePicker.ImagePickerAsset, _source: string) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + 10;
        });
      }, 100);

      // Simulate API call (in real app, you would upload to your server)
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newImage: MenuItemImage = {
        id: Date.now().toString(),
        url: asset.uri, // Use the actual selected image URI
        thumbnailUrl: asset.uri, // In real app, you might generate a thumbnail
        alt: `Menu item image ${(formData.images?.length || 0) + 1}`,
        isPrimary: (formData.images?.length || 0) === 0,
        order: (formData.images?.length || 0) + 1,
        filters: {
          brightness: 1,
          contrast: 1,
          saturation: 1,
          blur: 0,
        },
        metadata: {
          size: asset.fileSize || 0,
          format: asset.uri.split('.').pop() || 'jpeg',
          width: asset.width,
          height: asset.height,
          uploadedAt: new Date(),
        },
      };

      const currentImages = formData.images || [];
      onUpdateField('images', [...currentImages, newImage]);
      onValidateField('images');

      Alert.alert('Success', 'Image added successfully!');
    } catch (_error) {
      Alert.alert('Error', 'Failed to process image. Please try again.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDeleteImage = (imageId: string) => {
    Alert.alert(
      'Delete Image',
      'Are you sure you want to delete this image?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteImage(imageId),
        },
      ]
    );
  };

  const deleteImage = async (imageId: string) => {
    try {
      const currentImages = formData.images || [];
      const updatedImages = currentImages.filter(img => img.id !== imageId);
      
      // If we deleted the primary image, make the first remaining image primary
      if (updatedImages.length > 0) {
        const deletedImage = currentImages.find(img => img.id === imageId);
        if (deletedImage?.isPrimary) {
          updatedImages[0].isPrimary = true;
        }
      }
      
      onUpdateField('images', updatedImages);
      onValidateField('images');
    } catch (_error) {
      Alert.alert('Error', 'Failed to delete image. Please try again.');
    }
  };

  const handleSetPrimaryImage = (imageId: string) => {
    const currentImages = formData.images || [];
    const updatedImages = currentImages.map(img => ({
      ...img,
      isPrimary: img.id === imageId,
    }));
    
    onUpdateField('images', updatedImages);
  };

  const handleReorderImages = (fromIndex: number, toIndex: number) => {
    const currentImages = [...(formData.images || [])];
    const [movedImage] = currentImages.splice(fromIndex, 1);
    currentImages.splice(toIndex, 0, movedImage);
    
    // Update order values
    const reorderedImages = currentImages.map((img, index) => ({
      ...img,
      order: index + 1,
    }));
    
    onUpdateField('images', reorderedImages);
  };

  const imagesError = getFieldError('images');
  const currentImages = formData.images || [];

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Media Gallery</Text>
          <Text style={styles.subtitle}>
            Add high-quality images to showcase your menu item
          </Text>
        </View>

        {/* Upload Section */}
        <View style={styles.uploadSection}>
          <TouchableOpacity
            style={[
              styles.uploadButton,
              isUploading && styles.uploadButtonDisabled,
            ]}
            onPress={handleImagePicker}
            disabled={isUploading}
          >
            {isUploading ? (
              <View style={styles.uploadingContainer}>
                <ActivityIndicator size="large" color={Colors.light.primary} />
                <Text style={styles.uploadingText}>
                  Uploading... {uploadProgress}%
                </Text>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      { width: `${uploadProgress}%` },
                    ]}
                  />
                </View>
              </View>
            ) : (
              <>
                <Ionicons
                  name="image-outline" as any
                  size={48}
                  color={Colors.light.primary}
                />
                <Text style={styles.uploadButtonText}>Add Photos</Text>
                <Text style={styles.uploadButtonSubtext}>
                  Tap to choose from gallery
                </Text>
              </>
            )}
          </TouchableOpacity>

          {imagesError && (
            <Text style={styles.errorText}>{imagesError.message}</Text>
          )}
        </View>

        {/* Images Grid */}
        {currentImages.length > 0 && (
          <View style={styles.imagesSection}>
            <Text style={styles.sectionTitle}>
              Images ({currentImages.length})
            </Text>
            
            <View style={styles.imagesGrid}>
              {currentImages.map((image, index) => (
                <ImageCard
                  key={image.id}
                  image={image}
                  index={index}
                  onDelete={() => handleDeleteImage(image.id)}
                  onSetPrimary={() => handleSetPrimaryImage(image.id)}
                  onReorder={handleReorderImages}
                />
              ))}
            </View>
          </View>
        )}

        {/* Tips Section */}
        <View style={styles.tipsContainer}>
          <View style={styles.tipsHeader}>
            <Ionicons
              name="bulb-outline" as any
              size={20}
              color={Colors.light.warning}
            />
            <Text style={styles.tipsTitle}>Photo Tips</Text>
          </View>
          <View style={styles.tipsList}>
            <Text style={styles.tipItem}>
              • Use natural lighting for the best results
            </Text>
            <Text style={styles.tipItem}>
              • Take photos from multiple angles
            </Text>
            <Text style={styles.tipItem}>
              • Show the dish as customers will receive it
            </Text>
            <Text style={styles.tipItem}>
              • Include garnishes and sides in the shot
            </Text>
            <Text style={styles.tipItem}>
              • First image will be used as the main photo
            </Text>
          </View>
        </View>
      </ScrollView>
    </Animated.View>
  );
}

interface ImageCardProps {
  image: MenuItemImage;
  index: number;
  onDelete: () => void;
  onSetPrimary: () => void;
  onReorder: (fromIndex: number, toIndex: number) => void;
}

function ImageCard({
  image,
  index: _index,
  onDelete,
  onSetPrimary,
  onReorder: _onReorder,
}: ImageCardProps) {
  const scaleAnimation = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleAnimation.value }],
  }));

  const handlePress = () => {
    scaleAnimation.value = withSpring(0.95, { damping: 15, stiffness: 300 });
    setTimeout(() => {
      scaleAnimation.value = withSpring(1, { damping: 15, stiffness: 300 });
    }, 100);
  };

  return (
    <Animated.View style={[styles.imageCard, animatedStyle]}>
      <TouchableOpacity onPress={handlePress} activeOpacity={0.8}>
        <Image
          source={{ uri: image.thumbnailUrl || image.url }}
          style={styles.imagePreview}
          resizeMode="cover"
        />
        
        {/* Primary Badge */}
        {image.isPrimary && (
          <View style={styles.primaryBadge}>
            <Text style={styles.primaryBadgeText}>Main</Text>
          </View>
        )}
        
        {/* Image Actions */}
        <View style={styles.imageActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={onSetPrimary}
          >
            <Ionicons
              name={image.isPrimary ? 'star' : 'star-outline' as any}
              size={16}
              color={image.isPrimary ? Colors.light.warning : Colors.light.textInverse}
            />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={onDelete}
          >
            <Ionicons
              name="trash-outline" as any
              size={16}
              color={Colors.light.textInverse}
            />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    lineHeight: 22,
  },
  uploadSection: {
    marginBottom: 32,
  },
  uploadButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
    paddingHorizontal: 24,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    borderStyle: 'dashed',
    backgroundColor: Colors.light.primaryLight,
  },
  uploadButtonDisabled: {
    opacity: 0.7,
  },
  uploadingContainer: {
    alignItems: 'center',
    width: '100%',
  },
  uploadingText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
    marginTop: 12,
    marginBottom: 16,
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: Colors.light.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.light.primary,
    borderRadius: 2,
  },
  uploadButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.primary,
    marginTop: 12,
  },
  uploadButtonSubtext: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginTop: 4,
    textAlign: 'center',
  },
  imagesSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  imagesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  imageCard: {
    width: imageSize,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: Colors.light.surface,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imagePreview: {
    width: '100%',
    height: imageSize * 0.75,
    backgroundColor: Colors.light.border,
  },
  primaryBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: Colors.light.warning,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  primaryBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
  },
  imageActions: {
    flexDirection: 'row',
    position: 'absolute',
    top: 8,
    right: 8,
    gap: 4,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButton: {
    backgroundColor: 'rgba(220, 53, 69, 0.8)',
  },
  tipsContainer: {
    backgroundColor: Colors.light.warningLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: Colors.light.warning,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 8,
  },
  tipsList: {
    gap: 4,
  },
  tipItem: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.error,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default MediaGalleryStep;
