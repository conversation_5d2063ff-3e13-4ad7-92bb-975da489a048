import React from 'react';
import { StyleSheet, View } from 'react-native';
import { FormStep, FormValidationError, MenuItem } from '../types';
import { BasicInformationStep } from './steps/BasicInformationStep';
import { CustomizationsStep } from './steps/CustomizationsStep';

import { MarketingStep } from './steps/MarketingStep';
import { MediaGalleryStep } from './steps/MediaGalleryStep';
import { PricingAvailabilityStep } from './steps/PricingAvailabilityStep';
import { ReviewPublishStep } from './steps/ReviewPublishStep';

interface StepContentProps {
  step: FormStep;
  formData: Partial<MenuItem>;
  onUpdateField: (field: string, value: any) => void;
  onValidateField: (field: string) => Promise<FormValidationError[]>;
  onSubmitForm: () => Promise<void>;
  errors: FormValidationError[];
  analytics: any;
  onGenerateAIDescription: () => Promise<string>;
  onOptimizeForSEO: () => Promise<void>;
  isSubmitting: boolean;
}

export function StepContent({
  step,
  formData,
  onUpdateField,
  onValidateField,
  onSubmitForm,
  errors,
  analytics,
  onGenerateAIDescription,
  onOptimizeForSEO,
  isSubmitting,
}: StepContentProps) {
  const getStepErrors = (stepId: string) => {
    return errors.filter(error => {
      // Map fields to steps
      const fieldStepMap: Record<string, string> = {
        name: 'basic',
        description: 'basic',
        category: 'basic',
        price: 'pricing',
        preparationTime: 'pricing',
        images: 'media',

        customizations: 'customizations',
        tags: 'marketing',
      };
      
      return fieldStepMap[error.field] === stepId;
    });
  };

  const stepErrors = getStepErrors(step.id);

  const commonProps = {
    formData,
    onUpdateField,
    onValidateField,
    errors: stepErrors,
  };

  const renderStepContent = () => {
    console.log('Rendering step content for:', step.id, step.title);

    switch (step.id) {
      case 'basic':
        // console.log('Rendering BasicInformationStep');
        return (
          <BasicInformationStep
            {...commonProps}
            onGenerateAIDescription={onGenerateAIDescription}
          />
        );

      case 'pricing':
        // console.log('Rendering PricingAvailabilityStep');
        return (
          <PricingAvailabilityStep
            {...commonProps}
            analytics={analytics}
          />
        );

      case 'media':
        // console.log('Rendering MediaGalleryStep');
        return (
          <MediaGalleryStep
            {...commonProps}
          />
        );



      case 'customizations':
        // console.log('Rendering CustomizationsStep');
        return (
          <CustomizationsStep
            {...commonProps}
          />
        );

      case 'marketing':
        // console.log('Rendering MarketingStep');
        return (
          <MarketingStep
            {...commonProps}
            onOptimizeForSEO={onOptimizeForSEO}
          />
        );

      case 'review':
        // console.log('Rendering ReviewPublishStep');
        return (
          <ReviewPublishStep
            {...commonProps}
            onSubmitForm={onSubmitForm}
            isSubmitting={isSubmitting}
          />
        );

      default:
        // console.log('Rendering default step (BasicInformationStep)');
        return (
          <View style={styles.placeholderContainer}>
            <BasicInformationStep
              {...commonProps}
              onGenerateAIDescription={onGenerateAIDescription}
            />
          </View>
        );
    }
  };

  return (
    <View style={styles.container}>
      {renderStepContent()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default StepContent;
