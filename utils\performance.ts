// Performance monitoring utilities for the restaurant app
import { InteractionManager, PerformanceObserver } from 'react-native';

export interface PerformanceMetrics {
  screenLoadTime: number;
  navigationTime: number;
  renderTime: number;
  memoryUsage: number;
  bundleSize?: number;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private startTimes: Map<string, number> = new Map();

  // Start timing a performance metric
  startTiming(key: string): void {
    this.startTimes.set(key, Date.now());
  }

  // End timing and record the metric
  endTiming(key: string, metricType: keyof PerformanceMetrics): void {
    const startTime = this.startTimes.get(key);
    if (!startTime) return;

    const duration = Date.now() - startTime;
    const existing = this.metrics.get(key) || {} as PerformanceMetrics;
    
    this.metrics.set(key, {
      ...existing,
      [metricType]: duration
    });

    this.startTimes.delete(key);
  }

  // Get metrics for a specific screen/component
  getMetrics(key: string): PerformanceMetrics | undefined {
    return this.metrics.get(key);
  }

  // Get all metrics
  getAllMetrics(): Map<string, PerformanceMetrics> {
    return this.metrics;
  }

  // Log performance metrics to console (development only)
  logMetrics(key?: string): void {
    if (__DEV__) {
      if (key) {
        const metrics = this.getMetrics(key);
        console.log(`Performance Metrics for ${key}:`, metrics);
      } else {
        console.log('All Performance Metrics:', Object.fromEntries(this.metrics));
      }
    }
  }

  // Monitor memory usage
  getMemoryUsage(): number {
    if (global.performance && global.performance.memory) {
      return global.performance.memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  }

  // Monitor navigation performance
  measureNavigation(screenName: string, callback: () => void): void {
    this.startTiming(`navigation_${screenName}`);
    
    InteractionManager.runAfterInteractions(() => {
      this.endTiming(`navigation_${screenName}`, 'navigationTime');
      callback();
    });
  }

  // Monitor screen render performance
  measureScreenRender(screenName: string): void {
    this.startTiming(`render_${screenName}`);
    
    // Use requestAnimationFrame to measure when rendering is complete
    requestAnimationFrame(() => {
      this.endTiming(`render_${screenName}`, 'renderTime');
    });
  }

  // Clear all metrics
  clearMetrics(): void {
    this.metrics.clear();
    this.startTimes.clear();
  }

  // Export metrics for analytics
  exportMetrics(): string {
    const metricsObject = Object.fromEntries(this.metrics);
    return JSON.stringify(metricsObject, null, 2);
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React Hook for performance monitoring
export const usePerformanceMonitor = (screenName: string) => {
  const startScreenLoad = () => {
    performanceMonitor.startTiming(`screen_${screenName}`);
  };

  const endScreenLoad = () => {
    performanceMonitor.endTiming(`screen_${screenName}`, 'screenLoadTime');
    performanceMonitor.logMetrics(`screen_${screenName}`);
  };

  const measureRender = () => {
    performanceMonitor.measureScreenRender(screenName);
  };

  return {
    startScreenLoad,
    endScreenLoad,
    measureRender,
    getMetrics: () => performanceMonitor.getMetrics(`screen_${screenName}`)
  };
};

// Performance decorator for async functions
export const withPerformanceTracking = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  operationName: string
): T => {
  return (async (...args: any[]) => {
    performanceMonitor.startTiming(operationName);
    try {
      const result = await fn(...args);
      performanceMonitor.endTiming(operationName, 'screenLoadTime');
      return result;
    } catch (error) {
      performanceMonitor.endTiming(operationName, 'screenLoadTime');
      throw error;
    }
  }) as T;
};

// Bundle size analyzer (development only)
export const analyzeBundleSize = () => {
  if (__DEV__) {
    console.log('Bundle analysis available via: npm run analyze-bundle');
  }
};
