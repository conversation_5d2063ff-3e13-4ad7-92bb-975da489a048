import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { PremiumColors, PremiumShadows } from '@/constants/PremiumTheme';

interface KPICardProps {
  title: string;
  value: string | number;
  change?: number;
  target?: string;
  icon: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
  size?: 'small' | 'medium' | 'large';
  animated?: boolean;
}

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  prefix?: string;
  suffix?: string;
  decimals?: number;
  style?: any;
}

export const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  value,
  duration = 1000,
  prefix = '',
  suffix = '',
  decimals = 0,
  style,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const displayValue = useRef(0);
  const [currentValue, setCurrentValue] = React.useState(0);

  useEffect(() => {
    animatedValue.addListener(({ value: animValue }) => {
      displayValue.current = animValue;
      setCurrentValue(animValue);
    });

    Animated.timing(animatedValue, {
      toValue: value,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: false,
    }).start();

    return () => {
      animatedValue.removeAllListeners();
    };
  }, [value]);

  const formatValue = (val: number) => {
    return `${prefix}${val.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}${suffix}`;
  };

  return (
    <Text style={style}>
      {formatValue(currentValue)}
    </Text>
  );
};

export const KPICard: React.FC<KPICardProps> = ({
  title,
  value,
  change,
  target,
  icon,
  iconColor = PremiumColors.primary,
  size = 'medium',
  animated = true,
}) => {
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();
  }, []);

  const getChangeColor = (changeValue: number) => {
    return changeValue >= 0 ? PremiumColors.success : PremiumColors.danger;
  };

  const getChangeIcon = (changeValue: number) => {
    return changeValue >= 0 ? 'trending-up' : 'trending-down';
  };

  const formatPercentage = (changeValue: number) => {
    const sign = changeValue >= 0 ? '+' : '';
    return `${sign}${changeValue}%`;
  };

  const cardStyles = [
    styles.kpiCard,
    size === 'small' && styles.kpiCardSmall,
    size === 'large' && styles.kpiCardLarge,
  ];

  const titleStyles = [
    styles.kpiTitle,
    size === 'small' && styles.kpiTitleSmall,
    size === 'large' && styles.kpiTitleLarge,
  ];

  const valueStyles = [
    styles.kpiValue,
    size === 'small' && styles.kpiValueSmall,
    size === 'large' && styles.kpiValueLarge,
  ];

  const iconSize = size === 'small' ? 18 : size === 'large' ? 28 : 22;

  return (
    <Animated.View style={[cardStyles, { transform: [{ scale: scaleAnim }] }]}>
      {/* Header with Icon and Title */}
      <View style={styles.kpiHeader}>
        <View style={[styles.iconContainer, { backgroundColor: `${iconColor}15` }]}>
          <Ionicons name={icon} size={iconSize} color={iconColor} />
        </View>
        <Text style={titleStyles}>{title}</Text>
      </View>

      {/* Main Value */}
      <View style={styles.kpiValueContainer}>
        {animated && typeof value === 'number' ? (
          <AnimatedCounter
            value={value}
            style={valueStyles}
            prefix={title.toLowerCase().includes('revenue') ? 'PKR ' : ''}
            suffix={title.toLowerCase().includes('rating') ? '/5' : ''}
            decimals={title.toLowerCase().includes('rating') ? 1 : 0}
          />
        ) : (
          <Text style={valueStyles}>{value}</Text>
        )}
      </View>

      {/* Change Indicator */}
      {change !== undefined && (
        <View style={styles.kpiChange}>
          <View style={[styles.changeIndicator, { backgroundColor: `${getChangeColor(change)}15` }]}>
            <Ionicons 
              name={getChangeIcon(change)} 
              size={14} 
              color={getChangeColor(change)} 
            />
            <Text style={[styles.kpiChangeText, { color: getChangeColor(change) }]}>
              {formatPercentage(change)}
            </Text>
          </View>
        </View>
      )}

      {/* Target/Additional Info */}
      {target && (
        <Text style={styles.kpiTarget}>{target}</Text>
      )}

      {/* Progress Bar for Completion Rates */}
      {title.toLowerCase().includes('completion') && typeof value === 'number' && (
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <Animated.View 
              style={[
                styles.progressBarFill, 
                { 
                  width: `${value}%`,
                  backgroundColor: value >= 90 ? PremiumColors.success : 
                                 value >= 70 ? PremiumColors.warning : 
                                 PremiumColors.danger
                }
              ]} 
            />
          </View>
        </View>
      )}
    </Animated.View>
  );
};

export const KPIGrid: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <View style={styles.kpiGrid}>{children}</View>;
};

export const KPIRow: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <View style={styles.kpiRow}>{children}</View>;
};

const styles = StyleSheet.create({
  // Grid and Row Layouts
  kpiGrid: {
    gap: 12,
  },
  kpiRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },

  // Card Styles
  kpiCard: {
    flex: 1,
    backgroundColor: PremiumColors.white,
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: PremiumColors.grayMedium,
    ...PremiumShadows.medium,
  },
  kpiCardSmall: {
    padding: 12,
    borderRadius: 12,
  },
  kpiCardLarge: {
    flex: 2,
    padding: 20,
    borderRadius: 20,
  },

  // Header Styles
  kpiHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 10,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Title Styles
  kpiTitle: {
    fontSize: 14,
    color: PremiumColors.grayDark,
    fontWeight: '600',
    flex: 1,
  },
  kpiTitleSmall: {
    fontSize: 12,
  },
  kpiTitleLarge: {
    fontSize: 16,
  },

  // Value Styles
  kpiValueContainer: {
    marginBottom: 8,
  },
  kpiValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: PremiumColors.black,
  },
  kpiValueSmall: {
    fontSize: 18,
  },
  kpiValueLarge: {
    fontSize: 32,
  },

  // Change Indicator Styles
  kpiChange: {
    marginBottom: 8,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  kpiChangeText: {
    fontSize: 12,
    fontWeight: '700',
  },

  // Target/Additional Info
  kpiTarget: {
    fontSize: 11,
    color: PremiumColors.grayDark,
    fontWeight: '500',
  },

  // Progress Bar Styles
  progressBarContainer: {
    marginTop: 8,
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: PremiumColors.grayMedium,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 2,
  },
});
