import api, { ApiResponse, PaginatedResponse, handleApiError } from './api';

// Restaurant types
export interface Restaurant {
  id: string;
  name: string;
  description?: string;
  address: string;
  phone: string;
  email: string;
  type: string[];
  status: 'active' | 'inactive' | 'suspended';
  verificationStatus: 'pending' | 'verified' | 'rejected';
  ownerId: string;
  createdAt: string;
  updatedAt: string;
}

export interface RestaurantStats {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  totalCustomers: number;
  activeMenuItems: number;
  pendingOrders: number;
}

export interface RestaurantSettings {
  isOpen: boolean;
  openingHours: {
    [key: string]: { open: string; close: string; closed: boolean };
  };
  deliveryRadius: number;
  minimumOrderAmount: number;
  deliveryFee: number;
  estimatedDeliveryTime: number;
  acceptingOrders: boolean;
}

class RestaurantService {
  // Get restaurant profile
  async getRestaurant(): Promise<ApiResponse<Restaurant>> {
    try {
      const response = await api.get<ApiResponse<Restaurant>>('/restaurant/profile');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update restaurant profile
  async updateRestaurant(data: Partial<Restaurant>): Promise<ApiResponse<Restaurant>> {
    try {
      const response = await api.put<ApiResponse<Restaurant>>('/restaurant/profile', data);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get restaurant statistics
  async getStats(period?: 'today' | 'week' | 'month' | 'year'): Promise<ApiResponse<RestaurantStats>> {
    try {
      const params = period ? { period } : {};
      const response = await api.get<ApiResponse<RestaurantStats>>('/restaurant/stats', { params });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get restaurant settings
  async getSettings(): Promise<ApiResponse<RestaurantSettings>> {
    try {
      const response = await api.get<ApiResponse<RestaurantSettings>>('/restaurant/settings');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update restaurant settings
  async updateSettings(settings: Partial<RestaurantSettings>): Promise<ApiResponse<RestaurantSettings>> {
    try {
      const response = await api.put<ApiResponse<RestaurantSettings>>('/restaurant/settings', settings);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Toggle restaurant open/close status
  async toggleStatus(isOpen: boolean): Promise<ApiResponse<{ isOpen: boolean }>> {
    try {
      const response = await api.patch<ApiResponse<{ isOpen: boolean }>>('/restaurant/toggle-status', { isOpen });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Upload restaurant images
  async uploadImage(imageUri: string, type: 'logo' | 'cover' | 'gallery'): Promise<ApiResponse<{ url: string }>> {
    try {
      const formData = new FormData();
      formData.append('image', {
        uri: imageUri,
        type: 'image/jpeg',
        name: `${type}_image.jpg`,
      } as any);
      formData.append('type', type);

      const response = await api.post<ApiResponse<{ url: string }>>(
        '/restaurant/upload-image',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Delete restaurant image
  async deleteImage(imageUrl: string): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.delete<ApiResponse<{ message: string }>>('/restaurant/delete-image', {
        data: { imageUrl }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get revenue analytics
  async getRevenueAnalytics(
    startDate: string,
    endDate: string,
    groupBy: 'day' | 'week' | 'month' = 'day'
  ): Promise<ApiResponse<Array<{ date: string; revenue: number; orders: number }>>> {
    try {
      const response = await api.get<ApiResponse<Array<{ date: string; revenue: number; orders: number }>>>(
        '/restaurant/analytics/revenue',
        {
          params: { startDate, endDate, groupBy }
        }
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get top selling items
  async getTopSellingItems(limit: number = 10): Promise<ApiResponse<Array<{
    itemId: string;
    name: string;
    totalSold: number;
    revenue: number;
  }>>> {
    try {
      const response = await api.get<ApiResponse<Array<{
        itemId: string;
        name: string;
        totalSold: number;
        revenue: number;
      }>>>('/restaurant/analytics/top-items', {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get customer analytics
  async getCustomerAnalytics(): Promise<ApiResponse<{
    totalCustomers: number;
    newCustomers: number;
    returningCustomers: number;
    averageOrdersPerCustomer: number;
  }>> {
    try {
      const response = await api.get<ApiResponse<{
        totalCustomers: number;
        newCustomers: number;
        returningCustomers: number;
        averageOrdersPerCustomer: number;
      }>>('/restaurant/analytics/customers');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export default new RestaurantService();
