import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { clearError, registerUser } from '@/store/slices/authSlice';
import { RegisterData } from '@/types';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

export default function RegisterScreen() {
  const dispatch = useAppDispatch();
  const { isLoading, error, isAuthenticated } = useAppSelector(state => state.auth);
  
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<RegisterData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    restaurantName: '',
    restaurantType: '',
    description: '',
    address: {
      id: '',
      street: '',
      city: '',
      state: ''
    },
    businessLicense: '',
    taxId: '',
    acceptTerms: false,
    acceptPrivacy: false,
  });

  const [countryCode, setCountryCode] = useState('+92'); // Only Pakistan
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showRestaurantTypeDropdown, setShowRestaurantTypeDropdown] = useState(false);

  // Restaurant type options
  const restaurantTypes = [
    'Fast Food',
    'Cafe',
    'BBQ / Grilled',
    'Bakery',
    'Seafood',
    'Pakistani / Desi',
    'Chinese',
    'Italian',
    'Indian',
    'American',
    'Japanese',
    'Soup & Salad Bar',
    'Juice Bar / Smoothie Spot',
    'Breakfast-Only',
    'Late Night',
    'Meal Box',
    'Biryani Center',
    'Pizza Parlor',
    'Pasta House'
  ];
  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    feedback: '',
    requirements: {
      length: false,
      uppercase: false,
      lowercase: false,
      number: false,
      special: false,
    }
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});

  // Navigate to main app if authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Use a timeout to prevent immediate navigation conflicts
      const timer = setTimeout(() => {
        router.replace('/(tabs)');
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  // Password strength checker
  const checkPasswordStrength = (password: string) => {
    const requirements = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*()_\-+={}[\]|:;"'<,>.?/~\\]/.test(password),
    };

    const score = Object.values(requirements).filter(Boolean).length;
    let feedback = '';

    if (score === 0) feedback = 'Very Weak';
    else if (score === 1) feedback = 'Weak';
    else if (score === 2) feedback = 'Fair';
    else if (score === 3) feedback = 'Good';
    else if (score === 4) feedback = 'Strong';
    else if (score === 5) feedback = 'Very Strong';

    return { score, feedback, requirements };
  };

  // Phone number formatter - Pakistan only, 11 digits
  const formatPhoneNumber = (phone: string, countryCode: string) => {
    // Remove all non-digits
    const digits = phone.replace(/\D/g, '');

    // Only allow up to 11 digits
    const limitedDigits = digits.slice(0, 11);

    if (countryCode === '+92') {
      // Pakistan format: XXXX-XXXXXXX (11 digits total)
      if (limitedDigits.length <= 4) return limitedDigits;
      if (limitedDigits.length <= 11) {
        return `${limitedDigits.slice(0, 4)}-${limitedDigits.slice(4)}`;
      }
    }

    return limitedDigits;
  };

  // Phone number validator - Pakistan only, exactly 11 digits
  const validatePhoneNumber = (phone: string, countryCode: string) => {
    const digits = phone.replace(/\D/g, '');

    if (countryCode === '+92') {
      // Pakistan: Accept exactly 11 digits
      if (digits.length !== 11) {
        return 'Phone number must be exactly 11 digits';
      }
      // Check if it starts with 03 for mobile numbers
      if (!digits.startsWith('03')) {
        return 'Please enter a valid mobile number starting with 03';
      }
    }

    return '';
  };

  const validateStep1 = () => {
    const errors: {[key: string]: string} = {};

    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }

    // Last name is now optional - no validation needed

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email';
    }

    // Enhanced password validation
    if (!formData.password) {
      errors.password = 'Password is required';
    } else {
      const strength = checkPasswordStrength(formData.password);
      if (strength.score < 4) {
        errors.password = 'Password must be strong (at least 4/5 requirements)';
      }
    }

    if (!formData.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    // Enhanced phone validation
    if (!formData.phone.trim()) {
      errors.phone = 'Phone number is required';
    } else {
      const phoneError = validatePhoneNumber(formData.phone, countryCode);
      if (phoneError) {
        errors.phone = phoneError;
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateStep2 = () => {
    const errors: {[key: string]: string} = {};

    if (!formData.restaurantName.trim()) {
      errors.restaurantName = 'Restaurant name is required';
    }

    if (!formData.restaurantType.trim()) {
      errors.restaurantType = 'Restaurant type is required';
    }

    if (!formData.address.street.trim()) {
      errors.street = 'Street address is required';
    }

    if (!formData.address.city.trim()) {
      errors.city = 'City is required';
    }

    if (!formData.address.state.trim()) {
      errors.state = 'State is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateStep3 = () => {
    const errors: {[key: string]: string} = {};
    
    if (!formData.acceptTerms) {
      errors.acceptTerms = 'You must accept the Terms of Service';
    }
    
    if (!formData.acceptPrivacy) {
      errors.acceptPrivacy = 'You must accept the Privacy Policy';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    let isValid = false;
    
    if (currentStep === 1) {
      isValid = validateStep1();
    } else if (currentStep === 2) {
      isValid = validateStep2();
    }
    
    if (isValid && currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setFormErrors({});
    }
  };

  const handleRegister = async () => {
    if (!validateStep3()) return;
    
    try {
      await dispatch(registerUser(formData)).unwrap();
      // Navigation handled by useEffect
    } catch {
      // Error handled by Redux state
    }
  };

  const updateField = (field: string, value: any) => {
    // Handle phone number formatting
    if (field === 'phone') {
      const formattedPhone = formatPhoneNumber(value, countryCode);
      value = formattedPhone;
    }

    // Handle password strength checking
    if (field === 'password') {
      const strength = checkPasswordStrength(value);
      setPasswordStrength(strength);
    }

    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev as any)[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    // Clear field error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const getPasswordStrength = () => {
    const password = formData.password;
    let score = 0;

    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[!@#$%^&*()_\-+={}[\]|:;"'<,>.?/~\\]/.test(password)) score++;

    if (score <= 2) {
      return { text: 'Weak', color: '#B91C1C' };
    } else if (score <= 3) {
      return { text: 'Fair', color: '#DC143C' };
    } else if (score <= 4) {
      return { text: 'Good', color: '#EF4444' };
    } else {
      return { text: 'Strong', color: '#DC143C' };
    }
  };

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Personal Information</Text>
      
      <View style={styles.row}>
        <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
          <Text style={styles.label}>First Name</Text>
          <TextInput
            style={[styles.input, formErrors.firstName && styles.inputError]}
            placeholder="John"
            value={formData.firstName}
            onChangeText={(value) => updateField('firstName', value)}
            autoCapitalize="words"
            editable={!isLoading}
          />
          {formErrors.firstName && <Text style={styles.errorText}>{formErrors.firstName}</Text>}
        </View>
        
        <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
          <Text style={styles.label}>Last Name (Optional)</Text>
          <TextInput
            style={[styles.input, formErrors.lastName && styles.inputError]}
            placeholder="Doe"
            value={formData.lastName}
            onChangeText={(value) => updateField('lastName', value)}
            autoCapitalize="words"
            editable={!isLoading}
          />
          {formErrors.lastName && <Text style={styles.errorText}>{formErrors.lastName}</Text>}
        </View>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Email Address</Text>
        <TextInput
          style={[styles.input, formErrors.email && styles.inputError]}
          placeholder="<EMAIL>"
          value={formData.email}
          onChangeText={(value) => updateField('email', value)}
          keyboardType="email-address"
          autoCapitalize="none"
          autoCorrect={false}
          editable={!isLoading}
        />
        {formErrors.email && <Text style={styles.errorText}>{formErrors.email}</Text>}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Phone Number</Text>
        <View style={styles.phoneContainer}>
          <TouchableOpacity
            style={styles.countryCodeButton}
            onPress={() => setShowCountryDropdown(!showCountryDropdown)}
          >
            <Text style={styles.countryCodeText}>🇵🇰 +92</Text>
            <IconSymbol name="chevron.down" size={16} color={Colors.light.textSecondary} />
          </TouchableOpacity>
          <TextInput
            style={[styles.phoneInput, formErrors.phone && styles.inputError]}
            placeholder="03XX-XXXXXXX"
            value={formData.phone}
            onChangeText={(value) => updateField('phone', value)}
            keyboardType="phone-pad"
            editable={!isLoading}
            maxLength={12} // 11 digits + 1 dash
          />
        </View>

        {/* Country Code Dropdown */}
        {showCountryDropdown && (
          <View style={styles.dropdownContainer}>
            <TouchableOpacity
              style={styles.dropdownItem}
              onPress={() => {
                setCountryCode('+92');
                setShowCountryDropdown(false);
              }}
            >
              <Text style={styles.dropdownText}>🇵🇰 Pakistan (+92)</Text>
            </TouchableOpacity>
          </View>
        )}

        {formErrors.phone && <Text style={styles.errorText}>{formErrors.phone}</Text>}
        <Text style={styles.helperText}>
          Enter exactly 11 digits (e.g., 03XX-XXXXXXX)
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Password</Text>
        <View style={styles.passwordContainer}>
          <TextInput
            style={[styles.passwordInput, formErrors.password && styles.inputError]}
            placeholder="Create a strong password"
            value={formData.password}
            onChangeText={(value) => updateField('password', value)}
            secureTextEntry={!showPassword}
            autoCapitalize="none"
            autoCorrect={false}
            editable={!isLoading}
          />
          <TouchableOpacity
            onPress={() => setShowPassword(!showPassword)}
            style={styles.eyeButton}
            activeOpacity={0.7}
          >
            <IconSymbol
              name={showPassword ? "lock.open" : "lock"}
              size={18}
              color={showPassword ? '#DC143C' : '#6B7280'}
            />
          </TouchableOpacity>
        </View>
        {formErrors.password && <Text style={styles.errorText}>{formErrors.password}</Text>}

        {/* Enhanced Password Strength Indicator */}
        {formData.password.length > 0 && (
          <View style={styles.passwordStrengthContainer}>
            <View style={styles.strengthHeader}>
              <Text style={styles.strengthLabel}>Password Strength</Text>
              <Text style={[styles.strengthText, {
                color:
                  passwordStrength.score <= 1 ? '#B91C1C' :
                  passwordStrength.score <= 2 ? '#DC143C' :
                  passwordStrength.score <= 3 ? '#EF4444' :
                  passwordStrength.score <= 4 ? '#DC143C' : '#DC143C'
              }]}>
                {passwordStrength.feedback}
              </Text>
            </View>

            {/* Multi-segment strength meter */}
            <View style={styles.strengthMeterContainer}>
              {[1, 2, 3, 4, 5].map((segment) => (
                <View
                  key={segment}
                  style={[
                    styles.strengthSegment,
                    {
                      backgroundColor: passwordStrength.score >= segment
                        ? passwordStrength.score <= 1 ? '#B91C1C' :
                          passwordStrength.score <= 2 ? '#DC143C' :
                          passwordStrength.score <= 3 ? '#EF4444' :
                          passwordStrength.score <= 4 ? '#DC143C' : '#DC143C'
                        : '#e5e7eb'
                    }
                  ]}
                />
              ))}
            </View>
          </View>
        )}

        {/* Enhanced Password Requirements */}
        {formData.password.length > 0 && (
          <View style={styles.requirementsContainer}>
            <Text style={styles.requirementsTitle}>Requirements</Text>
            <View style={styles.requirementsGrid}>
              {Object.entries(passwordStrength.requirements).map(([key, met]) => (
                <View key={key} style={styles.requirementItem}>
                  <View style={[styles.requirementIcon, { backgroundColor: met ? '#DC143C' : '#e5e7eb' }]}>
                    <IconSymbol
                      name={met ? "checkmark" : "xmark"}
                      size={12}
                      color={met ? '#ffffff' : '#9ca3af'}
                    />
                  </View>
                  <Text style={[styles.requirementText, { color: met ? '#DC143C' : Colors.light.textSecondary }]}>
                    {key === 'length' && '8+ characters'}
                    {key === 'uppercase' && 'Uppercase (A-Z)'}
                    {key === 'lowercase' && 'Lowercase (a-z)'}
                    {key === 'number' && 'Number (0-9)'}
                    {key === 'special' && 'Special character'}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Confirm Password</Text>
        <View style={styles.passwordContainer}>
          <TextInput
            style={[styles.passwordInput, formErrors.confirmPassword && styles.inputError]}
            placeholder="Confirm your password"
            value={formData.confirmPassword}
            onChangeText={(value) => updateField('confirmPassword', value)}
            secureTextEntry={!showConfirmPassword}
            autoCapitalize="none"
            autoCorrect={false}
            editable={!isLoading}
          />
          <TouchableOpacity
            onPress={() => setShowConfirmPassword(!showConfirmPassword)}
            style={styles.eyeButton}
            activeOpacity={0.7}
          >
            <IconSymbol
              name={showConfirmPassword ? "eye.slash" : "eye"}
              size={20}
              color={showConfirmPassword ? '#DC143C' : '#6B7280'}
            />
          </TouchableOpacity>
        </View>
        {formErrors.confirmPassword && <Text style={styles.errorText}>{formErrors.confirmPassword}</Text>}
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Restaurant Information</Text>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Restaurant Name</Text>
        <TextInput
          style={[styles.input, formErrors.restaurantName && styles.inputError]}
          placeholder="Your Restaurant Name"
          value={formData.restaurantName}
          onChangeText={(value) => updateField('restaurantName', value)}
          editable={!isLoading}
        />
        {formErrors.restaurantName && <Text style={styles.errorText}>{formErrors.restaurantName}</Text>}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Restaurant Type</Text>
        <TouchableOpacity
          style={[
            styles.dropdownButton,
            formErrors.restaurantType && styles.inputError
          ]}
          onPress={() => setShowRestaurantTypeDropdown(!showRestaurantTypeDropdown)}
          disabled={isLoading}
        >
          <Text style={[styles.dropdownText, !formData.restaurantType && styles.placeholderText]}>
            {formData.restaurantType || 'Select restaurant type'}
          </Text>
          <IconSymbol
            name={showRestaurantTypeDropdown ? "chevron.up" : "chevron.down"}
            size={16}
            color={Colors.light.textSecondary}
          />
        </TouchableOpacity>

        {formErrors.restaurantType && (
          <Text style={styles.errorText}>{formErrors.restaurantType}</Text>
        )}

        {showRestaurantTypeDropdown && (
          <View style={styles.dropdownList}>
            <ScrollView
              style={styles.dropdownScrollView}
              nestedScrollEnabled={true}
              showsVerticalScrollIndicator={true}
            >
              {restaurantTypes.map((type) => (
                <TouchableOpacity
                  key={type}
                  style={[
                    styles.dropdownItem,
                    formData.restaurantType === type && styles.selectedDropdownItem
                  ]}
                  onPress={() => {
                    updateField('restaurantType', type);
                    setShowRestaurantTypeDropdown(false);
                  }}
                >
                  <Text style={[
                    styles.dropdownItemText,
                    formData.restaurantType === type && styles.selectedDropdownItemText
                  ]}>
                    {type}
                  </Text>
                  {formData.restaurantType === type && (
                    <IconSymbol name="checkmark" size={16} color={Colors.light.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Description (Optional)</Text>
        <TextInput
          style={styles.textArea}
          placeholder="Tell customers about your restaurant..."
          value={formData.description}
          onChangeText={(value) => updateField('description', value)}
          multiline
          numberOfLines={3}
          editable={!isLoading}
        />
      </View>

      <Text style={styles.sectionTitle}>Address</Text>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Street Address</Text>
        <TextInput
          style={[styles.input, formErrors.street && styles.inputError]}
          placeholder="123 Main Street"
          value={formData.address.street}
          onChangeText={(value) => updateField('address.street', value)}
          editable={!isLoading}
        />
        {formErrors.street && <Text style={styles.errorText}>{formErrors.street}</Text>}
      </View>

      <View style={styles.row}>
        <View style={[styles.inputContainer, { flex: 2, marginRight: 8 }]}>
          <Text style={styles.label}>City</Text>
          <TextInput
            style={[styles.input, formErrors.city && styles.inputError]}
            placeholder="City"
            value={formData.address.city}
            onChangeText={(value) => updateField('address.city', value)}
            editable={!isLoading}
          />
          {formErrors.city && <Text style={styles.errorText}>{formErrors.city}</Text>}
        </View>

        <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
          <Text style={styles.label}>State</Text>
          <TextInput
            style={[styles.input, formErrors.state && styles.inputError]}
            placeholder="State"
            value={formData.address.state}
            onChangeText={(value) => updateField('address.state', value)}
            editable={!isLoading}
          />
          {formErrors.state && <Text style={styles.errorText}>{formErrors.state}</Text>}
        </View>
      </View>


    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Review & Confirm</Text>
      <Text style={styles.stepSubtitle}>Please review your information before creating your account</Text>

      {/* Enhanced Account Summary */}
      <View style={styles.enhancedSummaryContainer}>
        <View style={styles.summaryHeader}>
          <IconSymbol name="person.circle.fill" size={24} color={Colors.light.primary} />
          <Text style={styles.enhancedSummaryTitle}>Account Summary</Text>
        </View>

        {/* Personal Information Card */}
        <View style={styles.summaryCard}>
          <View style={styles.cardHeader}>
            <IconSymbol name="person.fill" size={18} color={Colors.light.primary} />
            <Text style={styles.cardTitle}>Personal Information</Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setCurrentStep(1)}
            >
              <IconSymbol name="pencil" size={14} color={Colors.light.primary} />
              <Text style={styles.editButtonText}>Edit</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.cardContent}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Full Name</Text>
              <Text style={styles.summaryValue}>
                {formData.firstName} {formData.lastName || '(No last name)'}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Email Address</Text>
              <Text style={styles.summaryValue}>{formData.email}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Phone Number</Text>
              <Text style={styles.summaryValue}>{countryCode} {formData.phone}</Text>
            </View>
          </View>
        </View>

        {/* Restaurant Information Card */}
        <View style={styles.summaryCard}>
          <View style={styles.cardHeader}>
            <IconSymbol name="building.2.fill" size={18} color={Colors.light.primary} />
            <Text style={styles.cardTitle}>Restaurant Information</Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setCurrentStep(2)}
            >
              <IconSymbol name="pencil" size={14} color={Colors.light.primary} />
              <Text style={styles.editButtonText}>Edit</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.cardContent}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Restaurant Name</Text>
              <Text style={styles.summaryValue}>{formData.restaurantName}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Restaurant Type</Text>
              <View style={styles.typeTag}>
                <Text style={styles.typeTagText}>{formData.restaurantType}</Text>
              </View>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Description</Text>
              <Text style={styles.summaryValue}>
                {formData.description || 'No description provided'}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Address</Text>
              <Text style={styles.summaryValue}>
                {formData.address.street}, {formData.address.city}, {formData.address.state}
              </Text>
            </View>
          </View>
        </View>

        {/* Security Information Card */}
        <View style={styles.summaryCard}>
          <View style={styles.cardHeader}>
            <IconSymbol name="lock.fill" size={18} color={Colors.light.primary} />
            <Text style={styles.cardTitle}>Security</Text>
          </View>
          <View style={styles.cardContent}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Password</Text>
              <Text style={styles.summaryValue}>••••••••</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Password Strength</Text>
              <View style={styles.strengthIndicator}>
                <View style={[
                  styles.summaryStrengthBar,
                  { backgroundColor: getPasswordStrength().color }
                ]} />
                <Text style={[
                  styles.strengthText,
                  { color: getPasswordStrength().color }
                ]}>
                  {getPasswordStrength().text}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>

      {/* Terms and Conditions */}
      <View style={styles.termsSection}>
        <Text style={styles.termsTitle}>Terms & Conditions</Text>

        <TouchableOpacity
          style={styles.enhancedCheckboxContainer}
          onPress={() => updateField('acceptTerms', !formData.acceptTerms)}
        >
          <View style={[
            styles.customCheckbox,
            formData.acceptTerms && styles.customCheckboxChecked
          ]}>
            {formData.acceptTerms && (
              <IconSymbol name="checkmark" size={14} color={Colors.light.surface} />
            )}
          </View>
          <Text style={styles.checkboxText}>
            I accept the <Text style={styles.linkText}>Terms of Service</Text> and understand the platform policies
          </Text>
        </TouchableOpacity>
        {formErrors.acceptTerms && <Text style={styles.errorText}>{formErrors.acceptTerms}</Text>}

        <TouchableOpacity
          style={styles.enhancedCheckboxContainer}
          onPress={() => updateField('acceptPrivacy', !formData.acceptPrivacy)}
        >
          <View style={[
            styles.customCheckbox,
            formData.acceptPrivacy && styles.customCheckboxChecked
          ]}>
            {formData.acceptPrivacy && (
              <IconSymbol name="checkmark" size={14} color={Colors.light.surface} />
            )}
          </View>
          <Text style={styles.checkboxText}>
            I accept the <Text style={styles.linkText}>Privacy Policy</Text> and consent to data processing
          </Text>
        </TouchableOpacity>
        {formErrors.acceptPrivacy && <Text style={styles.errorText}>{formErrors.acceptPrivacy}</Text>}
      </View>

      {/* Final Confirmation */}
      <View style={styles.confirmationSection}>
        <View style={styles.confirmationIcon}>
          <IconSymbol name="checkmark.shield.fill" size={32} color={Colors.light.primary} />
        </View>
        <Text style={styles.confirmationText}>
          By creating your account, you'll be able to manage your restaurant, track orders, and grow your business with our platform.
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={Colors.light.primary} translucent={false} />

      <LinearGradient
        colors={[Colors.light.primary, Colors.light.primaryDark]}
        style={styles.gradient}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
          >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <IconSymbol name="fork.knife" size={48} color={Colors.light.primary} />
          </View>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>Join our restaurant management platform</Text>
        </View>

        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          {[1, 2, 3].map((step) => (
            <View key={step} style={styles.progressStep}>
              <View style={[
                styles.progressCircle,
                currentStep >= step && styles.progressCircleActive
              ]}>
                <Text style={[
                  styles.progressText,
                  currentStep >= step && styles.progressTextActive
                ]}>
                  {step}
                </Text>
              </View>
              {step < 3 && (
                <View style={[
                  styles.progressLine,
                  currentStep > step && styles.progressLineActive
                ]} />
              )}
            </View>
          ))}
        </View>

        {/* Form Steps */}
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}

        {/* Error Message */}
        {error && (
          <View style={styles.errorContainer}>
            <IconSymbol name="exclamationmark.triangle" size={16} color={Colors.light.error} />
            <Text style={styles.errorMessage}>{error}</Text>
          </View>
        )}

        {/* Navigation Buttons */}
        <View style={styles.buttonContainer}>
          {currentStep > 1 && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBack}
              disabled={isLoading}
            >
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              currentStep === 1 && styles.nextButtonFull,
              isLoading && styles.nextButtonDisabled
            ]}
            onPress={currentStep === 3 ? handleRegister : handleNext}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color={Colors.light.textInverse} size="small" />
            ) : (
              <Text style={styles.nextButtonText}>
                {currentStep === 3 ? 'Create Account' : 'Next'}
              </Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Continue with Google */}
        <View style={styles.socialSection}>
          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>or</Text>
            <View style={styles.dividerLine} />
          </View>

          <TouchableOpacity style={styles.googleButton} onPress={() => Alert.alert('Coming Soon', 'Google Sign-In will be available soon!')}>
            <IconSymbol name="globe" size={20} color="#DC143C" />
            <Text style={styles.googleButtonText}>Continue with Google</Text>
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>Already have an account? </Text>
          <TouchableOpacity onPress={() => router.replace('/login')}>
            <Text style={styles.signInText}>Sign In</Text>
          </TouchableOpacity>
        </View>

          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textInverse,
    textAlign: 'center',
    opacity: 0.9,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressCircleActive: {
    backgroundColor: Colors.light.primary,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textSecondary,
  },
  progressTextActive: {
    color: Colors.light.textInverse,
  },
  progressLine: {
    width: 40,
    height: 2,
    backgroundColor: Colors.light.border,
    marginHorizontal: 8,
  },
  progressLineActive: {
    backgroundColor: Colors.light.primary,
  },
  stepContainer: {
    marginBottom: 30,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 20,
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 6,
  },
  input: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  inputError: {
    borderColor: Colors.light.error,
  },
  textArea: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
    height: 80,
    textAlignVertical: 'top',
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  eyeButton: {
    padding: 10,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    marginRight: 6,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 40,
    minHeight: 40,
    borderWidth: 1.5,
    borderColor: Colors.light.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.error,
    marginTop: 4,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkboxText: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 12,
    flex: 1,
  },
  linkText: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  summaryContainer: {
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  summaryText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF2F2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorMessage: {
    fontSize: 14,
    color: Colors.light.error,
    marginLeft: 8,
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginBottom: 30,
  },
  backButton: {
    flex: 1,
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  nextButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButtonFull: {
    flex: 1,
    marginRight: 0,
  },
  nextButtonDisabled: {
    opacity: 0.6,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  footerText: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  signInText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  // Phone number styles
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  countryCodeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRightWidth: 1,
    borderRightColor: Colors.light.border,
  },
  countryCodeText: {
    fontSize: 16,
    color: Colors.light.text,
    marginRight: 4,
  },
  phoneInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: 4,
  },
  // Dropdown styles
  dropdownContainer: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    zIndex: 1000,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  dropdownText: {
    fontSize: 16,
    color: Colors.light.text,
    flex: 1,
  },
  // Enhanced Password strength styles
  passwordStrengthContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  strengthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  strengthLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: Colors.light.text,
  },
  strengthMeterContainer: {
    flexDirection: 'row',
    gap: 4,
    marginBottom: 8,
  },
  strengthSegment: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#e5e7eb',
  },
  strengthMeter: {
    height: 4,
    backgroundColor: Colors.light.border,
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 4,
  },
  strengthBar: {
    height: '100%',
    borderRadius: 2,
  },
  strengthText: {
    fontSize: 12,
    fontWeight: '600',
  },
  requirementsContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  requirementsTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  requirementsGrid: {
    flexDirection: 'column',
    gap: 8,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  requirementIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  requirementText: {
    fontSize: 13,
    flex: 1,
  },

  // Restaurant Type Dropdown Styles
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    backgroundColor: Colors.light.surface,
    minHeight: 48,
  },
  placeholderText: {
    color: Colors.light.textSecondary,
  },
  dropdownList: {
    marginTop: 4,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    backgroundColor: Colors.light.surface,
    height: 192, // 4 items × 48px per item = 192px
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dropdownScrollView: {
    flex: 1,
  },
  selectedDropdownItem: {
    backgroundColor: Colors.light.backgroundSecondary,
  },
  dropdownItemText: {
    fontSize: 16,
    color: Colors.light.text,
    flex: 1,
  },
  selectedDropdownItemText: {
    color: Colors.light.primary,
    fontWeight: '600',
  },

  // Enhanced Step 3 Styles
  stepSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  enhancedSummaryContainer: {
    marginBottom: 24,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  enhancedSummaryTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    marginLeft: 8,
  },
  summaryCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    backgroundColor: Colors.light.backgroundSecondary,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 8,
    flex: 1,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  editButtonText: {
    fontSize: 12,
    color: Colors.light.primary,
    fontWeight: '600',
    marginLeft: 4,
  },
  cardContent: {
    padding: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
    flex: 1,
  },
  summaryValue: {
    fontSize: 14,
    color: Colors.light.text,
    fontWeight: '400',
    flex: 2,
    textAlign: 'right',
  },
  typeTag: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    alignSelf: 'flex-end',
  },
  typeTagText: {
    fontSize: 12,
    color: Colors.light.surface,
    fontWeight: '600',
  },
  strengthIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 2,
    justifyContent: 'flex-end',
  },
  summaryStrengthBar: {
    width: 60,
    height: 4,
    borderRadius: 2,
    marginRight: 8,
  },
  termsSection: {
    marginBottom: 24,
  },
  termsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  enhancedCheckboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  customCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  customCheckboxChecked: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  confirmationSection: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  confirmationIcon: {
    marginBottom: 12,
  },
  confirmationText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  socialSection: {
    marginTop: 20,
    marginBottom: 20,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.light.border,
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  googleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  googleButtonText: {
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '600',
    marginLeft: 8,
  },
});

// Create a Restaurant Registration screen using React Native and Expo
// Required fields: restaurantName, ownerName, email, phoneNumber, password, confirmPassword, location
// Use react-native-paper or native components with clean and responsive UI
// Add validation:
// - Email format + uniqueness check (via GET /check-email-exists)
// - Restaurant name uniqueness check (via GET /check-restaurant-name)
// - Password match and strength
// - Phone number regex check
// Add auto-complete for location using expo-location + Google Places
// Suggest alternative restaurant names if taken
// Add password visibility toggle
// Add loading spinner for API checks
// Prepare the data for submission via POST /register-restaurant
// Keep code modular: split into input components and custom hooks
