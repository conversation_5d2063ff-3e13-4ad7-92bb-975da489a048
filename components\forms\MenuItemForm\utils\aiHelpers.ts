import { MenuItem } from '../types';

// Mock AI service - In production, this would connect to OpenAI, Claude, or similar
class AIService {
  private static instance: AIService;
  private apiKey: string = '';
  private baseUrl: string = 'https://api.openai.com/v1';

  static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  setApiKey(key: string) {
    this.apiKey = key;
  }

  async generateText(prompt: string, maxTokens: number = 150): Promise<string> {
    // Mock implementation - replace with actual AI service call
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate AI-generated content based on prompt keywords
        if (prompt.includes('description')) {
          resolve(this.generateMockDescription(prompt));
        } else if (prompt.includes('SEO')) {
          resolve(this.generateMockSEOContent(prompt));
        } else {
          resolve('AI-generated content would appear here.');
        }
      }, 1500); // Simulate API delay
    });
  }

  private generateMockDescription(prompt: string): string {
    const templates = [
      "A delicious and carefully crafted dish that combines fresh, high-quality ingredients to create an unforgettable dining experience. Our chefs have perfected this recipe to deliver exceptional flavors that will satisfy your taste buds and leave you craving more.",
      "Experience the perfect harmony of flavors in this expertly prepared dish. Made with premium ingredients and traditional cooking techniques, this item represents the finest in culinary craftsmanship and attention to detail.",
      "Indulge in this exquisite creation that showcases the best of our kitchen's expertise. Each bite delivers a symphony of tastes and textures, carefully balanced to provide a memorable and satisfying meal.",
    ];
    
    return templates[Math.floor(Math.random() * templates.length)];
  }

  private generateMockSEOContent(prompt: string): string {
    return "Optimized content with relevant keywords, proper structure, and enhanced readability for better search engine visibility and customer engagement.";
  }
}

export async function generateAIDescription(formData: Partial<MenuItem>): Promise<string> {
  const ai = AIService.getInstance();
  
  // Build context from form data
  const context = buildContextFromFormData(formData);
  
  const prompt = `Generate an appetizing and detailed description for a restaurant menu item with the following details:
  
  ${context}
  
  Requirements:
  - 150-250 words
  - Highlight key ingredients and flavors
  - Use sensory language that makes the dish sound appealing
  - Include preparation method hints
  - Mention dietary considerations if applicable
  - Write in an engaging, professional tone
  - Avoid overly complex language
  - Focus on what makes this dish special
  
  Generate a description that would entice customers to order this item:`;

  try {
    const description = await ai.generateText(prompt, 250);
    return description;
  } catch (error) {
    console.error('Failed to generate AI description:', error);
    throw new Error('Unable to generate description. Please try again.');
  }
}

export async function optimizeForSEO(formData: Partial<MenuItem>): Promise<Partial<MenuItem>> {
  const ai = AIService.getInstance();
  
  const context = buildContextFromFormData(formData);
  
  const prompt = `Optimize the following menu item for SEO and customer engagement:
  
  ${context}
  
  Provide optimizations for:
  1. Item name (if needed) - make it more searchable and appealing
  2. Description - optimize for relevant keywords while maintaining readability
  3. Tags - suggest relevant tags for better categorization
  4. Meta information for online ordering platforms
  
  Focus on:
  - Local SEO keywords
  - Food-related search terms
  - Dietary preference keywords
  - Cuisine type keywords
  - Preparation method keywords
  
  Return suggestions in JSON format:`;

  try {
    const optimizations = await ai.generateText(prompt, 300);
    
    // Parse AI response and return optimized data
    // In a real implementation, you'd parse the JSON response
    const optimizedData: Partial<MenuItem> = {
      ...formData,
      // Mock optimizations
      description: formData.description ? 
        `${formData.description} This expertly crafted dish features premium ingredients and traditional cooking methods, perfect for food lovers seeking authentic flavors and exceptional quality.` :
        formData.description,
      tags: [
        ...(formData.tags || []),
        'premium-quality',
        'chef-special',
        'locally-sourced',
        'fresh-ingredients'
      ].slice(0, 15), // Limit to 15 tags
      seoOptimized: true,
    };

    return optimizedData;
  } catch (error) {
    console.error('Failed to optimize for SEO:', error);
    throw new Error('Unable to optimize for SEO. Please try again.');
  }
}

export async function generateIngredientSuggestions(
  itemName: string,
  category: string,
  cuisine?: string
): Promise<string[]> {
  const ai = AIService.getInstance();
  
  const prompt = `Suggest common ingredients for a ${category} dish called "${itemName}"${cuisine ? ` in ${cuisine} cuisine` : ''}. 
  
  Provide 8-12 typical ingredients that would be used in this dish. Focus on:
  - Main proteins or base ingredients
  - Common vegetables and aromatics
  - Typical seasonings and spices
  - Cooking oils or fats
  - Garnishes or finishing touches
  
  Return as a simple comma-separated list:`;

  try {
    const suggestions = await ai.generateText(prompt, 100);
    
    // Mock ingredient suggestions based on category
    const mockSuggestions: Record<string, string[]> = {
      'appetizer': ['olive oil', 'garlic', 'onion', 'herbs', 'cheese', 'bread', 'tomatoes', 'lettuce'],
      'main course': ['protein', 'vegetables', 'rice', 'pasta', 'sauce', 'seasonings', 'oil', 'herbs'],
      'dessert': ['flour', 'sugar', 'eggs', 'butter', 'vanilla', 'chocolate', 'cream', 'fruits'],
      'beverage': ['water', 'ice', 'fruits', 'herbs', 'sweetener', 'citrus', 'tea', 'coffee'],
    };

    return mockSuggestions[category.toLowerCase()] || mockSuggestions['main course'];
  } catch (error) {
    console.error('Failed to generate ingredient suggestions:', error);
    return [];
  }
}

export async function generateNutritionalEstimate(
  ingredients: any[],
  servingSize: string
): Promise<any> {
  // Mock nutritional calculation
  // In production, this would use a nutritional database API
  const baseCalories = ingredients.length * 50;
  const variation = Math.random() * 100;
  
  return {
    calories: Math.round(baseCalories + variation),
    protein: Math.round((baseCalories * 0.15) / 4), // 15% of calories from protein
    carbohydrates: Math.round((baseCalories * 0.50) / 4), // 50% from carbs
    fat: Math.round((baseCalories * 0.35) / 9), // 35% from fat
    fiber: Math.round(ingredients.length * 2),
    sugar: Math.round(ingredients.length * 3),
    sodium: Math.round(ingredients.length * 100),
    cholesterol: Math.round(ingredients.length * 10),
    servingSize,
    servingsPerContainer: 1,
  };
}

export async function generatePricingSuggestions(
  formData: Partial<MenuItem>,
  competitorData?: any[]
): Promise<{
  suggestedPrice: number;
  priceRange: { min: number; max: number };
  reasoning: string;
}> {
  const ai = AIService.getInstance();
  
  const context = buildContextFromFormData(formData);
  const competitorContext = competitorData ? 
    `Competitor prices: ${competitorData.map(c => `${c.name}: $${c.price}`).join(', ')}` : 
    'No competitor data available';
  
  const prompt = `Suggest pricing for this menu item:
  
  ${context}
  ${competitorContext}
  
  Consider:
  - Ingredient costs and preparation complexity
  - Category typical pricing
  - Market positioning
  - Profit margins (aim for 60-70%)
  - Local market conditions
  
  Provide pricing recommendation with reasoning:`;

  try {
    await ai.generateText(prompt, 200);
    
    // Mock pricing calculation
    const basePrice = calculateBasePrice(formData);
    const marketAdjustment = competitorData ? 
      calculateMarketAdjustment(basePrice, competitorData) : 0;
    
    const suggestedPrice = basePrice + marketAdjustment;
    
    return {
      suggestedPrice: Math.round(suggestedPrice * 100) / 100,
      priceRange: {
        min: Math.round((suggestedPrice * 0.8) * 100) / 100,
        max: Math.round((suggestedPrice * 1.2) * 100) / 100,
      },
      reasoning: `Based on ingredient costs, preparation complexity, and market analysis, this price provides optimal value while maintaining healthy profit margins.`,
    };
  } catch (error) {
    console.error('Failed to generate pricing suggestions:', error);
    throw new Error('Unable to generate pricing suggestions. Please try again.');
  }
}

function buildContextFromFormData(formData: Partial<MenuItem>): string {
  const parts = [];
  
  if (formData.name) parts.push(`Name: ${formData.name}`);
  if (formData.category) parts.push(`Category: ${formData.category}`);
  if (formData.description) parts.push(`Current description: ${formData.description}`);
  if (formData.ingredients) {
    parts.push(`Ingredients: ${formData.ingredients.map(i => i.name).join(', ')}`);
  }
  if (formData.preparationTime) {
    parts.push(`Preparation time: ${formData.preparationTime} minutes`);
  }
  if (formData.tags) parts.push(`Tags: ${formData.tags.join(', ')}`);
  
  return parts.join('\n');
}

function calculateBasePrice(formData: Partial<MenuItem>): number {
  let basePrice = 10; // Default base price
  
  // Adjust based on category
  const categoryMultipliers: Record<string, number> = {
    'appetizer': 0.6,
    'main course': 1.0,
    'dessert': 0.7,
    'beverage': 0.4,
  };
  
  const multiplier = categoryMultipliers[formData.category?.toLowerCase() || 'main course'] || 1.0;
  basePrice *= multiplier;
  
  // Adjust based on ingredients count
  if (formData.ingredients) {
    basePrice += formData.ingredients.length * 1.5;
  }
  
  // Adjust based on preparation time
  if (formData.preparationTime) {
    basePrice += (formData.preparationTime / 10) * 2;
  }
  
  return basePrice;
}

function calculateMarketAdjustment(basePrice: number, competitorData: any[]): number {
  if (!competitorData.length) return 0;
  
  const avgCompetitorPrice = competitorData.reduce((sum, c) => sum + c.price, 0) / competitorData.length;
  const difference = avgCompetitorPrice - basePrice;
  
  // Adjust by 30% of the difference to stay competitive but not match exactly
  return difference * 0.3;
}

export async function generateCustomizationSuggestions(
  itemName: string,
  category: string,
  ingredients: any[]
): Promise<any[]> {
  // Mock customization suggestions
  const baseSuggestions = [
    {
      name: 'Size Options',
      options: ['Small', 'Regular', 'Large'],
      type: 'single-select',
      required: true,
    },
    {
      name: 'Spice Level',
      options: ['Mild', 'Medium', 'Hot', 'Extra Hot'],
      type: 'single-select',
      required: false,
    },
    {
      name: 'Add-ons',
      options: ['Extra Cheese', 'Avocado', 'Bacon', 'Extra Sauce'],
      type: 'multi-select',
      required: false,
    },
  ];
  
  return baseSuggestions;
}
