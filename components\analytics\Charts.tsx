import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { PremiumColors, PremiumShadows } from '@/constants/PremiumTheme';

const { width: screenWidth } = Dimensions.get('window');

interface LineChartProps {
  data: Array<{ label: string; value: number }>;
  height?: number;
  showGrid?: boolean;
  animated?: boolean;
  color?: string;
  title?: string;
}

interface BarChartProps {
  data: Array<{ label: string; value: number; color?: string }>;
  height?: number;
  maxValue?: number;
  animated?: boolean;
  title?: string;
}

interface DonutChartProps {
  data: Array<{ label: string; value: number; color: string }>;
  size?: number;
  innerRadius?: number;
  title?: string;
  centerText?: string;
}

export const LineChart: React.FC<LineChartProps> = ({
  data,
  height = 200,
  showGrid = true,
  animated = true,
  color = PremiumColors.primary,
  title,
}) => {
  const animatedValues = useRef(data.map(() => new Animated.Value(0))).current;
  const chartWidth = screenWidth - 80;
  const chartHeight = height - 60;

  useEffect(() => {
    if (animated) {
      const animations = animatedValues.map((animValue, index) =>
        Animated.timing(animValue, {
          toValue: 1,
          duration: 1000 + index * 100,
          useNativeDriver: false,
        })
      );
      Animated.stagger(100, animations).start();
    } else {
      animatedValues.forEach(animValue => animValue.setValue(1));
    }
  }, [data]);

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const valueRange = maxValue - minValue;

  const getPointPosition = (index: number, value: number) => {
    const x = (index / (data.length - 1)) * chartWidth;
    const y = chartHeight - ((value - minValue) / valueRange) * chartHeight;
    return { x, y };
  };

  const createPath = () => {
    if (data.length === 0) return '';
    
    let path = '';
    data.forEach((point, index) => {
      const { x, y } = getPointPosition(index, point.value);
      if (index === 0) {
        path += `M${x},${y}`;
      } else {
        path += ` L${x},${y}`;
      }
    });
    return path;
  };

  return (
    <View style={[styles.chartContainer, { height }]}>
      {title && <Text style={styles.chartTitle}>{title}</Text>}
      
      <View style={styles.chartArea}>
        {/* Grid Lines */}
        {showGrid && (
          <View style={styles.gridContainer}>
            {[0, 1, 2, 3, 4].map(i => (
              <View
                key={i}
                style={[
                  styles.gridLine,
                  { top: (i / 4) * chartHeight }
                ]}
              />
            ))}
          </View>
        )}

        {/* Chart Points and Line */}
        <View style={styles.chartContent}>
          {data.map((point, index) => {
            const { x, y } = getPointPosition(index, point.value);
            return (
              <Animated.View
                key={index}
                style={[
                  styles.chartPoint,
                  {
                    left: x - 4,
                    top: y - 4,
                    backgroundColor: color,
                    opacity: animatedValues[index],
                  }
                ]}
              />
            );
          })}
        </View>

        {/* X-axis Labels */}
        <View style={styles.xAxisContainer}>
          {data.map((point, index) => (
            <Text key={index} style={styles.xAxisLabel}>
              {point.label}
            </Text>
          ))}
        </View>
      </View>
    </View>
  );
};

export const BarChart: React.FC<BarChartProps> = ({
  data,
  height = 200,
  maxValue,
  animated = true,
  title,
}) => {
  const animatedValues = useRef(data.map(() => new Animated.Value(0))).current;
  const chartWidth = screenWidth - 80;
  const chartHeight = height - 80;
  const barWidth = (chartWidth - (data.length - 1) * 8) / data.length;

  const max = maxValue || Math.max(...data.map(d => d.value));

  useEffect(() => {
    if (animated) {
      const animations = animatedValues.map((animValue, index) =>
        Animated.timing(animValue, {
          toValue: 1,
          duration: 800 + index * 100,
          useNativeDriver: false,
        })
      );
      Animated.stagger(100, animations).start();
    } else {
      animatedValues.forEach(animValue => animValue.setValue(1));
    }
  }, [data]);

  return (
    <View style={[styles.chartContainer, { height }]}>
      {title && <Text style={styles.chartTitle}>{title}</Text>}
      
      <View style={styles.barChartArea}>
        <View style={styles.barsContainer}>
          {data.map((item, index) => {
            const barHeight = (item.value / max) * chartHeight;
            return (
              <View key={index} style={styles.barColumn}>
                <Animated.View
                  style={[
                    styles.bar,
                    {
                      width: barWidth,
                      height: animatedValues[index].interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, barHeight],
                      }),
                      backgroundColor: item.color || PremiumColors.primary,
                    }
                  ]}
                />
                <Text style={styles.barLabel}>{item.label}</Text>
              </View>
            );
          })}
        </View>
      </View>
    </View>
  );
};

export const DonutChart: React.FC<DonutChartProps> = ({
  data,
  size = 120,
  innerRadius = 40,
  title,
  centerText,
}) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  const radius = size / 2;
  const strokeWidth = radius - innerRadius;

  return (
    <View style={[styles.chartContainer, { height: size + 60 }]}>
      {title && <Text style={styles.chartTitle}>{title}</Text>}
      
      <View style={styles.donutContainer}>
        <View style={[styles.donutChart, { width: size, height: size }]}>
          {/* Donut segments would be implemented with SVG or custom drawing */}
          <View style={[
            styles.donutPlaceholder,
            { 
              width: size, 
              height: size, 
              borderRadius: size / 2,
              borderWidth: strokeWidth,
              borderColor: PremiumColors.primary,
            }
          ]}>
            {centerText && (
              <View style={styles.donutCenter}>
                <Text style={styles.donutCenterText}>{centerText}</Text>
              </View>
            )}
          </View>
        </View>
        
        {/* Legend */}
        <View style={styles.donutLegend}>
          {data.map((item, index) => (
            <View key={index} style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: item.color }]} />
              <Text style={styles.legendText}>
                {item.label}: {((item.value / total) * 100).toFixed(1)}%
              </Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

export const OrderFlowChart: React.FC = () => {
  const flowData = [
    { stage: 'Orders Placed', value: 847, percentage: 100 },
    { stage: 'Orders Confirmed', value: 834, percentage: 98 },
    { stage: 'In Preparation', value: 782, percentage: 92 },
    { stage: 'Ready for Pickup', value: 721, percentage: 85 },
    { stage: 'Delivered', value: 698, percentage: 82 },
    { stage: 'Customer Rated', value: 567, percentage: 67 },
  ];

  return (
    <View style={styles.flowChartContainer}>
      <Text style={styles.chartTitle}>📋 Order Flow Analysis</Text>
      
      {flowData.map((stage, index) => (
        <View key={index} style={styles.flowStage}>
          <View style={styles.flowStageInfo}>
            <Text style={styles.flowStageTitle}>{stage.stage}</Text>
            <Text style={styles.flowStageValue}>
              {stage.value} ({stage.percentage}%)
            </Text>
          </View>
          <View style={styles.flowBarContainer}>
            <View 
              style={[
                styles.flowBar,
                { 
                  width: `${stage.percentage}%`,
                  backgroundColor: stage.percentage >= 90 ? PremiumColors.success :
                                 stage.percentage >= 70 ? PremiumColors.warning :
                                 PremiumColors.danger
                }
              ]} 
            />
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  chartContainer: {
    backgroundColor: PremiumColors.white,
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    ...PremiumShadows.medium,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 16,
    textAlign: 'center',
  },
  chartArea: {
    position: 'relative',
  },
  gridContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  gridLine: {
    position: 'absolute',
    width: '100%',
    height: 1,
    backgroundColor: PremiumColors.grayMedium,
    opacity: 0.3,
  },
  chartContent: {
    position: 'relative',
  },
  chartPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: PremiumColors.white,
  },
  xAxisContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  xAxisLabel: {
    fontSize: 12,
    color: PremiumColors.grayDark,
    textAlign: 'center',
  },
  
  // Bar Chart Styles
  barChartArea: {
    flex: 1,
  },
  barsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: '100%',
    paddingBottom: 30,
  },
  barColumn: {
    alignItems: 'center',
  },
  bar: {
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    marginBottom: 8,
  },
  barLabel: {
    fontSize: 12,
    color: PremiumColors.grayDark,
    textAlign: 'center',
  },
  
  // Donut Chart Styles
  donutContainer: {
    alignItems: 'center',
  },
  donutChart: {
    position: 'relative',
    marginBottom: 16,
  },
  donutPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  donutCenter: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  donutCenterText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: PremiumColors.black,
    textAlign: 'center',
  },
  donutLegend: {
    gap: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    fontSize: 12,
    color: PremiumColors.grayDark,
  },
  
  // Flow Chart Styles
  flowChartContainer: {
    backgroundColor: PremiumColors.white,
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    ...PremiumShadows.medium,
  },
  flowStage: {
    marginBottom: 12,
  },
  flowStageInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  flowStageTitle: {
    fontSize: 14,
    color: PremiumColors.black,
    fontWeight: '500',
  },
  flowStageValue: {
    fontSize: 14,
    color: PremiumColors.grayDark,
    fontWeight: '600',
  },
  flowBarContainer: {
    height: 8,
    backgroundColor: PremiumColors.grayMedium,
    borderRadius: 4,
    overflow: 'hidden',
  },
  flowBar: {
    height: '100%',
    borderRadius: 4,
  },
});
