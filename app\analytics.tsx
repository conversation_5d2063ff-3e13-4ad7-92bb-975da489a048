import { PremiumColors, PremiumShadows } from '@/constants/PremiumTheme';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
    RefreshControl,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { AppIcon, RestaurantIcons } from '../components/ui/AppIcon';

// Mock Analytics Data
const mockAnalyticsData = {
  revenue: {
    today: 2847,
    todayChange: 12,
    week: 18432,
    weekChange: 8,
    month: 67891,
    monthChange: -3,
    year: 789234,
    yearChange: 15,
  },
  orders: {
    today: 127,
    todayChange: 15,
    completionRate: 94,
    avgPrepTime: 18,
    targetPrepTime: 15,
    pending: 8,
    preparing: 15,
    ready: 4,
    delivered: 100,
  },
  customers: {
    newVsReturning: { new: 32, returning: 95 },
    satisfaction: 4.7,
    reviewCount: 234,
    avgLifetimeValue: 247,
    retentionRate: 68,
    retentionChange: 5,
  },
};

const revenueChartData = [
  { period: 'Jan', value: 45000, change: 12 },
  { period: 'Feb', value: 52000, change: 15 },
  { period: 'Mar', value: 48000, change: -8 },
  { period: 'Apr', value: 61000, change: 27 },
  { period: 'May', value: 58000, change: -5 },
  { period: 'Jun', value: 67000, change: 15 },
];

const orderTrendData = [
  { period: 'Mon', orders: 89, revenue: 2340 },
  { period: 'Tue', orders: 127, revenue: 3120 },
  { period: 'Wed', orders: 156, revenue: 3890 },
  { period: 'Thu', orders: 134, revenue: 3456 },
  { period: 'Fri', orders: 198, revenue: 4567 },
  { period: 'Sat', orders: 234, revenue: 5234 },
  { period: 'Sun', orders: 167, revenue: 3987 },
];

export default function AnalyticsScreen() {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('today');
  const [selectedChart, setSelectedChart] = useState('revenue');

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 2000);
  }, []);

  const formatCurrency = (amount: number) => {
    return `PKR ${amount.toLocaleString()}`;
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? PremiumColors.success : PremiumColors.error;
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? RestaurantIcons.dashboard.trending_up : RestaurantIcons.dashboard.trending_down;
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity 
        style={styles.backButton}
        onPress={() => router.back()}
      >
        <AppIcon 
          name={RestaurantIcons.nav.back} 
          size="md" 
          color="white" 
        />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Analytics Dashboard</Text>
      <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
        <AppIcon 
          name={RestaurantIcons.actions.refresh} 
          size="md" 
          color="white" 
        />
      </TouchableOpacity>
    </View>
  );

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {['today', 'week', 'month', 'year'].map((period) => (
        <TouchableOpacity
          key={period}
          style={[
            styles.periodButton,
            selectedPeriod === period && styles.periodButtonActive
          ]}
          onPress={() => setSelectedPeriod(period)}
        >
          <Text style={[
            styles.periodButtonText,
            selectedPeriod === period && styles.periodButtonTextActive
          ]}>
            {period.charAt(0).toUpperCase() + period.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderKPICard = (title: string, value: string, change: number, icon: string) => (
    <View style={styles.kpiCard}>
      <LinearGradient
        colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,0.85)']}
        style={styles.kpiCardGradient}
      >
        <View style={styles.kpiHeader}>
          <AppIcon name={icon} size="sm" color={PremiumColors.primary} />
          <Text style={styles.kpiTitle}>{title}</Text>
        </View>
        <Text style={styles.kpiValue}>{value}</Text>
        <View style={styles.kpiChange}>
          <AppIcon 
            name={getChangeIcon(change)} 
            size="xs" 
            color={getChangeColor(change)} 
          />
          <Text style={[styles.kpiChangeText, { color: getChangeColor(change) }]}>
            {change > 0 ? '+' : ''}{change}%
          </Text>
        </View>
      </LinearGradient>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient
        colors={[PremiumColors.primary, PremiumColors.primaryDark]}
        style={styles.background}
      >
        {renderHeader()}
        
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {renderPeriodSelector()}

          {/* KPI Overview */}
          <View style={styles.kpiSection}>
            <Text style={styles.sectionTitle}>Key Performance Indicators</Text>
            <View style={styles.kpiGrid}>
              {renderKPICard(
                'Revenue',
                formatCurrency(mockAnalyticsData.revenue[selectedPeriod as keyof typeof mockAnalyticsData.revenue] as number),
                mockAnalyticsData.revenue[`${selectedPeriod}Change` as keyof typeof mockAnalyticsData.revenue] as number,
                RestaurantIcons.dashboard.revenue
              )}
              {renderKPICard(
                'Orders',
                mockAnalyticsData.orders.today.toString(),
                mockAnalyticsData.orders.todayChange,
                RestaurantIcons.dashboard.orders
              )}
            </View>
            <View style={styles.kpiGrid}>
              {renderKPICard(
                'Avg Prep Time',
                `${mockAnalyticsData.orders.avgPrepTime}min`,
                mockAnalyticsData.orders.avgPrepTime <= mockAnalyticsData.orders.targetPrepTime ? 5 : -8,
                RestaurantIcons.dashboard.time
              )}
              {renderKPICard(
                'Completion Rate',
                `${mockAnalyticsData.orders.completionRate}%`,
                2,
                RestaurantIcons.status.success
              )}
            </View>
          </View>

          {/* Chart Section */}
          <View style={styles.chartSection}>
            <View style={styles.chartHeader}>
              <Text style={styles.sectionTitle}>Trends</Text>
              <View style={styles.chartSelector}>
                {['revenue', 'orders'].map((chart) => (
                  <TouchableOpacity
                    key={chart}
                    style={[
                      styles.chartButton,
                      selectedChart === chart && styles.chartButtonActive
                    ]}
                    onPress={() => setSelectedChart(chart)}
                  >
                    <Text style={[
                      styles.chartButtonText,
                      selectedChart === chart && styles.chartButtonTextActive
                    ]}>
                      {chart.charAt(0).toUpperCase() + chart.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Simple Chart Representation */}
            <View style={styles.chartContainer}>
              <LinearGradient
                colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,0.85)']}
                style={styles.chartCard}
              >
                <Text style={styles.chartTitle}>
                  {selectedChart === 'revenue' ? 'Revenue Trend' : 'Order Trend'}
                </Text>
                <View style={styles.chartPlaceholder}>
                  <Text style={styles.chartPlaceholderText}>
                    📊 {selectedChart === 'revenue' ? 'Revenue' : 'Orders'} Chart
                  </Text>
                  <Text style={styles.chartSubtext}>
                    Interactive charts would be rendered here
                  </Text>
                </View>
              </LinearGradient>
            </View>
          </View>

          {/* Bottom spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PremiumColors.background,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    padding: 4,
    marginBottom: 24,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: 'rgba(255,255,255,0.9)',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.8)',
  },
  periodButtonTextActive: {
    color: PremiumColors.primary,
  },
  kpiSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    marginBottom: 16,
  },
  kpiGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  kpiCard: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
    ...PremiumShadows.medium,
  },
  kpiCardGradient: {
    padding: 16,
  },
  kpiHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  kpiTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: PremiumColors.textSecondary,
    marginLeft: 8,
  },
  kpiValue: {
    fontSize: 20,
    fontWeight: '700',
    color: PremiumColors.text,
    marginBottom: 4,
  },
  kpiChange: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  kpiChangeText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  chartSection: {
    marginBottom: 32,
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartSelector: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 8,
    padding: 2,
  },
  chartButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  chartButtonActive: {
    backgroundColor: 'rgba(255,255,255,0.9)',
  },
  chartButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.8)',
  },
  chartButtonTextActive: {
    color: PremiumColors.primary,
  },
  chartContainer: {
    borderRadius: 16,
    overflow: 'hidden',
    ...PremiumShadows.medium,
  },
  chartCard: {
    padding: 20,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: PremiumColors.text,
    marginBottom: 16,
  },
  chartPlaceholder: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'rgba(0,0,0,0.1)',
    borderStyle: 'dashed',
  },
  chartPlaceholderText: {
    fontSize: 18,
    fontWeight: '600',
    color: PremiumColors.textSecondary,
    marginBottom: 8,
  },
  chartSubtext: {
    fontSize: 14,
    color: PremiumColors.textSecondary,
    opacity: 0.7,
  },
  bottomSpacing: {
    height: 40,
  },
});
