import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { CustomizationGroup, CustomizationOption, FormValidationError, MenuItem } from '../../types';

interface CustomizationsStepProps {
  formData: Partial<MenuItem>;
  onUpdateField: (field: string, value: any) => void;
  onValidateField: (field: string) => Promise<FormValidationError[]>;
  errors: FormValidationError[];
}

export function CustomizationsStep({
  formData,
  onUpdateField,
  onValidateField,
  errors,
}: CustomizationsStepProps) {
  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);

  const fadeAnimation = useSharedValue(0);
  const slideAnimation = useSharedValue(0);

  useEffect(() => {
    fadeAnimation.value = withTiming(1, { duration: 500 });
    slideAnimation.value = withSpring(1, { damping: 20, stiffness: 100 });
    
    // Initialize customizations if not set
    if (!formData.customizations) {
      onUpdateField('customizations', []);
    }
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
    transform: [{ translateY: (1 - slideAnimation.value) * 50 }],
  }));

  const getFieldError = (fieldName: string) => {
    return errors.find(error => error.field === fieldName);
  };

  // Removed dropdown functionality - using simple TextInput instead

  const handleAddCustomizationGroup = () => {
    const newGroup: CustomizationGroup = {
      id: Date.now().toString(),
      name: 'New Customization Group',
      description: '',
      isRequired: false,
      allowMultiple: false,
      maxSelections: 1,
      minSelections: 0,
      options: [],
      displayOrder: (formData.customizations?.length || 0) + 1,
    };

    const currentCustomizations = formData.customizations || [];
    onUpdateField('customizations', [...currentCustomizations, newGroup]);
    onValidateField('customizations');

    // Expand the new group
    setExpandedGroups([...expandedGroups, newGroup.id]);
  };

  const handleAddSuggestedGroup = (groupType: string) => {
    const suggestedGroups = {
      'Size Options': {
        name: 'Size Options',
        description: 'Choose your preferred size',
        isRequired: true,
        allowMultiple: false,
        maxSelections: 1,
        minSelections: 1,
        options: [
          { id: '1', name: 'Small', description: 'Regular size', priceModifier: 0, isDefault: true, isAvailable: true },
          { id: '2', name: 'Medium', description: 'Medium size', priceModifier: 50, isDefault: false, isAvailable: true },
          { id: '3', name: 'Large', description: 'Large size', priceModifier: 100, isDefault: false, isAvailable: true },
        ]
      },
      'Extra Toppings': {
        name: 'Extra Toppings',
        description: 'Add extra toppings to your order',
        isRequired: false,
        allowMultiple: true,
        maxSelections: 5,
        minSelections: 0,
        options: [
          { id: '1', name: 'Extra Cheese', description: 'Additional cheese topping', priceModifier: 30, isDefault: false, isAvailable: true },
          { id: '2', name: 'Extra Sauce', description: 'Extra sauce on the side', priceModifier: 20, isDefault: false, isAvailable: true },
          { id: '3', name: 'Mushrooms', description: 'Fresh mushrooms', priceModifier: 25, isDefault: false, isAvailable: true },
          { id: '4', name: 'Olives', description: 'Black or green olives', priceModifier: 25, isDefault: false, isAvailable: true },
        ]
      },
      'Spice Level': {
        name: 'Spice Level',
        description: 'How spicy would you like it?',
        isRequired: true,
        allowMultiple: false,
        maxSelections: 1,
        minSelections: 1,
        options: [
          { id: '1', name: 'Mild', description: 'Light spice level', priceModifier: 0, isDefault: true, isAvailable: true },
          { id: '2', name: 'Medium', description: 'Moderate spice level', priceModifier: 0, isDefault: false, isAvailable: true },
          { id: '3', name: 'Hot', description: 'Spicy level', priceModifier: 0, isDefault: false, isAvailable: true },
          { id: '4', name: 'Extra Hot', description: 'Very spicy level', priceModifier: 0, isDefault: false, isAvailable: true },
        ]
      },
      'Drinks': {
        name: 'Drinks',
        description: 'Add a drink to your meal',
        isRequired: false,
        allowMultiple: false,
        maxSelections: 1,
        minSelections: 0,
        options: [
          { id: '1', name: 'Soft Drink', description: 'Carbonated beverage', priceModifier: 80, isDefault: false, isAvailable: true },
          { id: '2', name: 'Fresh Juice', description: 'Freshly squeezed juice', priceModifier: 120, isDefault: false, isAvailable: true },
          { id: '3', name: 'Water', description: 'Bottled water', priceModifier: 40, isDefault: false, isAvailable: true },
          { id: '4', name: 'Tea/Coffee', description: 'Hot beverage', priceModifier: 60, isDefault: false, isAvailable: true },
        ]
      }
    };

    const template = suggestedGroups[groupType as keyof typeof suggestedGroups];
    if (!template) return;

    const newGroup: CustomizationGroup = {
      id: Date.now().toString(),
      ...template,
      displayOrder: (formData.customizations?.length || 0) + 1,
      options: template.options.map((option, index) => ({
        ...option,
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        displayOrder: index + 1,
        priceAdjustment: option.priceModifier || 0, // Map priceModifier to priceAdjustment
      }))
    };

    const currentCustomizations = formData.customizations || [];
    onUpdateField('customizations', [...currentCustomizations, newGroup]);
    onValidateField('customizations');

    // Expand the new group
    setExpandedGroups([...expandedGroups, newGroup.id]);
  };

  const handleUpdateGroup = (groupId: string, field: string, value: any) => {
    const currentCustomizations = formData.customizations || [];
    const updatedCustomizations = currentCustomizations.map(group =>
      group.id === groupId
        ? { ...group, [field]: value }
        : group
    );
    onUpdateField('customizations', updatedCustomizations);
    onValidateField('customizations');
  };

  const handleRemoveGroup = (groupId: string) => {
    Alert.alert(
      'Remove Customization Group',
      'Are you sure you want to remove this customization group?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            const currentCustomizations = formData.customizations || [];
            const updatedCustomizations = currentCustomizations.filter(
              group => group.id !== groupId
            );
            onUpdateField('customizations', updatedCustomizations);
            setExpandedGroups(expandedGroups.filter(id => id !== groupId));
          },
        },
      ]
    );
  };

  const handleAddOption = (groupId: string) => {
    const newOption: CustomizationOption = {
      id: Date.now().toString(),
      name: 'New Option',
      description: '',
      priceAdjustment: 0,
      isAvailable: true,
      displayOrder: 1,
    };
    
    const currentCustomizations = formData.customizations || [];
    const updatedCustomizations = currentCustomizations.map(group =>
      group.id === groupId
        ? {
            ...group,
            options: [...group.options, newOption],
          }
        : group
    );
    onUpdateField('customizations', updatedCustomizations);
  };

  const handleUpdateOption = (groupId: string, optionId: string, field: string, value: any) => {
    const currentCustomizations = formData.customizations || [];
    const updatedCustomizations = currentCustomizations.map(group =>
      group.id === groupId
        ? {
            ...group,
            options: group.options.map(option =>
              option.id === optionId
                ? { ...option, [field]: value }
                : option
            ),
          }
        : group
    );
    onUpdateField('customizations', updatedCustomizations);
  };

  const handleRemoveOption = (groupId: string, optionId: string) => {
    const currentCustomizations = formData.customizations || [];
    const updatedCustomizations = currentCustomizations.map(group =>
      group.id === groupId
        ? {
            ...group,
            options: group.options.filter(option => option.id !== optionId),
          }
        : group
    );
    onUpdateField('customizations', updatedCustomizations);
  };

  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroups(prev =>
      prev.includes(groupId)
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId]
    );
  };

  const customizationsError = getFieldError('customizations');

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Extra Addons</Text>
          <Text style={styles.subtitle}>
            Add customizable options and extras for your menu item
          </Text>
        </View>

        {/* Quick Suggestions */}
        <View style={styles.suggestionsSection}>
          <Text style={styles.suggestionsTitle}>Quick Add Popular Addons</Text>
          <View style={styles.suggestionsGrid}>
            <TouchableOpacity
              style={styles.suggestionButton}
              onPress={() => handleAddSuggestedGroup('Size Options')}
            >
              <IconSymbol name="resize" size={16} color={Colors.light.textInverse} />
              <Text style={styles.suggestionText}>Size Options</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.suggestionButton}
              onPress={() => handleAddSuggestedGroup('Extra Toppings')}
            >
              <IconSymbol name="plus-circle" size={16} color={Colors.light.textInverse} />
              <Text style={styles.suggestionText}>Extra Toppings</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.suggestionButton}
              onPress={() => handleAddSuggestedGroup('Spice Level')}
            >
              <IconSymbol name="flame" size={16} color={Colors.light.textInverse} />
              <Text style={styles.suggestionText}>Spice Level</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.suggestionButton}
              onPress={() => handleAddSuggestedGroup('Drinks')}
            >
              <IconSymbol name="cup" size={16} color={Colors.light.textInverse} />
              <Text style={styles.suggestionText}>Drinks</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Add Custom Group Button */}
        <TouchableOpacity
          style={styles.addGroupButton}
          onPress={handleAddCustomizationGroup}
        >
          <IconSymbol
            name="plus"
            size={20}
            color={Colors.light.textInverse}
          />
          <Text style={styles.addGroupButtonText}>Add Custom Group</Text>
        </TouchableOpacity>

        {/* Customization Groups */}
        {(formData.customizations || []).map((group) => (
          <CustomizationGroupCard
            key={group.id}
            group={group}
            isExpanded={expandedGroups.includes(group.id)}
            onToggleExpansion={() => toggleGroupExpansion(group.id)}
            onUpdateGroup={(field, value) => handleUpdateGroup(group.id, field, value)}
            onRemoveGroup={() => handleRemoveGroup(group.id)}
            onAddOption={() => handleAddOption(group.id)}
            onUpdateOption={(optionId, field, value) =>
              handleUpdateOption(group.id, optionId, field, value)
            }
            onRemoveOption={(optionId) => handleRemoveOption(group.id, optionId)}
          />
        ))}

        {customizationsError && (
          <Text style={styles.errorText}>{customizationsError.message}</Text>
        )}

        {/* Tips Section */}
        <View style={styles.tipsContainer}>
          <View style={styles.tipsHeader}>
            <Ionicons
              name="bulb-outline"
              size={20}
              color={Colors.light.info}
            />
            <Text style={styles.tipsTitle}>Customization Tips</Text>
          </View>
          <View style={styles.tipsList}>
            <Text style={styles.tipItem}>
              • Group related options together (e.g., &quot;Size&quot;, &quot;Toppings&quot;)
            </Text>
            <Text style={styles.tipItem}>
              • Use clear, descriptive names for options
            </Text>
            <Text style={styles.tipItem}>
              • Set appropriate price adjustments for premium options
            </Text>
            <Text style={styles.tipItem}>
              • Mark essential choices as required
            </Text>
            <Text style={styles.tipItem}>
              • Consider limiting selections to avoid overwhelming customers
            </Text>
          </View>
        </View>
      </ScrollView>
    </Animated.View>
  );
}

interface CustomizationGroupCardProps {
  group: CustomizationGroup;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onUpdateGroup: (field: string, value: any) => void;
  onRemoveGroup: () => void;
  onAddOption: () => void;
  onUpdateOption: (optionId: string, field: string, value: any) => void;
  onRemoveOption: (optionId: string) => void;
}

function CustomizationGroupCard({
  group,
  isExpanded,
  onToggleExpansion,
  onUpdateGroup,
  onRemoveGroup,
  onAddOption,
  onUpdateOption,
  onRemoveOption,
}: CustomizationGroupCardProps) {
  return (
    <View style={styles.groupCard}>
      {/* Group Header */}
      <TouchableOpacity
        style={styles.groupHeader}
        onPress={onToggleExpansion}
      >
        <View style={styles.groupHeaderContent}>
          <Text style={styles.groupName}>{group.name}</Text>
          <Text style={styles.groupOptionsCount}>
            {group.options.length} option{group.options.length !== 1 ? 's' : ''}
          </Text>
        </View>
        <View style={styles.groupHeaderActions}>
          <TouchableOpacity
            style={styles.removeGroupButton}
            onPress={onRemoveGroup}
          >
            <Ionicons
              name="trash-outline"
              size={16}
              color={Colors.light.error}
            />
          </TouchableOpacity>
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={Colors.light.textSecondary}
          />
        </View>
      </TouchableOpacity>

      {/* Group Details */}
      {isExpanded && (
        <View style={styles.groupDetails}>
          {/* Group Name */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Group Name</Text>
            <TextInput
              style={styles.textInput}
              value={group.name}
              onChangeText={(text) => onUpdateGroup('name', text)}
              placeholder="e.g., Size, Toppings, Extras"
              placeholderTextColor={Colors.light.textTertiary}
            />
          </View>

          {/* Group Description */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Description (Optional)</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={group.description || ''}
              onChangeText={(text) => onUpdateGroup('description', text)}
              placeholder="Brief description of this customization group"
              placeholderTextColor={Colors.light.textTertiary}
              multiline
              numberOfLines={2}
            />
          </View>

          {/* Group Settings */}
          <View style={styles.groupSettings}>
            <View style={styles.settingRow}>
              <Text style={styles.settingLabel}>Required</Text>
              <Switch
                value={group.isRequired}
                onValueChange={(value) => onUpdateGroup('isRequired', value)}
                trackColor={{
                  false: Colors.light.border,
                  true: Colors.light.primaryLight,
                }}
                thumbColor={
                  group.isRequired ? Colors.light.primary : Colors.light.textTertiary
                }
              />
            </View>

            <View style={styles.settingRow}>
              <Text style={styles.settingLabel}>Allow Multiple</Text>
              <Switch
                value={group.allowMultiple}
                onValueChange={(value) => onUpdateGroup('allowMultiple', value)}
                trackColor={{
                  false: Colors.light.border,
                  true: Colors.light.primaryLight,
                }}
                thumbColor={
                  group.allowMultiple ? Colors.light.primary : Colors.light.textTertiary
                }
              />
            </View>
          </View>

          {/* Selection Limits */}
          {group.allowMultiple && (
            <View style={styles.selectionLimits}>
              <View style={styles.limitInput}>
                <Text style={styles.inputLabel}>Min Selections</Text>
                <TextInput
                  style={styles.numberInput}
                  value={group.minSelections?.toString() || '0'}
                  onChangeText={(text) => {
                    const value = parseInt(text) || 0;
                    onUpdateGroup('minSelections', value);
                  }}
                  keyboardType="numeric"
                  placeholder="0"
                />
              </View>

              <View style={styles.limitInput}>
                <Text style={styles.inputLabel}>Max Selections</Text>
                <TextInput
                  style={styles.numberInput}
                  value={group.maxSelections?.toString() || '1'}
                  onChangeText={(text) => {
                    const value = parseInt(text) || 1;
                    onUpdateGroup('maxSelections', value);
                  }}
                  keyboardType="numeric"
                  placeholder="1"
                />
              </View>
            </View>
          )}

          {/* Options */}
          <View style={styles.optionsSection}>
            <View style={styles.optionsHeader}>
              <Text style={styles.optionsTitle}>Options</Text>
              <TouchableOpacity
                style={styles.addOptionButton}
                onPress={onAddOption}
              >
                <IconSymbol
                  name="plus"
                  size={16}
                  color={Colors.light.textInverse}
                />
                <Text style={styles.addOptionButtonText}>Add Option</Text>
              </TouchableOpacity>
            </View>

            {group.options.map((option) => (
              <OptionCard
                key={option.id}
                option={option}
                onUpdate={(field, value) => onUpdateOption(option.id, field, value)}
                onRemove={() => onRemoveOption(option.id)}
              />
            ))}
          </View>
        </View>
      )}
    </View>
  );
}

interface OptionCardProps {
  option: CustomizationOption;
  onUpdate: (field: string, value: any) => void;
  onRemove: () => void;
}

function OptionCard({ option, onUpdate, onRemove }: OptionCardProps) {
  return (
    <View style={styles.optionCard}>
      <View style={styles.optionHeader}>
        <TextInput
          style={styles.optionNameInput}
          value={option.name}
          onChangeText={(text) => onUpdate('name', text)}
          placeholder="Option name"
          placeholderTextColor={Colors.light.textTertiary}
        />
        <TouchableOpacity
          style={styles.removeOptionButton}
          onPress={onRemove}
        >
          <Ionicons
            name="trash-outline"
            size={14}
            color={Colors.light.error}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.optionDetails}>
        <View style={styles.priceAdjustmentContainer}>
          <Text style={styles.priceLabel}>Price Adjustment</Text>
          <View style={styles.priceInputContainer}>
            <Text style={styles.currencySymbol}>PKR</Text>
            <TextInput
              style={styles.priceInput}
              value={option.priceAdjustment?.toString() || '0'}
              onChangeText={(text) => onUpdate('priceAdjustment', parseFloat(text) || 0)}
              keyboardType="decimal-pad"
              placeholder="0"
            />
          </View>
        </View>

        <View style={styles.availabilityToggle}>
          <Text style={styles.availabilityLabel}>Available</Text>
          <Switch
            value={option.isAvailable}
            onValueChange={(value) => onUpdate('isAvailable', value)}
            trackColor={{
              false: Colors.light.border,
              true: Colors.light.successLight,
            }}
            thumbColor={
              option.isAvailable ? Colors.light.success : Colors.light.textTertiary
            }
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    lineHeight: 22,
  },
  suggestionsSection: {
    marginBottom: 16,
  },
  suggestionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  suggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  suggestionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.light.primary,
    borderWidth: 1,
    borderColor: Colors.light.primary,
    minWidth: '45%',
    justifyContent: 'center',
  },
  suggestionText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.light.textInverse,
    marginLeft: 4,
  },
  addGroupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    borderStyle: 'dashed',
    backgroundColor: Colors.light.primary,
    marginBottom: 20,
  },
  addGroupButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textInverse,
    marginLeft: 8,
  },
  groupCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    overflow: 'hidden',
  },
  groupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: Colors.light.background,
  },
  groupHeaderContent: {
    flex: 1,
  },
  groupName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  groupOptionsCount: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  groupHeaderActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  removeGroupButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: Colors.light.errorLight,
  },
  groupDetails: {
    padding: 16,
    gap: 16,
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  textInput: {
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: Colors.light.background,
  },
  textArea: {
    minHeight: 60,
    textAlignVertical: 'top',
  },
  groupSettings: {
    gap: 12,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  settingLabel: {
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500',
  },
  selectionLimits: {
    flexDirection: 'row',
    gap: 12,
  },
  limitInput: {
    flex: 1,
    gap: 8,
  },
  numberInput: {
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: Colors.light.background,
    textAlign: 'center',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    backgroundColor: Colors.light.background,
  },
  dropdownButtonText: {
    fontSize: 16,
    color: Colors.light.text,
  },
  dropdown: {
    marginTop: 4,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    backgroundColor: Colors.light.surface,
    maxHeight: 80, // Show only 2 options at a time (40px each)
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    position: 'relative',
    zIndex: 1000,
  },
  dropdownScroll: {
    maxHeight: 80, // Show only 2 options at a time
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    height: 40, // Fixed height for each item
  },
  dropdownItemText: {
    fontSize: 16,
    color: Colors.light.text,
  },
  optionsSection: {
    gap: 12,
  },
  optionsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  optionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  addOptionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: Colors.light.primary,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  addOptionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textInverse,
    marginLeft: 4,
  },
  optionCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  optionNameInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    paddingVertical: 4,
    marginRight: 12,
  },
  removeOptionButton: {
    padding: 6,
    borderRadius: 6,
    backgroundColor: Colors.light.errorLight,
  },
  optionDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  priceAdjustmentContainer: {
    flex: 1,
  },
  priceLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginRight: 4,
  },
  priceInput: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    paddingVertical: 4,
    minWidth: 60,
    textAlign: 'center',
  },
  availabilityToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  availabilityLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  tipsContainer: {
    backgroundColor: Colors.light.infoLight,
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: Colors.light.info,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 8,
  },
  tipsList: {
    gap: 4,
  },
  tipItem: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.error,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default CustomizationsStep;
