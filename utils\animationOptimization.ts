import { useCallback, useEffect, useRef } from 'react';
import { Animated, Easing, InteractionManager } from 'react-native';
import {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';

// Animation configuration presets
export const AnimationPresets = {
  // Spring animations
  spring: {
    gentle: { damping: 20, stiffness: 90, mass: 1 },
    bouncy: { damping: 10, stiffness: 100, mass: 1 },
    quick: { damping: 15, stiffness: 200, mass: 0.8 },
  },
  
  // Timing animations
  timing: {
    fast: { duration: 200, easing: Easing.out(Easing.quad) },
    normal: { duration: 300, easing: Easing.out(Easing.cubic) },
    slow: { duration: 500, easing: Easing.out(Easing.cubic) },
    elastic: { duration: 400, easing: Easing.elastic(1.2) },
  },
  
  // Navigation transitions
  navigation: {
    slideIn: { duration: 250, easing: Easing.out(Easing.cubic) },
    slideOut: { duration: 200, easing: Easing.in(Easing.cubic) },
    fade: { duration: 200, easing: Easing.linear },
  },
};

// Optimized fade animation hook
export const useFadeAnimation = (
  initialValue = 0,
  config = AnimationPresets.timing.normal
) => {
  const opacity = useSharedValue(initialValue);

  const fadeIn = useCallback(() => {
    opacity.value = withTiming(1, config);
  }, [opacity, config]);

  const fadeOut = useCallback(() => {
    opacity.value = withTiming(0, config);
  }, [opacity, config]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  return { fadeIn, fadeOut, animatedStyle, opacity };
};

// Optimized scale animation hook
export const useScaleAnimation = (
  initialValue = 1,
  config = AnimationPresets.spring.gentle
) => {
  const scale = useSharedValue(initialValue);

  const scaleIn = useCallback(() => {
    scale.value = withSpring(1, config);
  }, [scale, config]);

  const scaleOut = useCallback(() => {
    scale.value = withSpring(0, config);
  }, [scale, config]);

  const pulse = useCallback(() => {
    scale.value = withSpring(1.1, config, () => {
      scale.value = withSpring(1, config);
    });
  }, [scale, config]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return { scaleIn, scaleOut, pulse, animatedStyle, scale };
};

// Optimized slide animation hook
export const useSlideAnimation = (
  direction: 'left' | 'right' | 'up' | 'down' = 'right',
  distance = 100,
  config = AnimationPresets.timing.normal
) => {
  const translateX = useSharedValue(direction === 'left' ? -distance : direction === 'right' ? distance : 0);
  const translateY = useSharedValue(direction === 'up' ? -distance : direction === 'down' ? distance : 0);

  const slideIn = useCallback(() => {
    translateX.value = withTiming(0, config);
    translateY.value = withTiming(0, config);
  }, [translateX, translateY, config]);

  const slideOut = useCallback(() => {
    translateX.value = withTiming(
      direction === 'left' ? -distance : direction === 'right' ? distance : 0,
      config
    );
    translateY.value = withTiming(
      direction === 'up' ? -distance : direction === 'down' ? distance : 0,
      config
    );
  }, [translateX, translateY, direction, distance, config]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));

  return { slideIn, slideOut, animatedStyle, translateX, translateY };
};

// Optimized rotation animation hook
export const useRotationAnimation = (
  config = AnimationPresets.timing.normal
) => {
  const rotation = useSharedValue(0);

  const rotate = useCallback((degrees: number) => {
    rotation.value = withTiming(degrees, config);
  }, [rotation, config]);

  const spin = useCallback(() => {
    rotation.value = withTiming(360, { ...config, duration: 1000 }, () => {
      rotation.value = 0;
    });
  }, [rotation, config]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  return { rotate, spin, animatedStyle, rotation };
};

// Optimized loading animation hook
export const useLoadingAnimation = () => {
  const opacity = useSharedValue(0.3);
  const scale = useSharedValue(1);

  const startLoading = useCallback(() => {
    const animate = () => {
      opacity.value = withTiming(1, { duration: 800 }, () => {
        opacity.value = withTiming(0.3, { duration: 800 }, () => {
          runOnJS(animate)();
        });
      });
    };
    animate();
  }, [opacity]);

  const stopLoading = useCallback(() => {
    opacity.value = withTiming(1, AnimationPresets.timing.fast);
  }, [opacity]);

  const pulseLoading = useCallback(() => {
    const animate = () => {
      scale.value = withSpring(1.1, AnimationPresets.spring.quick, () => {
        scale.value = withSpring(1, AnimationPresets.spring.quick, () => {
          runOnJS(animate)();
        });
      });
    };
    animate();
  }, [scale]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  return { startLoading, stopLoading, pulseLoading, animatedStyle };
};

// Optimized scroll-based animation hook
export const useScrollAnimation = (scrollY: any, inputRange: number[], outputRange: number[]) => {
  const animatedStyle = useAnimatedStyle(() => {
    const value = interpolate(
      scrollY.value,
      inputRange,
      outputRange,
      Extrapolate.CLAMP
    );

    return {
      transform: [{ translateY: value }],
    };
  });

  return animatedStyle;
};

// Performance-optimized animation manager
export class AnimationManager {
  private static instance: AnimationManager;
  private activeAnimations = new Set<string>();
  private animationQueue: Array<() => void> = [];
  private isProcessing = false;

  static getInstance(): AnimationManager {
    if (!AnimationManager.instance) {
      AnimationManager.instance = new AnimationManager();
    }
    return AnimationManager.instance;
  }

  // Queue animation to prevent too many concurrent animations
  queueAnimation(id: string, animation: () => void): void {
    if (this.activeAnimations.size < 3) { // Limit concurrent animations
      this.executeAnimation(id, animation);
    } else {
      this.animationQueue.push(() => this.executeAnimation(id, animation));
    }
  }

  private executeAnimation(id: string, animation: () => void): void {
    this.activeAnimations.add(id);
    
    InteractionManager.runAfterInteractions(() => {
      animation();
      
      // Clean up after animation
      setTimeout(() => {
        this.activeAnimations.delete(id);
        this.processQueue();
      }, 300); // Assume animation duration
    });
  }

  private processQueue(): void {
    if (this.animationQueue.length > 0 && !this.isProcessing) {
      this.isProcessing = true;
      const nextAnimation = this.animationQueue.shift();
      if (nextAnimation) {
        nextAnimation();
      }
      this.isProcessing = false;
    }
  }

  // Cancel all animations
  cancelAll(): void {
    this.activeAnimations.clear();
    this.animationQueue = [];
  }

  // Get animation statistics
  getStats(): { active: number; queued: number } {
    return {
      active: this.activeAnimations.size,
      queued: this.animationQueue.length,
    };
  }
}

// Hook for performance-aware animations
export const usePerformanceAnimation = (id: string) => {
  const animationManager = AnimationManager.getInstance();

  const queueAnimation = useCallback((animation: () => void) => {
    animationManager.queueAnimation(id, animation);
  }, [animationManager, id]);

  return { queueAnimation };
};

// Optimized list item animation hook
export const useListItemAnimation = (index: number, delay = 50) => {
  const opacity = useSharedValue(0);
  const translateY = useSharedValue(20);

  const animateIn = useCallback(() => {
    const animationDelay = index * delay;
    
    setTimeout(() => {
      opacity.value = withTiming(1, AnimationPresets.timing.normal);
      translateY.value = withTiming(0, AnimationPresets.timing.normal);
    }, animationDelay);
  }, [opacity, translateY, index, delay]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ translateY: translateY.value }],
  }));

  return { animateIn, animatedStyle };
};

// Gesture-based animation utilities
export const GestureAnimations = {
  // Swipe to dismiss
  swipeToDismiss: (translateX: any, threshold = 100) => {
    'worklet';
    return Math.abs(translateX) > threshold;
  },

  // Pull to refresh
  pullToRefresh: (translateY: any, threshold = 80) => {
    'worklet';
    return translateY > threshold;
  },

  // Elastic scroll
  elasticScroll: (scrollY: any, contentHeight: number, containerHeight: number) => {
    'worklet';
    const maxScroll = contentHeight - containerHeight;
    if (scrollY < 0) {
      return scrollY * 0.5; // Elastic effect at top
    } else if (scrollY > maxScroll) {
      return maxScroll + (scrollY - maxScroll) * 0.5; // Elastic effect at bottom
    }
    return scrollY;
  },
};

// Export singleton
export const animationManager = AnimationManager.getInstance();
