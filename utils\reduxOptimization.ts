import { createSelector } from '@reduxjs/toolkit';
import { useCallback, useMemo, useRef } from 'react';
import { useSelector } from 'react-redux';

// Optimized selector creation with memoization
export const createOptimizedSelector = <State, Result>(
  inputSelectors: ((state: State) => any)[],
  resultFunc: (...args: any[]) => Result,
  options?: {
    memoizeOptions?: {
      maxSize?: number;
      equalityCheck?: (a: any, b: any) => boolean;
    };
  }
) => {
  return createSelector(inputSelectors, resultFunc, {
    memoizeOptions: {
      maxSize: options?.memoizeOptions?.maxSize || 10,
      equalityCheck: options?.memoizeOptions?.equalityCheck || ((a, b) => a === b),
    },
  });
};

// Memory-efficient useSelector hook
export const useOptimizedSelector = <State, Selected>(
  selector: (state: State) => Selected,
  equalityFn?: (left: Selected, right: Selected) => boolean
) => {
  const defaultEqualityFn = useCallback((left: Selected, right: Selected) => {
    // Shallow comparison for objects and arrays
    if (typeof left === 'object' && typeof right === 'object') {
      if (left === null || right === null) return left === right;
      
      if (Array.isArray(left) && Array.isArray(right)) {
        if (left.length !== right.length) return false;
        return left.every((item, index) => item === right[index]);
      }
      
      const leftKeys = Object.keys(left);
      const rightKeys = Object.keys(right);
      
      if (leftKeys.length !== rightKeys.length) return false;
      
      return leftKeys.every(key => 
        (left as any)[key] === (right as any)[key]
      );
    }
    
    return left === right;
  }, []);

  return useSelector(selector, equalityFn || defaultEqualityFn);
};

// Batch action dispatcher to reduce re-renders
export class BatchActionDispatcher {
  private static instance: BatchActionDispatcher;
  private pendingActions: any[] = [];
  private batchTimeout: NodeJS.Timeout | null = null;
  private dispatch: any = null;

  static getInstance(): BatchActionDispatcher {
    if (!BatchActionDispatcher.instance) {
      BatchActionDispatcher.instance = new BatchActionDispatcher();
    }
    return BatchActionDispatcher.instance;
  }

  setDispatch(dispatch: any): void {
    this.dispatch = dispatch;
  }

  batchDispatch(action: any, delay = 16): void {
    this.pendingActions.push(action);

    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }

    this.batchTimeout = setTimeout(() => {
      this.flushActions();
    }, delay);
  }

  private flushActions(): void {
    if (this.pendingActions.length > 0 && this.dispatch) {
      // Dispatch all pending actions
      this.pendingActions.forEach(action => {
        this.dispatch(action);
      });
      
      this.pendingActions = [];
    }
    
    this.batchTimeout = null;
  }

  // Force flush all pending actions
  flush(): void {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.flushActions();
    }
  }
}

// Hook for batched dispatching
export const useBatchDispatch = () => {
  const batchDispatcher = BatchActionDispatcher.getInstance();
  
  return useCallback((action: any, delay?: number) => {
    batchDispatcher.batchDispatch(action, delay);
  }, [batchDispatcher]);
};

// Optimized state normalization utilities
export const StateNormalization = {
  // Normalize array of objects by ID
  normalizeById: <T extends { id: string | number }>(items: T[]) => {
    const byId: Record<string | number, T> = {};
    const allIds: (string | number)[] = [];

    items.forEach(item => {
      byId[item.id] = item;
      allIds.push(item.id);
    });

    return { byId, allIds };
  },

  // Denormalize normalized state
  denormalize: <T>(byId: Record<string | number, T>, allIds: (string | number)[]) => {
    return allIds.map(id => byId[id]).filter(Boolean);
  },

  // Update normalized state efficiently
  updateNormalizedState: <T extends { id: string | number }>(
    state: { byId: Record<string | number, T>; allIds: (string | number)[] },
    updates: Partial<T>[],
    action: 'add' | 'update' | 'remove'
  ) => {
    const newState = {
      byId: { ...state.byId },
      allIds: [...state.allIds]
    };

    updates.forEach(update => {
      if (!update.id) return;

      switch (action) {
        case 'add':
          if (!newState.byId[update.id]) {
            newState.byId[update.id] = update as T;
            newState.allIds.push(update.id);
          }
          break;
        
        case 'update':
          if (newState.byId[update.id]) {
            newState.byId[update.id] = { ...newState.byId[update.id], ...update };
          }
          break;
        
        case 'remove':
          delete newState.byId[update.id];
          newState.allIds = newState.allIds.filter(id => id !== update.id);
          break;
      }
    });

    return newState;
  }
};

// Memory-efficient pagination utilities
export const PaginationUtils = {
  // Create paginated selector
  createPaginatedSelector: <T>(
    itemsSelector: (state: any) => T[],
    pageSize = 20
  ) => {
    return createSelector(
      [itemsSelector, (state: any, page: number) => page],
      (items, page) => {
        const startIndex = page * pageSize;
        const endIndex = startIndex + pageSize;
        
        return {
          items: items.slice(startIndex, endIndex),
          totalItems: items.length,
          totalPages: Math.ceil(items.length / pageSize),
          currentPage: page,
          hasNextPage: endIndex < items.length,
          hasPreviousPage: page > 0
        };
      }
    );
  },

  // Virtual scrolling data selector
  createVirtualScrollSelector: <T>(
    itemsSelector: (state: any) => T[],
    itemHeight: number,
    containerHeight: number
  ) => {
    return createSelector(
      [itemsSelector, (state: any, scrollOffset: number) => scrollOffset],
      (items, scrollOffset) => {
        const visibleCount = Math.ceil(containerHeight / itemHeight);
        const startIndex = Math.floor(scrollOffset / itemHeight);
        const endIndex = Math.min(startIndex + visibleCount + 2, items.length); // +2 for buffer
        
        return {
          visibleItems: items.slice(startIndex, endIndex),
          startIndex,
          endIndex,
          totalHeight: items.length * itemHeight
        };
      }
    );
  }
};

// Redux middleware for memory optimization
export const memoryOptimizationMiddleware = (store: any) => (next: any) => (action: any) => {
  // Log memory usage for specific actions in development
  if (__DEV__ && action.type.includes('FETCH_SUCCESS')) {
    const beforeMemory = global.performance?.memory?.usedJSHeapSize || 0;
    const result = next(action);
    const afterMemory = global.performance?.memory?.usedJSHeapSize || 0;
    
    const memoryDiff = (afterMemory - beforeMemory) / (1024 * 1024);
    if (memoryDiff > 5) { // Alert if action increases memory by more than 5MB
      console.warn(`Action ${action.type} increased memory by ${memoryDiff.toFixed(2)}MB`);
    }
    
    return result;
  }
  
  return next(action);
};

// Cleanup utilities for component unmounting
export const useCleanupOnUnmount = (cleanupFunctions: (() => void)[]) => {
  const cleanupRef = useRef(cleanupFunctions);
  
  // Update cleanup functions
  cleanupRef.current = cleanupFunctions;
  
  // Cleanup on unmount
  useMemo(() => {
    return () => {
      cleanupRef.current.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          console.warn('Cleanup function failed:', error);
        }
      });
    };
  }, []);
};

// Export singleton instance
export const batchActionDispatcher = BatchActionDispatcher.getInstance();
