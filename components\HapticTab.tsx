import { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs';
import { PlatformPressable } from '@react-navigation/elements';
import * as Haptics from 'expo-haptics';
import { StyleSheet, View } from 'react-native';

export function HapticTab(props: BottomTabBarButtonProps) {
  const isSelected = props.accessibilityState?.selected;

  return (
    <View style={[styles.tabContainer, isSelected && styles.selectedTabContainer]}>
      <PlatformPressable
        {...props}
        style={[props.style, isSelected && styles.selectedTab]}
        onPressIn={(ev) => {
          // Add haptic feedback for Android
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          props.onPressIn?.(ev);
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedTabContainer: {
    transform: [{ translateY: -8 }, { scale: 1.1 }], // Enhanced pop up effect with scaling
  },
  selectedTab: {
    backgroundColor: 'rgba(255, 193, 7, 0.3)', // Enhanced gold background for selected
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    shadowColor: '#FFC107',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.6,
    shadowRadius: 10,
    elevation: 12,
    borderWidth: 2,
    borderColor: 'rgba(255, 193, 7, 0.6)',
  },
});
