// Export all API services for easy importing
export { default as analyticsService } from './analyticsService';
export { default as api } from './api';
export { default as authService } from './authService';
export { default as menuService } from './menuService';
export { default as notificationsService } from './notificationsService';
export { default as ordersService } from './ordersService';
export { default as registrationService } from './registrationService';
export { default as restaurantService } from './restaurantService';
export { default as staffService } from './staffService';

// Re-export types
export type {
    CustomerAnalytics, DashboardStats, FinancialAnalytics, MenuAnalytics,
    OrderAnalytics, RevenueAnalytics
} from './analyticsService';
export type { ApiResponse, PaginatedResponse } from './api';
export type {
    AuthResponse, LoginRequest,
    RegisterRequest,
    User
} from './authService';
export type {
    CreateMenuItemData, MenuCategory, MenuCustomization, MenuItem
} from './menuService';
export type {
    Notification, NotificationSettings, NotificationType
} from './notificationsService';
export type {
    Order, OrderFilters, OrderItem, OrderStats, OrderStatus
} from './ordersService';
export type {
    RegistrationFormData,
    RegistrationResponse
} from './registrationService';
export type {
    Restaurant, RestaurantSettings, RestaurantStats
} from './restaurantService';
export type {
    CreateStaffData,
    StaffCredentials, StaffMember, StaffStats
} from './staffService';

