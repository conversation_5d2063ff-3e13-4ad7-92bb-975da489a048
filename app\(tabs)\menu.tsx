import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchMenuItems, updateMenuItemAvailability } from '@/store/slices/menuSlice';
import { MenuItem } from '@/types';
import { router } from 'expo-router';
import { useEffect, useMemo, useState } from 'react';
import {
    FlatList,
    Image,
    Modal,
    RefreshControl,
    ScrollView,
    StyleSheet,
    Switch,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

// const { width } = Dimensions.get('window'); // Removed unused variable

export default function MenuScreen() {
  return (
    <ProtectedRoute requiredPermission="menu">
      <MenuContent />
    </ProtectedRoute>
  );
}

function MenuContent() {
  const dispatch = useAppDispatch();
  const { categories, items, loading } = useAppSelector(state => state.menu);

  // Enhanced State Management
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showItemModal, setShowItemModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'category' | 'popularity'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');

  // Filter States
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [availabilityFilter, setAvailabilityFilter] = useState<'all' | 'available' | 'unavailable'>('all');
  const [dietaryFilters, setDietaryFilters] = useState({
    vegetarian: false,
    vegan: false,
    glutenFree: false,
    spicy: false
  });

  useEffect(() => {
    dispatch(fetchMenuItems());
  }, [dispatch]);

  // Enhanced Filtering and Sorting Logic
  const filteredAndSortedItems = useMemo(() => {
    let filtered = items.filter(item => {
      // Category filter
      if (selectedCategory !== 'all' && item.category !== selectedCategory) return false;

      // Search filter
      if (searchQuery && !item.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !item.description.toLowerCase().includes(searchQuery.toLowerCase())) return false;

      // Price range filter
      if (item.price < priceRange[0] || item.price > priceRange[1]) return false;

      // Availability filter
      if (availabilityFilter === 'available' && !item.isAvailable) return false;
      if (availabilityFilter === 'unavailable' && item.isAvailable) return false;

      // Dietary filters
      if (dietaryFilters.vegetarian && !item.isVegetarian) return false;
      if (dietaryFilters.vegan && !item.isVegan) return false;
      if (dietaryFilters.glutenFree && !item.isGlutenFree) return false;
      if (dietaryFilters.spicy && !item.isSpicy) return false;

      return true;
    });

    // Sort items
    filtered.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'price':
          comparison = a.price - b.price;
          break;
        case 'category':
          comparison = a.category.localeCompare(b.category);
          break;
        case 'popularity':
          comparison = (b.orderCount || 0) - (a.orderCount || 0);
          break;
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [items, selectedCategory, searchQuery, priceRange, availabilityFilter, dietaryFilters, sortBy, sortOrder]);

  const handleRefresh = () => {
    dispatch(fetchMenuItems());
  };

  const handleToggleAvailability = (itemId: string, isAvailable: boolean) => {
    dispatch(updateMenuItemAvailability({ itemId, isAvailable }));
  };

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === filteredAndSortedItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredAndSortedItems.map(item => item.id));
    }
  };

  const handleBulkAvailabilityToggle = (isAvailable: boolean) => {
    selectedItems.forEach(itemId => {
      dispatch(updateMenuItemAvailability({ itemId, isAvailable }));
    });
    setSelectedItems([]);
    setShowBulkActions(false);
  };

  const handleItemPress = (item: MenuItem) => {
    if (showBulkActions) {
      handleSelectItem(item.id);
    } else {
      setSelectedItem(item);
      setShowItemModal(true);
    }
  };

  const handleEditItem = (item: MenuItem) => {
    // Navigate to add-menu-item screen with edit mode and pre-populated data
    router.push({
      pathname: '/add-menu-item',
      params: {
        mode: 'edit',
        itemId: item.id,
        itemData: JSON.stringify(item)
      }
    });
  };

  const handleItemLongPress = (item: MenuItem) => {
    if (!showBulkActions) {
      setShowBulkActions(true);
      setSelectedItems([item.id]);
    }
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('all');
    setPriceRange([0, 100]);
    setAvailabilityFilter('all');
    setDietaryFilters({
      vegetarian: false,
      vegan: false,
      glutenFree: false,
      spicy: false
    });
  };

  const renderMenuItem = ({ item }: { item: MenuItem }) => {
    const isSelected = selectedItems.includes(item.id);
    const stockLevel = item.stockLevel || 0;
    const isLowStock = stockLevel < 10 && stockLevel > 0;
    const isOutOfStock = stockLevel === 0;

    return (
      <TouchableOpacity
        style={[
          styles.menuItemCard,
          isSelected && styles.selectedItemCard,
          !item.isAvailable && styles.unavailableItemCard
        ]}
        onPress={() => handleItemPress(item)}
        onLongPress={() => handleItemLongPress(item)}
      >
        {/* Selection Checkbox */}
        {showBulkActions && (
          <View style={styles.selectionCheckbox}>
            <View style={[styles.checkbox, isSelected && styles.checkedBox]}>
              {isSelected && (
                <IconSymbol name="checkmark" size={16} color={Colors.light.textInverse} />
              )}
            </View>
          </View>
        )}

        {/* Item Image */}
        <View style={styles.itemImageContainer}>
          {item.imageUrl ? (
            <Image source={{ uri: item.imageUrl }} style={styles.itemImage} />
          ) : (
            <View style={styles.placeholderImage}>
              <IconSymbol name="photo" size={32} color={Colors.light.textTertiary} />
            </View>
          )}

          {/* Stock Level Indicator */}
          {isOutOfStock && (
            <View style={styles.stockBadge}>
              <Text style={styles.stockBadgeText}>Out of Stock</Text>
            </View>
          )}
          {isLowStock && (
            <View style={[styles.stockBadge, { backgroundColor: Colors.light.warning }]}>
              <Text style={styles.stockBadgeText}>Low Stock</Text>
            </View>
          )}
        </View>

        <View style={styles.itemContent}>
          <View style={styles.itemHeader}>
            <View style={styles.itemInfo}>
              <View style={styles.itemNameRow}>
                <Text style={styles.itemName}>{item.name}</Text>
                {item.isPopular && (
                  <View style={styles.popularBadge}>
                    <IconSymbol name="star.fill" size={12} color={Colors.light.warning} />
                    <Text style={styles.popularText}>Popular</Text>
                  </View>
                )}
              </View>

              <Text style={styles.itemDescription} numberOfLines={2}>
                {item.description}
              </Text>

              <View style={styles.itemMeta}>
                <View style={styles.priceRow}>
                  <Text style={styles.itemPrice}>PKR {item.price.toFixed(0)}</Text>
                </View>
                <View style={styles.metaRow}>
                  <Text style={styles.prepTime}>
                    <IconSymbol name="clock" size={12} color={Colors.light.textTertiary} />
                    {item.preparationTime} min
                  </Text>
                  {item.orderCount && (
                    <Text style={styles.orderCount}>
                      <IconSymbol name="chart.bar" size={12} color={Colors.light.textTertiary} />
                      {item.orderCount} orders
                    </Text>
                  )}
                </View>
              </View>
            </View>

            <View style={styles.itemActions}>
              <View style={styles.availabilityToggle}>
                <Text style={[styles.availabilityText, { color: item.isAvailable ? Colors.light.success : Colors.light.error }]}>
                  {item.isAvailable ? 'Available' : 'Unavailable'}
                </Text>
                <Switch
                  value={item.isAvailable}
                  onValueChange={(value) => handleToggleAvailability(item.id, value)}
                  trackColor={{ false: Colors.light.border, true: Colors.light.success }}
                  thumbColor={item.isAvailable ? Colors.light.textInverse : Colors.light.textTertiary}
                />
              </View>

              {/* Quick Actions */}
              <View style={styles.quickActions}>
                <TouchableOpacity
                  style={styles.quickActionButton}
                  onPress={() => handleEditItem(item)}
                >
                  <IconSymbol name="pencil" size={16} color={Colors.light.primary} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.quickActionButton}>
                  <IconSymbol name="chart.line.uptrend.xyaxis" size={16} color={Colors.light.secondary} />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Item Tags */}
          <View style={styles.itemTags}>
            {item.isVegetarian && (
              <View style={[styles.tag, { backgroundColor: Colors.light.success }]}>
                <IconSymbol name="leaf" size={10} color={Colors.light.textInverse} />
                <Text style={styles.tagText}>Vegetarian</Text>
              </View>
            )}
            {item.isVegan && (
              <View style={[styles.tag, { backgroundColor: Colors.light.secondary }]}>
                <IconSymbol name="heart" size={10} color={Colors.light.textInverse} />
                <Text style={styles.tagText}>Vegan</Text>
              </View>
            )}
            {item.isGlutenFree && (
              <View style={[styles.tag, { backgroundColor: Colors.light.warning }]}>
                <IconSymbol name="checkmark.shield" size={10} color={Colors.light.textInverse} />
                <Text style={styles.tagText}>Gluten Free</Text>
              </View>
            )}
            {item.isSpicy && (
              <View style={[styles.tag, { backgroundColor: Colors.light.error }]}>
                <IconSymbol name="flame" size={10} color={Colors.light.textInverse} />
                <Text style={styles.tagText}>Spicy</Text>
              </View>
            )}
          </View>

          {/* Customizations Preview */}
          {item.customizations && item.customizations.length > 0 && (
            <View style={styles.customizationsPreview}>
              <IconSymbol name="slider.horizontal.3" size={12} color={Colors.light.textTertiary} />
              <Text style={styles.customizationsText}>
                {item.customizations.length} customization{item.customizations.length > 1 ? 's' : ''} available
              </Text>
            </View>
          )}

          {/* Stock Level */}
          {stockLevel > 0 && (
            <View style={styles.stockInfo}>
              <IconSymbol name="cube.box" size={12} color={Colors.light.textTertiary} />
              <Text style={styles.stockText}>Stock: {stockLevel} units</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const categoryOptions = [
    { key: 'all', label: 'All Items', count: items.length },
    ...categories.map(cat => ({
      key: cat.name,
      label: cat.name,
      count: items.filter(item => item.category === cat.name).length
    }))
  ];

  const activeFiltersCount = [
    searchQuery,
    selectedCategory !== 'all',
    availabilityFilter !== 'all',
    Object.values(dietaryFilters).some(Boolean),
    priceRange[0] > 0 || priceRange[1] < 100
  ].filter(Boolean).length;

  return (
    <View style={styles.container}>
      {/* Enhanced Header */}
      <View style={styles.headerContainer}>
        {/* Stats Row */}
        <View style={styles.statsRow}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{items.length}</Text>
            <Text style={styles.statLabel}>Total Items</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{items.filter(item => item.isAvailable).length}</Text>
            <Text style={styles.statLabel}>Available</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{categories.length}</Text>
            <Text style={styles.statLabel}>Categories</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{filteredAndSortedItems.length}</Text>
            <Text style={styles.statLabel}>Filtered</Text>
          </View>
        </View>

        {/* Search and Actions Row */}
        <View style={styles.searchRow}>
          <View style={styles.searchContainer}>
            <IconSymbol name="magnifyingglass" size={20} color={Colors.light.textTertiary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search menu items..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={Colors.light.textTertiary}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <IconSymbol name="xmark.circle.fill" size={20} color={Colors.light.textTertiary} />
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity
            style={[styles.filterButton, activeFiltersCount > 0 && styles.activeFilterButton]}
            onPress={() => setShowAdvancedFilters(!showAdvancedFilters)}
          >
            <IconSymbol name="line.3.horizontal.decrease.circle" size={24} color={Colors.light.primary} />
            {activeFiltersCount > 0 && (
              <View style={styles.filterBadge}>
                <Text style={styles.filterBadgeText}>{activeFiltersCount}</Text>
              </View>
            )}
          </TouchableOpacity>

          <TouchableOpacity style={styles.viewModeButton} onPress={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}>
            <IconSymbol name={viewMode === 'list' ? 'square.grid.2x2' : 'list.bullet'} size={24} color={Colors.light.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.addButton}
            onPress={() => router.push('/(tabs)/add-menu-item')}
          >
            <IconSymbol name="plus" size={20} color={Colors.light.textInverse} />
            <Text style={styles.addButtonText}>Add Item</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <View style={styles.advancedFilters}>
          {/* Sort Controls */}
          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>Sort By</Text>
            <View style={styles.sortControls}>
              <FlatList
                horizontal
                showsHorizontalScrollIndicator={false}
                data={[
                  { key: 'name', label: 'Name' },
                  { key: 'price', label: 'Price' },
                  { key: 'category', label: 'Category' },
                  { key: 'popularity', label: 'Popularity' }
                ]}
                keyExtractor={(item) => item.key}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[styles.filterChip, sortBy === item.key && styles.activeFilterChip]}
                    onPress={() => setSortBy(item.key as any)}
                  >
                    <Text style={[styles.filterChipText, sortBy === item.key && styles.activeFilterChipText]}>
                      {item.label}
                    </Text>
                  </TouchableOpacity>
                )}
              />
              <TouchableOpacity
                style={styles.sortOrderButton}
                onPress={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              >
                <IconSymbol
                  name={sortOrder === 'asc' ? 'arrow.up' : 'arrow.down'}
                  size={16}
                  color={Colors.light.primary}
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Availability Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>Availability</Text>
            <FlatList
              horizontal
              showsHorizontalScrollIndicator={false}
              data={[
                { key: 'all', label: 'All' },
                { key: 'available', label: 'Available' },
                { key: 'unavailable', label: 'Unavailable' }
              ]}
              keyExtractor={(item) => item.key}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[styles.filterChip, availabilityFilter === item.key && styles.activeFilterChip]}
                  onPress={() => setAvailabilityFilter(item.key as any)}
                >
                  <Text style={[styles.filterChipText, availabilityFilter === item.key && styles.activeFilterChipText]}>
                    {item.label}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>

          {/* Dietary Filters */}
          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>Dietary Options</Text>
            <View style={styles.dietaryFilters}>
              {Object.entries(dietaryFilters).map(([key, value]) => (
                <TouchableOpacity
                  key={key}
                  style={[styles.filterChip, value && styles.activeFilterChip]}
                  onPress={() => setDietaryFilters(prev => ({ ...prev, [key]: !prev[key as keyof typeof prev] }))}
                >
                  <Text style={[styles.filterChipText, value && styles.activeFilterChipText]}>
                    {key.charAt(0).toUpperCase() + key.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Clear Filters */}
          <TouchableOpacity style={styles.clearFiltersButton} onPress={clearFilters}>
            <IconSymbol name="trash" size={16} color={Colors.light.error} />
            <Text style={styles.clearFiltersText}>Clear All Filters</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Bulk Actions Bar */}
      {showBulkActions && selectedItems.length > 0 && (
        <View style={styles.bulkActionsBar}>
          <TouchableOpacity style={styles.selectAllButton} onPress={handleSelectAll}>
            <IconSymbol
              name={selectedItems.length === filteredAndSortedItems.length ? "checkmark.square" : "square"}
              size={24}
              color={Colors.light.textInverse}
            />
          </TouchableOpacity>
          <Text style={styles.selectedCount}>{selectedItems.length} selected</Text>
          <View style={styles.bulkActions}>
            <TouchableOpacity
              style={[styles.bulkActionButton, { backgroundColor: Colors.light.success }]}
              onPress={() => handleBulkAvailabilityToggle(true)}
            >
              <Text style={styles.bulkActionButtonText}>Make Available</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.bulkActionButton, { backgroundColor: Colors.light.error }]}
              onPress={() => handleBulkAvailabilityToggle(false)}
            >
              <Text style={styles.bulkActionButtonText}>Make Unavailable</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.bulkActionButton}
              onPress={() => setShowBulkActions(false)}
            >
              <IconSymbol name="xmark" size={16} color={Colors.light.textInverse} />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Category Filter */}
      <View style={styles.filterContainer}>
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={categoryOptions}
          keyExtractor={(item) => item.key}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.filterTab,
                selectedCategory === item.key && styles.activeFilterTab
              ]}
              onPress={() => setSelectedCategory(item.key)}
            >
              <Text style={[
                styles.filterTabText,
                selectedCategory === item.key && styles.activeFilterTabText
              ]}>
                {item.label}
              </Text>
              <View style={styles.categoryCount}>
                <Text style={styles.categoryCountText}>{item.count}</Text>
              </View>
            </TouchableOpacity>
          )}
        />
      </View>

      {/* Menu Items List */}
      <FlatList
        data={filteredAndSortedItems}
        keyExtractor={(item) => item.id}
        renderItem={renderMenuItem}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={handleRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <IconSymbol name="book" size={64} color={Colors.light.textTertiary} />
            <Text style={styles.emptyText}>No menu items found</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery ? `No items match "${searchQuery}"` :
               activeFiltersCount > 0 ? 'Try adjusting your filters' :
               selectedCategory === 'all'
                ? 'Add your first menu item to get started'
                : `No items in ${selectedCategory} category`
              }
            </Text>
            {activeFiltersCount > 0 && (
              <TouchableOpacity style={styles.clearFiltersButton} onPress={clearFilters}>
                <Text style={styles.clearFiltersText}>Clear Filters</Text>
              </TouchableOpacity>
            )}
          </View>
        }
      />

      {/* Item Details Modal */}
      <Modal
        visible={showItemModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowItemModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Item Details</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowItemModal(false)}
            >
              <IconSymbol name="xmark" size={24} color={Colors.light.text} />
            </TouchableOpacity>
          </View>

          {selectedItem && (
            <ScrollView style={styles.modalContent}>
              {/* Item Image */}
              <View style={styles.modalImageContainer}>
                {selectedItem.imageUrl ? (
                  <Image source={{ uri: selectedItem.imageUrl }} style={styles.modalImage} />
                ) : (
                  <View style={styles.modalPlaceholderImage}>
                    <IconSymbol name="photo" size={64} color={Colors.light.textTertiary} />
                    <Text style={styles.placeholderText}>No image available</Text>
                  </View>
                )}
                <TouchableOpacity style={styles.editImageButton}>
                  <IconSymbol name="camera" size={20} color={Colors.light.textInverse} />
                </TouchableOpacity>
              </View>

              {/* Item Info */}
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Basic Information</Text>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Name:</Text>
                  <Text style={styles.infoValue}>{selectedItem.name}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Category:</Text>
                  <Text style={styles.infoValue}>{selectedItem.category}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Price:</Text>
                  <Text style={styles.infoValue}>${selectedItem.price.toFixed(2)}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Prep Time:</Text>
                  <Text style={styles.infoValue}>{selectedItem.preparationTime} minutes</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Description:</Text>
                  <Text style={styles.infoValue}>{selectedItem.description}</Text>
                </View>
              </View>

              {/* Availability & Stock */}
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Availability & Stock</Text>
                <View style={styles.availabilityRow}>
                  <Text style={styles.infoLabel}>Available:</Text>
                  <Switch
                    value={selectedItem.isAvailable}
                    onValueChange={(value) => handleToggleAvailability(selectedItem.id, value)}
                    trackColor={{ false: Colors.light.border, true: Colors.light.success }}
                    thumbColor={selectedItem.isAvailable ? Colors.light.textInverse : Colors.light.textTertiary}
                  />
                </View>
                {selectedItem.stockLevel !== undefined && (
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Stock Level:</Text>
                    <Text style={styles.infoValue}>{selectedItem.stockLevel} units</Text>
                  </View>
                )}
              </View>

              {/* Dietary Information */}
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Dietary Information</Text>
                <View style={styles.dietaryTags}>
                  {selectedItem.isVegetarian && (
                    <View style={[styles.tag, { backgroundColor: Colors.light.success }]}>
                      <IconSymbol name="leaf" size={12} color={Colors.light.textInverse} />
                      <Text style={styles.tagText}>Vegetarian</Text>
                    </View>
                  )}
                  {selectedItem.isVegan && (
                    <View style={[styles.tag, { backgroundColor: Colors.light.secondary }]}>
                      <IconSymbol name="heart" size={12} color={Colors.light.textInverse} />
                      <Text style={styles.tagText}>Vegan</Text>
                    </View>
                  )}
                  {selectedItem.isGlutenFree && (
                    <View style={[styles.tag, { backgroundColor: Colors.light.warning }]}>
                      <IconSymbol name="checkmark.shield" size={12} color={Colors.light.textInverse} />
                      <Text style={styles.tagText}>Gluten Free</Text>
                    </View>
                  )}
                  {selectedItem.isSpicy && (
                    <View style={[styles.tag, { backgroundColor: Colors.light.error }]}>
                      <IconSymbol name="flame" size={12} color={Colors.light.textInverse} />
                      <Text style={styles.tagText}>Spicy</Text>
                    </View>
                  )}
                </View>
              </View>

              {/* Customizations */}
              {selectedItem.customizations && selectedItem.customizations.length > 0 && (
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Customizations</Text>
                  {selectedItem.customizations.map((customization, index) => (
                    <View key={index} style={styles.customizationItem}>
                      <Text style={styles.customizationName}>{customization.name}</Text>
                      <Text style={styles.customizationOptions}>
                        {customization.options.length} options: {customization.options.slice(0, 3).map(opt => opt.name).join(', ')}
                        {customization.options.length > 3 && '...'}
                      </Text>
                    </View>
                  ))}
                </View>
              )}

              {/* Sales Analytics */}
              {selectedItem.orderCount && (
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Sales Analytics</Text>
                  <View style={styles.analyticsGrid}>
                    <View style={styles.analyticsCard}>
                      <Text style={styles.analyticsNumber}>{selectedItem.orderCount}</Text>
                      <Text style={styles.analyticsLabel}>Total Orders</Text>
                    </View>
                    <View style={styles.analyticsCard}>
                      <Text style={styles.analyticsNumber}>${(selectedItem.orderCount * selectedItem.price).toFixed(0)}</Text>
                      <Text style={styles.analyticsLabel}>Revenue</Text>
                    </View>
                    <View style={styles.analyticsCard}>
                      <Text style={styles.analyticsNumber}>4.5</Text>
                      <Text style={styles.analyticsLabel}>Rating</Text>
                    </View>
                  </View>
                </View>
              )}

              {/* Action Buttons */}
              <View style={styles.modalActions}>
                <TouchableOpacity style={[styles.modalActionButton, { backgroundColor: Colors.light.primary }]}>
                  <IconSymbol name="pencil" size={20} color={Colors.light.textInverse} />
                  <Text style={styles.modalActionButtonText}>Edit Item</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.modalActionButton, { backgroundColor: Colors.light.secondary }]}>
                  <IconSymbol name="square.and.arrow.up" size={20} color={Colors.light.textInverse} />
                  <Text style={styles.modalActionButtonText}>Share</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.modalActionButton, { backgroundColor: Colors.light.error }]}>
                  <IconSymbol name="trash" size={20} color={Colors.light.textInverse} />
                  <Text style={styles.modalActionButtonText}>Delete</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          )}
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF3E0', // Golden food-themed background
  },

  // Enhanced Header Styles
  headerContainer: {
    backgroundColor: Colors.light.surface,
    paddingTop: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: Colors.light.text,
  },
  filterButton: {
    padding: 8,
    position: 'relative',
  },
  activeFilterButton: {
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 8,
  },
  filterBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: Colors.light.error,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  filterBadgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
  },
  viewModeButton: {
    padding: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  addButtonText: {
    color: Colors.light.textInverse,
    fontSize: 14,
    fontWeight: '600',
  },

  // Advanced Filters
  advancedFilters: {
    backgroundColor: Colors.light.surface,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  filterSection: {
    marginBottom: 12,
  },
  filterSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: Colors.light.backgroundSecondary,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  activeFilterChip: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  filterChipText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  activeFilterChipText: {
    color: Colors.light.textInverse,
  },
  sortControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortOrderButton: {
    padding: 8,
    marginLeft: 8,
  },
  dietaryFilters: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  clearFiltersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.error, // Use error color for better contrast with white text
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
    marginTop: 8,
  },
  clearFiltersText: {
    fontSize: 14,
    color: '#FFFFFF', // Snow white color
    fontWeight: '500',
  },

  // Bulk Actions
  bulkActionsBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 8,
    borderRadius: 8,
  },
  selectAllButton: {
    marginRight: 12,
  },
  selectedCount: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.textInverse,
    textAlign: 'center',
    fontWeight: '600',
  },
  bulkActions: {
    flexDirection: 'row',
    gap: 8,
  },
  bulkActionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  bulkActionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  filterContainer: {
    backgroundColor: Colors.light.surface,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  filterTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: Colors.light.backgroundSecondary,
    gap: 6,
  },
  activeFilterTab: {
    backgroundColor: Colors.light.primary,
  },
  filterTabText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: Colors.light.textInverse,
  },
  categoryCount: {
    backgroundColor: Colors.light.border,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  categoryCountText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.light.textSecondary,
  },
  listContainer: {
    padding: 16,
  },
  menuItemCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)', // Semi-transparent white for food theme
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
    borderWidth: 1,
    borderColor: 'rgba(220, 20, 60, 0.08)', // Very subtle red border
  },
  selectedItemCard: {
    borderWidth: 2,
    borderColor: Colors.light.primary,
  },
  unavailableItemCard: {
    opacity: 0.7,
  },
  selectionCheckbox: {
    position: 'absolute',
    top: 12,
    left: 12,
    zIndex: 1,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedBox: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  itemImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
    position: 'relative',
  },
  itemImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stockBadge: {
    position: 'absolute',
    bottom: 4,
    left: 4,
    right: 4,
    backgroundColor: Colors.light.error,
    borderRadius: 4,
    paddingVertical: 2,
    alignItems: 'center',
  },
  stockBadgeText: {
    fontSize: 8,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
  },
  itemContent: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  itemInfo: {
    flex: 1,
    marginRight: 16,
  },
  itemNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 8,
  },
  itemName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
    flex: 1,
  },
  popularBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    gap: 4,
  },
  popularText: {
    fontSize: 10,
    color: Colors.light.warning,
    fontWeight: '600',
  },
  itemDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  itemMeta: {
    marginTop: 8,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flexWrap: 'wrap',
  },
  itemPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  originalPrice: {
    fontSize: 14,
    color: Colors.light.textTertiary,
    textDecorationLine: 'line-through',
  },
  prepTime: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    backgroundColor: Colors.light.backgroundSecondary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  orderCount: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    backgroundColor: Colors.light.backgroundSecondary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  rating: {
    fontSize: 12,
    color: Colors.light.textTertiary,
  },
  itemActions: {
    alignItems: 'flex-end',
    gap: 8,
  },
  availabilityToggle: {
    alignItems: 'center',
    gap: 4,
  },
  availabilityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  quickActions: {
    flexDirection: 'row',
    gap: 8,
  },
  quickActionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  itemTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 8,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  customizationsContainer: {
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 8,
    borderRadius: 8,
  },
  customizationsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  customizationText: {
    fontSize: 11,
    color: Colors.light.textTertiary,
    marginBottom: 2,
  },
  customizationsPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 8,
    borderRadius: 8,
    marginTop: 8,
    gap: 6,
  },
  customizationsText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  stockInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 6,
    borderRadius: 6,
    marginTop: 8,
    gap: 4,
  },
  stockText: {
    fontSize: 11,
    color: Colors.light.textTertiary,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.light.textTertiary,
    textAlign: 'center',
    paddingHorizontal: 32,
  },

  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    backgroundColor: Colors.light.surface,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  closeButton: {
    padding: 8,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalImageContainer: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: 16,
    position: 'relative',
  },
  modalImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  modalPlaceholderImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 14,
    color: Colors.light.textTertiary,
    marginTop: 8,
  },
  editImageButton: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalSection: {
    marginBottom: 24,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  infoLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    color: Colors.light.text,
    flex: 1,
    textAlign: 'right',
  },
  availabilityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  dietaryTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  customizationItem: {
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  customizationName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  customizationOptions: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  analyticsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  analyticsCard: {
    flex: 1,
    backgroundColor: Colors.light.surface,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  analyticsNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  analyticsLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    paddingTop: 16,
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  modalActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  modalActionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
});
