import api, { ApiR<PERSON>ponse, PaginatedResponse, handleApiError } from './api';

// Notification types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  isRead: boolean;
  data?: any;
  userId: string;
  restaurantId: string;
  createdAt: string;
  readAt?: string;
}

export type NotificationType = 
  | 'order_new'
  | 'order_cancelled'
  | 'order_refund'
  | 'payment_received'
  | 'payment_failed'
  | 'staff_login'
  | 'menu_item_low_stock'
  | 'system_maintenance'
  | 'verification_approved'
  | 'verification_rejected'
  | 'review_new'
  | 'promotion_started'
  | 'promotion_ended';

export interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  notificationTypes: {
    [key in NotificationType]: {
      enabled: boolean;
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
}

export interface PushTokenData {
  token: string;
  platform: 'ios' | 'android';
  deviceId: string;
}

class NotificationsService {
  // Get notifications with pagination
  async getNotifications(
    page: number = 1,
    limit: number = 20,
    unreadOnly: boolean = false
  ): Promise<PaginatedResponse<Notification>> {
    try {
      const params = { page, limit };
      if (unreadOnly) {
        (params as any).unreadOnly = true;
      }
      
      const response = await api.get<PaginatedResponse<Notification>>('/notifications', { params });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get unread notification count
  async getUnreadCount(): Promise<ApiResponse<{ count: number }>> {
    try {
      const response = await api.get<ApiResponse<{ count: number }>>('/notifications/unread-count');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Mark notification as read
  async markAsRead(id: string): Promise<ApiResponse<Notification>> {
    try {
      const response = await api.patch<ApiResponse<Notification>>(`/notifications/${id}/read`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Mark all notifications as read
  async markAllAsRead(): Promise<ApiResponse<{ updated: number }>> {
    try {
      const response = await api.patch<ApiResponse<{ updated: number }>>('/notifications/mark-all-read');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Delete notification
  async deleteNotification(id: string): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.delete<ApiResponse<{ message: string }>>(`/notifications/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Delete all notifications
  async deleteAllNotifications(): Promise<ApiResponse<{ deleted: number }>> {
    try {
      const response = await api.delete<ApiResponse<{ deleted: number }>>('/notifications/delete-all');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get notification settings
  async getNotificationSettings(): Promise<ApiResponse<NotificationSettings>> {
    try {
      const response = await api.get<ApiResponse<NotificationSettings>>('/notifications/settings');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update notification settings
  async updateNotificationSettings(settings: Partial<NotificationSettings>): Promise<ApiResponse<NotificationSettings>> {
    try {
      const response = await api.put<ApiResponse<NotificationSettings>>('/notifications/settings', settings);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Register push token
  async registerPushToken(tokenData: PushTokenData): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.post<ApiResponse<{ message: string }>>('/notifications/register-token', tokenData);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Unregister push token
  async unregisterPushToken(deviceId: string): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.delete<ApiResponse<{ message: string }>>('/notifications/unregister-token', {
        data: { deviceId }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Send test notification
  async sendTestNotification(type: NotificationType): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.post<ApiResponse<{ message: string }>>('/notifications/test', { type });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get notification history
  async getNotificationHistory(
    startDate: string,
    endDate: string,
    type?: NotificationType
  ): Promise<ApiResponse<Array<{
    date: string;
    count: number;
    types: Array<{ type: NotificationType; count: number }>;
  }>>> {
    try {
      const params: any = { startDate, endDate };
      if (type) {
        params.type = type;
      }
      
      const response = await api.get<ApiResponse<Array<{
        date: string;
        count: number;
        types: Array<{ type: NotificationType; count: number }>;
      }>>>('/notifications/history', { params });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Bulk operations
  async bulkMarkAsRead(notificationIds: string[]): Promise<ApiResponse<{ updated: number }>> {
    try {
      const response = await api.patch<ApiResponse<{ updated: number }>>('/notifications/bulk-read', {
        notificationIds
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async bulkDelete(notificationIds: string[]): Promise<ApiResponse<{ deleted: number }>> {
    try {
      const response = await api.delete<ApiResponse<{ deleted: number }>>('/notifications/bulk-delete', {
        data: { notificationIds }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Subscribe to real-time notifications (WebSocket)
  async subscribeToRealTime(): Promise<WebSocket | null> {
    try {
      // This would typically use WebSocket or Server-Sent Events
      // Implementation depends on your backend setup
      const wsUrl = api.defaults.baseURL?.replace('http', 'ws') + '/notifications/subscribe';
      const ws = new WebSocket(wsUrl);
      return ws;
    } catch (error) {
      console.error('Failed to subscribe to real-time notifications:', error);
      return null;
    }
  }

  // Get notification templates (for customization)
  async getNotificationTemplates(): Promise<ApiResponse<Array<{
    type: NotificationType;
    title: string;
    message: string;
    variables: string[];
  }>>> {
    try {
      const response = await api.get<ApiResponse<Array<{
        type: NotificationType;
        title: string;
        message: string;
        variables: string[];
      }>>>('/notifications/templates');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update notification template
  async updateNotificationTemplate(
    type: NotificationType,
    template: { title: string; message: string }
  ): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.put<ApiResponse<{ message: string }>>(`/notifications/templates/${type}`, template);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export default new NotificationsService();
