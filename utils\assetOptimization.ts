import AsyncStorage from '@react-native-async-storage/async-storage';
import { Image } from 'expo-image';

// Asset cache configuration
const ASSET_CACHE_KEY = 'restaurant_app_asset_cache';
const CACHE_EXPIRY_TIME = 7 * 24 * 60 * 60 * 1000; // 7 days
const MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB

interface CachedAsset {
  uri: string;
  localPath?: string;
  timestamp: number;
  size: number;
  type: 'image' | 'icon' | 'font';
}

interface AssetCacheData {
  assets: Record<string, CachedAsset>;
  totalSize: number;
  lastCleanup: number;
}

export class AssetOptimizer {
  private static instance: AssetOptimizer;
  private cacheData: AssetCacheData = {
    assets: {},
    totalSize: 0,
    lastCleanup: Date.now()
  };

  static getInstance(): AssetOptimizer {
    if (!AssetOptimizer.instance) {
      AssetOptimizer.instance = new AssetOptimizer();
    }
    return AssetOptimizer.instance;
  }

  // Initialize asset cache
  async initialize(): Promise<void> {
    try {
      const cachedData = await AsyncStorage.getItem(ASSET_CACHE_KEY);
      if (cachedData) {
        this.cacheData = JSON.parse(cachedData);
        await this.cleanupExpiredAssets();
      }
    } catch (error) {
      console.warn('Failed to initialize asset cache:', error);
    }
  }

  // Optimize image for display
  optimizeImageSource(source: string | { uri: string }, options?: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  }): string | { uri: string } {
    if (typeof source === 'string') {
      return source; // Local asset, already optimized
    }

    const { uri } = source;
    const { width, height, quality = 80, format = 'webp' } = options || {};

    // For external URLs, add optimization parameters
    if (uri.startsWith('http')) {
      const url = new URL(uri);
      
      // Add optimization parameters
      if (width) url.searchParams.set('w', width.toString());
      if (height) url.searchParams.set('h', height.toString());
      url.searchParams.set('q', quality.toString());
      url.searchParams.set('f', format);
      url.searchParams.set('auto', 'compress');

      return { uri: url.toString() };
    }

    return source;
  }

  // Preload critical assets
  async preloadCriticalAssets(assets: string[]): Promise<void> {
    const preloadPromises = assets.map(async (asset) => {
      try {
        await Image.prefetch(asset);
        await this.addToCache(asset, 'image');
      } catch (error) {
        console.warn(`Failed to preload asset: ${asset}`, error);
      }
    });

    await Promise.all(preloadPromises);
  }

  // Add asset to cache
  private async addToCache(uri: string, type: CachedAsset['type']): Promise<void> {
    const timestamp = Date.now();
    const estimatedSize = this.estimateAssetSize(uri, type);

    // Check if cache is full
    if (this.cacheData.totalSize + estimatedSize > MAX_CACHE_SIZE) {
      await this.cleanupOldestAssets(estimatedSize);
    }

    this.cacheData.assets[uri] = {
      uri,
      timestamp,
      size: estimatedSize,
      type
    };

    this.cacheData.totalSize += estimatedSize;
    await this.saveCacheData();
  }

  // Estimate asset size based on type and URI
  private estimateAssetSize(uri: string, type: CachedAsset['type']): number {
    // Rough estimates for different asset types
    switch (type) {
      case 'image':
        return 500 * 1024; // 500KB average
      case 'icon':
        return 10 * 1024; // 10KB average
      case 'font':
        return 100 * 1024; // 100KB average
      default:
        return 100 * 1024; // 100KB default
    }
  }

  // Clean up expired assets
  private async cleanupExpiredAssets(): Promise<void> {
    const now = Date.now();
    const expiredAssets: string[] = [];

    Object.entries(this.cacheData.assets).forEach(([uri, asset]) => {
      if (now - asset.timestamp > CACHE_EXPIRY_TIME) {
        expiredAssets.push(uri);
      }
    });

    for (const uri of expiredAssets) {
      const asset = this.cacheData.assets[uri];
      this.cacheData.totalSize -= asset.size;
      delete this.cacheData.assets[uri];
    }

    if (expiredAssets.length > 0) {
      await this.saveCacheData();
    }
  }

  // Clean up oldest assets to make space
  private async cleanupOldestAssets(requiredSpace: number): Promise<void> {
    const sortedAssets = Object.entries(this.cacheData.assets)
      .sort(([, a], [, b]) => a.timestamp - b.timestamp);

    let freedSpace = 0;
    const assetsToRemove: string[] = [];

    for (const [uri, asset] of sortedAssets) {
      assetsToRemove.push(uri);
      freedSpace += asset.size;
      
      if (freedSpace >= requiredSpace) {
        break;
      }
    }

    for (const uri of assetsToRemove) {
      const asset = this.cacheData.assets[uri];
      this.cacheData.totalSize -= asset.size;
      delete this.cacheData.assets[uri];
    }

    await this.saveCacheData();
  }

  // Save cache data to AsyncStorage
  private async saveCacheData(): Promise<void> {
    try {
      await AsyncStorage.setItem(ASSET_CACHE_KEY, JSON.stringify(this.cacheData));
    } catch (error) {
      console.warn('Failed to save asset cache:', error);
    }
  }

  // Get cache statistics
  getCacheStats(): {
    totalAssets: number;
    totalSize: number;
    sizeInMB: number;
    lastCleanup: Date;
  } {
    return {
      totalAssets: Object.keys(this.cacheData.assets).length,
      totalSize: this.cacheData.totalSize,
      sizeInMB: this.cacheData.totalSize / (1024 * 1024),
      lastCleanup: new Date(this.cacheData.lastCleanup)
    };
  }

  // Clear all cached assets
  async clearCache(): Promise<void> {
    this.cacheData = {
      assets: {},
      totalSize: 0,
      lastCleanup: Date.now()
    };

    await AsyncStorage.removeItem(ASSET_CACHE_KEY);
    await Image.clearMemoryCache();
    await Image.clearDiskCache();
  }

  // Optimize font loading
  optimizeFontLoading(fonts: Record<string, any>): Record<string, any> {
    // Preload only essential fonts
    const essentialFonts = Object.entries(fonts).reduce((acc, [key, value]) => {
      // Only include fonts that are actually used
      if (key.includes('Regular') || key.includes('Bold')) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);

    return essentialFonts;
  }
}

// Utility functions for asset optimization
export const AssetUtils = {
  // Get optimized image dimensions for different screen densities
  getResponsiveImageSize: (baseWidth: number, baseHeight: number) => {
    const screenDensity = 2; // Assume 2x density for optimization
    return {
      width: baseWidth * screenDensity,
      height: baseHeight * screenDensity
    };
  },

  // Generate srcSet for responsive images
  generateImageSrcSet: (baseUrl: string, sizes: number[]) => {
    return sizes.map(size => ({
      uri: `${baseUrl}?w=${size}&q=80&f=webp`,
      width: size
    }));
  },

  // Check if image should be lazy loaded
  shouldLazyLoad: (imageIndex: number, visibleRange: number = 5) => {
    return imageIndex > visibleRange;
  },

  // Get placeholder for image loading
  getImagePlaceholder: (width: number, height: number) => {
    // Generate a simple placeholder based on dimensions
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f0f0f0"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999">Loading...</text>
      </svg>
    `)}`;
  }
};

// Initialize asset optimizer
export const assetOptimizer = AssetOptimizer.getInstance();
