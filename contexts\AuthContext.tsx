import { AuthContextType, mockUsers, User, UserRole } from '@/types/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [verificationStatus, setVerificationStatus] = useState<{
    canAccess: boolean;
    reason?: string;
  }>({ canAccess: true });

  // Load user from AsyncStorage on mount
  useEffect(() => {
    const loadUser = async () => {
      try {
        const savedUser = await AsyncStorage.getItem('restaurant_user');
        if (savedUser) {
          const parsedUser = JSON.parse(savedUser);
          setUser(parsedUser);
        }
      } catch (error) {
        console.error('Failed to load saved user:', error);
        await AsyncStorage.removeItem('restaurant_user');
      } finally {
        setIsLoading(false);
      }
    };
    loadUser();
  }, []);

  // Save user to AsyncStorage whenever user changes
  useEffect(() => {
    const saveUser = async () => {
      try {
        if (user) {
          await AsyncStorage.setItem('restaurant_user', JSON.stringify(user));
        } else {
          await AsyncStorage.removeItem('restaurant_user');
        }
      } catch (error) {
        console.error('Failed to save user:', error);
      }
    };
    saveUser();
  }, [user]);

  const validateCredentials = async (username: string, password: string): Promise<User | null> => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Find user in mock data without setting user in context
    const foundUser = mockUsers.find(
      u => u.username === username && u.password === password
    );

    return foundUser || null;
  };

  const login = async (username: string, password: string): Promise<User | null> => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Find user in mock data
    const foundUser = mockUsers.find(
      u => u.username === username && u.password === password
    );

    if (foundUser) {
      setUser(foundUser);
      return foundUser;
    }

    return null;
  };

  const logout = async () => {
    try {
      // Use authService for logout if available, otherwise fallback to local logout
      if (typeof authService?.logout === 'function') {
        await authService.logout();
      } else {
        await AsyncStorage.multiRemove(['userData', 'userRole']);
      }
      setUser(null);
      setVerificationStatus({ canAccess: true });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const checkVerificationStatus = async () => {
    try {
      // Use authService if available, otherwise skip verification for demo accounts
      if (typeof authService?.canAccessRestaurant === 'function') {
        const result = await authService.canAccessRestaurant();
        setVerificationStatus(result);
        return result;
      } else {
        // Fallback for demo accounts
        setVerificationStatus({ canAccess: true });
        return { canAccess: true };
      }
    } catch (error) {
      console.error('Error checking verification:', error);
      setVerificationStatus({ canAccess: false, reason: 'Error checking verification status' });
      return { canAccess: false, reason: 'Error checking verification status' };
    }
  };

  const hasRole = (roles: UserRole | UserRole[]): boolean => {
    if (!user) return false;

    if (Array.isArray(roles)) {
      return roles.includes(user.role);
    }

    return user.role === roles;
  };

  const value: AuthContextType = {
    user,
    validateCredentials,
    login,
    logout,
    isAuthenticated: !!user,
    hasRole,
    verificationStatus,
    checkVerificationStatus
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#f97316" />
      </View>
    );
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook for checking permissions
export function usePermissions() {
  const { user, hasRole } = useAuth();

  const canAccess = (permission: string): boolean => {
    if (!user) return false;

    const rolePermissions = {
      owner: ['dashboard', 'orders', 'menu', 'analytics', 'settings', 'staff-management'],
      admin: ['dashboard', 'orders', 'menu', 'analytics', 'settings'],
      staff: ['orders']
    };

    return rolePermissions[user.role]?.includes(permission) || false;
  };

  return {
    canAccess,
    hasRole,
    userRole: user?.role
  };
}
