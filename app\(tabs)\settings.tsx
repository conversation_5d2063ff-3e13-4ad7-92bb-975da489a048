import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { useAuth } from '@/contexts/AuthContext';
import { useThemeColors } from '@/contexts/SimpleThemeContext';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchRestaurantInfo, updateRestaurantInfo } from '@/store/slices/restaurantSlice';
import { Restaurant } from '@/types';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import { Alert, ScrollView, StyleSheet, Switch, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function SettingsScreen() {
  return (
    <ProtectedRoute requiredPermission="settings">
      <SettingsContent />
    </ProtectedRoute>
  );
}

function SettingsContent() {
  const dispatch = useAppDispatch();
  const { restaurant } = useAppSelector(state => state.restaurant);
  const { logout } = useAuth();
  const themeColors = useThemeColors();
  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValues, setTempValues] = useState<Partial<Restaurant>>({});

  useEffect(() => {
    if (!restaurant) {
      dispatch(fetchRestaurantInfo());
    }
  }, [dispatch, restaurant]);

  const handleEdit = (field: string, currentValue: any) => {
    setEditingField(field);
    setTempValues({ [field]: currentValue });
  };

  const handleSave = (field: string) => {
    if (tempValues[field as keyof Restaurant] !== undefined) {
      dispatch(updateRestaurantInfo({ [field]: tempValues[field as keyof Restaurant] }));
      setEditingField(null);
      setTempValues({});
    }
  };

  const handleCancel = () => {
    setEditingField(null);
    setTempValues({});
  };

  const handleToggle = (field: string, value: boolean) => {
    dispatch(updateRestaurantInfo({ [field]: value }));
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            logout();
            // The AuthWrapper will automatically redirect to login screen
          },
        },
      ]
    );
  };

  // const formatOperatingHours = (hours: any[]) => {
  //   const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  //   return hours.map(h =>
  //     `${days[h.dayOfWeek]}: ${h.isClosed ? 'Closed' : `${h.openTime} - ${h.closeTime}`}`
  //   ).join('\n');
  // }; // Commented out unused function

  const renderEditableField = (
    label: string,
    field: string,
    value: any,
    type: 'text' | 'number' = 'text',
    multiline = false
  ) => (
    <View style={styles.settingItem}>
      <Text style={styles.settingLabel}>{label}</Text>
      {editingField === field ? (
        <View style={styles.editContainer}>
          <TextInput
            style={[styles.input, multiline && styles.multilineInput]}
            value={String(tempValues[field as keyof Restaurant] || '')}
            onChangeText={(text) => setTempValues({ ...tempValues, [field]: type === 'number' ? parseFloat(text) || 0 : text })}
            keyboardType={type === 'number' ? 'numeric' : 'default'}
            multiline={multiline}
            autoFocus
          />
          <View style={styles.editActions}>
            <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={() => handleSave(field)}>
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <TouchableOpacity style={styles.valueContainer} onPress={() => handleEdit(field, value)}>
          <Text style={styles.settingValue}>{String(value)}</Text>
          <IconSymbol name="pencil" size={16} color={themeColors.textTertiary} />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderToggleField = (label: string, field: string, value: boolean, description?: string) => (
    <View style={styles.settingItem}>
      <View style={styles.toggleContainer}>
        <View>
          <Text style={styles.settingLabel}>{label}</Text>
          {description && <Text style={styles.settingDescription}>{description}</Text>}
        </View>
        <Switch
          value={value}
          onValueChange={(newValue) => handleToggle(field, newValue)}
          trackColor={{ false: themeColors.border, true: themeColors.success }}
          thumbColor={value ? themeColors.textInverse : themeColors.textTertiary}
        />
      </View>
    </View>
  );

  if (!restaurant) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading restaurant information...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="transparent" translucent={false} />

      {/* Enhanced Header */}
      <LinearGradient
        colors={[themeColors.primary, themeColors.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Restaurant Settings</Text>
            <Text style={styles.headerSubtitle}>Manage your restaurant preferences</Text>
          </View>
          <View style={styles.headerIcon}>
            <IconSymbol name="gear" size={28} color={themeColors.textInverse} />
          </View>
        </View>
      </LinearGradient>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>



        {/* Restaurant Status Card */}
        <View style={styles.cardContainer}>
          <LinearGradient
            colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,0.85)']}
            style={styles.card}
          >
            <View style={styles.cardHeader}>
              <View style={styles.cardTitleContainer}>
                <IconSymbol name="power" size={20} color={themeColors.primary} />
                <Text style={styles.cardTitle}>Restaurant Status</Text>
              </View>
            </View>
            <View style={styles.cardContent}>
              {renderToggleField(
                'Restaurant Open',
                'isOpen',
                restaurant.isOpen,
                'Toggle to open/close your restaurant'
              )}
              {renderToggleField(
                'Accepting Orders',
                'acceptingOrders',
                restaurant.acceptingOrders,
                'Control whether to accept new orders'
              )}
            </View>
          </LinearGradient>
        </View>

        {/* Basic Information Card */}
        <View style={styles.cardContainer}>
          <LinearGradient
            colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,0.85)']}
            style={styles.card}
          >
            <View style={styles.cardHeader}>
              <View style={styles.cardTitleContainer}>
                <IconSymbol name="info.circle" size={20} color={themeColors.primary} />
                <Text style={styles.cardTitle}>Basic Information</Text>
              </View>
            </View>
            <View style={styles.cardContent}>
              {renderEditableField('Restaurant Name', 'name', restaurant.name)}
              {renderEditableField('Description', 'description', restaurant.description, 'text', true)}
              {renderEditableField('Phone', 'phone', restaurant.phone)}
              {renderEditableField('Email', 'email', restaurant.email)}
              {renderEditableField('Website', 'website', restaurant.website || '')}
            </View>
          </LinearGradient>
        </View>

        {/* Address Card */}
        <View style={styles.cardContainer}>
          <LinearGradient
            colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,0.85)']}
            style={styles.card}
          >
            <View style={styles.cardHeader}>
              <View style={styles.cardTitleContainer}>
                <IconSymbol name="location" size={20} color={themeColors.primary} />
                <Text style={styles.cardTitle}>Address</Text>
              </View>
            </View>
            <View style={styles.cardContent}>
              {renderEditableField('Street', 'address.street', restaurant.address.street)}
              {renderEditableField('City', 'address.city', restaurant.address.city)}
              {renderEditableField('State', 'address.state', restaurant.address.state)}
            </View>
          </LinearGradient>
        </View>





        {/* App Settings Card */}
        <View style={styles.cardContainer}>
          <LinearGradient
            colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,0.85)']}
            style={styles.card}
          >
            <View style={styles.cardHeader}>
              <View style={styles.cardTitleContainer}>
                <IconSymbol name="gear" size={20} color={Colors.light.primary} />
                <Text style={styles.cardTitle}>App Settings</Text>
              </View>
            </View>
            <View style={styles.cardContent}>
              <TouchableOpacity
                style={styles.enhancedSettingItem}
                onPress={() => router.push('/settings/notifications')}
              >
                <View style={styles.settingRow}>
                  <View style={styles.settingIconContainer}>
                    <IconSymbol name="bell" size={20} color={Colors.light.primary} />
                  </View>
                  <View style={styles.settingTextContainer}>
                    <Text style={styles.settingLabel}>Notifications</Text>
                    <Text style={styles.settingDescription}>Manage your notification preferences</Text>
                  </View>
                  <IconSymbol name="chevron.right" size={16} color={Colors.light.textTertiary} />
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.enhancedSettingItem}
                onPress={() => router.push('/settings/help')}
              >
                <View style={styles.settingRow}>
                  <View style={styles.settingIconContainer}>
                    <IconSymbol name="questionmark.circle" size={20} color={Colors.light.primary} />
                  </View>
                  <View style={styles.settingTextContainer}>
                    <Text style={styles.settingLabel}>Help & Support</Text>
                    <Text style={styles.settingDescription}>Get help and contact support</Text>
                  </View>
                  <IconSymbol name="chevron.right" size={16} color={Colors.light.textTertiary} />
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.enhancedSettingItem}
                onPress={() => router.push('/settings/about')}
              >
                <View style={styles.settingRow}>
                  <View style={styles.settingIconContainer}>
                    <IconSymbol name="info.circle" size={20} color={Colors.light.primary} />
                  </View>
                  <View style={styles.settingTextContainer}>
                    <Text style={styles.settingLabel}>About</Text>
                    <Text style={styles.settingDescription}>App version and information</Text>
                  </View>
                  <IconSymbol name="chevron.right" size={16} color={Colors.light.textTertiary} />
                </View>
              </TouchableOpacity>
            </View>
          </LinearGradient>
        </View>

        {/* Logout Section */}
        <View style={styles.cardContainer}>
          <LinearGradient
            colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,0.85)']}
            style={styles.card}
          >
            <View style={styles.cardHeader}>
              <View style={styles.cardTitleContainer}>
                <IconSymbol name="power" size={20} color="#DC143C" />
                <Text style={[styles.cardTitle, { color: '#DC143C' }]}>Account</Text>
              </View>
            </View>
            <View style={styles.cardContent}>
              <TouchableOpacity
                style={[styles.enhancedSettingItem, styles.logoutButton]}
                onPress={handleLogout}
              >
                <View style={styles.settingRow}>
                  <View style={[styles.settingIconContainer, styles.logoutIconContainer]}>
                    <IconSymbol name="arrow.right.square" size={20} color="#FFFFFF" />
                  </View>
                  <View style={styles.settingTextContainer}>
                    <Text style={[styles.settingLabel, styles.logoutText]}>Logout</Text>
                    <Text style={styles.settingDescription}>Sign out of your account</Text>
                  </View>
                  <IconSymbol name="chevron.right" size={16} color="#DC143C" />
                </View>
              </TouchableOpacity>
            </View>
          </LinearGradient>
        </View>

        {/* Bottom spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F5E8', // Fresh chef-themed background
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E8F5E8', // Fresh chef-themed background
  },
  loadingText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.8)',
  },
  headerIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  cardContainer: {
    marginTop: 20,
    marginBottom: 10,
  },
  card: {
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  cardHeader: {
    marginBottom: 20,
  },
  cardTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginLeft: 12,
  },
  cardContent: {
    gap: 8,
  },
  settingItem: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  enhancedSettingItem: {
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderRadius: 12,
    marginVertical: 4,
    backgroundColor: 'rgba(255,255,255,0.5)',
  },
  settingIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingTextContainer: {
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    color: Colors.light.text,
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginTop: 2,
    lineHeight: 18,
  },
  settingValue: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    flex: 1,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  editContainer: {
    marginTop: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    backgroundColor: Colors.light.background,
    marginBottom: 8,
  },
  multilineInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  editActions: {
    flexDirection: 'row',
    gap: 8,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  saveButtonText: {
    fontSize: 14,
    color: Colors.light.textInverse,
    fontWeight: '500',
  },
  hoursContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 12,
    borderRadius: 8,
    marginTop: 4,
  },
  hoursText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    flex: 1,
    lineHeight: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 16,
    borderRadius: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  logoutButton: {
    borderWidth: 1,
    borderColor: 'rgba(255, 68, 68, 0.2)',
    backgroundColor: 'rgba(255, 68, 68, 0.05)',
  },
  logoutIconContainer: {
    backgroundColor: '#DC143C',
  },
  logoutText: {
    color: '#DC143C',
    fontWeight: '600',
  },
  bottomSpacing: {
    height: 40,
  },
});
