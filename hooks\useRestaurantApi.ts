import { useApi, useApiMutation, usePollingApi } from './useApi';
import { restaurantService, Restaurant, RestaurantStats, RestaurantSettings } from '@/services';

// Restaurant profile hooks
export function useRestaurant() {
  return useApi(() => restaurantService.getRestaurant());
}

export function useUpdateRestaurant() {
  return useApiMutation((data: Partial<Restaurant>) => 
    restaurantService.updateRestaurant(data)
  );
}

// Restaurant statistics hooks
export function useRestaurantStats(period?: 'today' | 'week' | 'month' | 'year') {
  return useApi(() => restaurantService.getStats(period), [period]);
}

// Restaurant settings hooks
export function useRestaurantSettings() {
  return useApi(() => restaurantService.getSettings());
}

export function useUpdateRestaurantSettings() {
  return useApiMutation((settings: Partial<RestaurantSettings>) => 
    restaurantService.updateSettings(settings)
  );
}

// Toggle restaurant status
export function useToggleRestaurantStatus() {
  return useApiMutation((isOpen: boolean) => 
    restaurantService.toggleStatus(isOpen)
  );
}

// Image upload hooks
export function useUploadRestaurantImage() {
  return useApiMutation(({ imageUri, type }: { imageUri: string; type: 'logo' | 'cover' | 'gallery' }) => 
    restaurantService.uploadImage(imageUri, type)
  );
}

export function useDeleteRestaurantImage() {
  return useApiMutation((imageUrl: string) => 
    restaurantService.deleteImage(imageUrl)
  );
}

// Analytics hooks
export function useRevenueAnalytics(startDate: string, endDate: string, groupBy: 'day' | 'week' | 'month' = 'day') {
  return useApi(() => restaurantService.getRevenueAnalytics(startDate, endDate, groupBy), [startDate, endDate, groupBy]);
}

export function useTopSellingItems(limit: number = 10) {
  return useApi(() => restaurantService.getTopSellingItems(limit), [limit]);
}

export function useCustomerAnalytics() {
  return useApi(() => restaurantService.getCustomerAnalytics());
}

// Real-time stats with polling
export function useRealTimeStats(enabled: boolean = true) {
  return usePollingApi(() => restaurantService.getStats('today'), 30000, enabled);
}
