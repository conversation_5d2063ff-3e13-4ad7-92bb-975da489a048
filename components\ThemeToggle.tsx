import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { useSimpleTheme } from '@/contexts/SimpleThemeContext';
import { IconSymbol } from '@/components/ui/IconSymbol';

interface ThemeToggleProps {
  showLabel?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: any;
}

export function ThemeToggle({ showLabel = true, size = 'medium', style }: ThemeToggleProps) {
  const { theme, isDark, toggleTheme, colors } = useSimpleTheme();
  
  const getIconSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'large': return 28;
      default: return 20;
    }
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return 'sun.max.fill';
      case 'dark':
        return 'moon.fill';
      case 'system':
        return 'gear';
      default:
        return 'sun.max.fill';
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case 'light':
        return 'Light Mode';
      case 'dark':
        return 'Dark Mode';
      case 'system':
        return 'System';
      default:
        return 'Light Mode';
    }
  };

  const styles = createStyles(colors, size);

  return (
    <TouchableOpacity 
      style={[styles.container, style]} 
      onPress={toggleTheme}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <IconSymbol 
          name={getThemeIcon()} 
          size={getIconSize()} 
          color={colors.primary} 
        />
      </View>
      {showLabel && (
        <Text style={styles.label}>{getThemeLabel()}</Text>
      )}
    </TouchableOpacity>
  );
}

const createStyles = (colors: any, size: string) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: size === 'small' ? 8 : size === 'large' ? 16 : 12,
    paddingHorizontal: size === 'small' ? 8 : size === 'large' ? 16 : 12,
    paddingVertical: size === 'small' ? 6 : size === 'large' ? 12 : 8,
    borderWidth: 1,
    borderColor: colors.border,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    marginRight: 8,
  },
  label: {
    fontSize: size === 'small' ? 12 : size === 'large' ? 16 : 14,
    fontWeight: '500',
    color: colors.text,
  },
});

// Simple icon-only toggle button
export function ThemeToggleIcon({ size = 20, style }: { size?: number; style?: any }) {
  const { theme, toggleTheme, colors } = useSimpleTheme();
  
  const getThemeIcon = () => {
    switch (theme) {
      case 'light': return 'sun.max.fill';
      case 'dark': return 'moon.fill';
      case 'system': return 'gear';
      default: return 'sun.max.fill';
    }
  };

  return (
    <TouchableOpacity 
      style={[{ padding: 8 }, style]} 
      onPress={toggleTheme}
      activeOpacity={0.7}
    >
      <IconSymbol 
        name={getThemeIcon()} 
        size={size} 
        color={colors.primary} 
      />
    </TouchableOpacity>
  );
}

// Theme selector with all three options
export function ThemeSelector() {
  const { theme, setTheme, colors } = useSimpleTheme();
  
  const options = [
    { key: 'light', label: 'Light', icon: 'sun.max.fill' },
    { key: 'dark', label: 'Dark', icon: 'moon.fill' },
    { key: 'system', label: 'System', icon: 'gear' },
  ] as const;

  const styles = createSelectorStyles(colors);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Theme</Text>
      <View style={styles.optionsContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.key}
            style={[
              styles.option,
              theme === option.key && styles.selectedOption,
            ]}
            onPress={() => setTheme(option.key)}
            activeOpacity={0.7}
          >
            <IconSymbol 
              name={option.icon} 
              size={18} 
              color={theme === option.key ? colors.primary : colors.textSecondary} 
            />
            <Text style={[
              styles.optionText,
              theme === option.key && styles.selectedOptionText,
            ]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const createSelectorStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  optionsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
    padding: 4,
  },
  option: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  selectedOption: {
    backgroundColor: colors.surface,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
    marginLeft: 6,
  },
  selectedOptionText: {
    color: colors.primary,
    fontWeight: '600',
  },
});
