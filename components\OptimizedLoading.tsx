import { useFadeAnimation, useLoadingAnimation, useScaleAnimation } from '@/utils/animationOptimization';
import React, { memo, useEffect } from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import Animated from 'react-native-reanimated';

interface OptimizedLoadingProps {
  visible: boolean;
  type?: 'spinner' | 'pulse' | 'fade' | 'skeleton';
  size?: 'small' | 'medium' | 'large';
  color?: string;
  text?: string;
  overlay?: boolean;
  style?: ViewStyle;
}

const OptimizedLoading: React.FC<OptimizedLoadingProps> = memo(({
  visible,
  type = 'spinner',
  size = 'medium',
  color = '#FF6B35',
  text,
  overlay = false,
  style
}) => {
  const { startLoading, stopLoading, animatedStyle: loadingStyle } = useLoadingAnimation();
  const { fadeIn, fadeOut, animatedStyle: fadeStyle } = useFadeAnimation(0);
  const { pulse, animatedStyle: pulseStyle } = useScaleAnimation();

  useEffect(() => {
    if (visible) {
      fadeIn();
      if (type === 'pulse') {
        pulse();
      } else {
        startLoading();
      }
    } else {
      fadeOut();
      stopLoading();
    }
  }, [visible, type, fadeIn, fadeOut, startLoading, stopLoading, pulse]);

  if (!visible) return null;

  const getSize = () => {
    switch (size) {
      case 'small': return 20;
      case 'large': return 40;
      default: return 30;
    }
  };

  const renderContent = () => {
    switch (type) {
      case 'pulse':
        return (
          <Animated.View style={[styles.pulseContainer, pulseStyle]}>
            <View style={[styles.pulseCircle, { backgroundColor: color }]} />
          </Animated.View>
        );

      case 'fade':
        return (
          <Animated.View style={loadingStyle}>
            <ActivityIndicator size={getSize()} color={color} />
          </Animated.View>
        );

      case 'skeleton':
        return <SkeletonLoader />;

      default:
        return <ActivityIndicator size={getSize()} color={color} />;
    }
  };

  const containerStyle = [
    styles.container,
    overlay && styles.overlay,
    style
  ];

  return (
    <Animated.View style={[containerStyle, fadeStyle]}>
      {renderContent()}
      {text && (
        <Text style={[styles.text, { color }]}>{text}</Text>
      )}
    </Animated.View>
  );
});

OptimizedLoading.displayName = 'OptimizedLoading';

// Skeleton loader component
const SkeletonLoader: React.FC = memo(() => {
  const { animatedStyle } = useLoadingAnimation();

  return (
    <View style={styles.skeletonContainer}>
      <Animated.View style={[styles.skeletonLine, styles.skeletonTitle, animatedStyle]} />
      <Animated.View style={[styles.skeletonLine, styles.skeletonSubtitle, animatedStyle]} />
      <Animated.View style={[styles.skeletonLine, styles.skeletonText, animatedStyle]} />
    </View>
  );
});

SkeletonLoader.displayName = 'SkeletonLoader';

// Optimized button loading state
interface LoadingButtonProps {
  loading: boolean;
  children: React.ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  loadingColor?: string;
  disabled?: boolean;
}

export const LoadingButton: React.FC<LoadingButtonProps> = memo(({
  loading,
  children,
  onPress,
  style,
  loadingColor = '#FFFFFF',
  disabled = false
}) => {
  const { fadeIn, fadeOut, animatedStyle } = useFadeAnimation(loading ? 0 : 1);

  useEffect(() => {
    if (loading) {
      fadeOut();
    } else {
      fadeIn();
    }
  }, [loading, fadeIn, fadeOut]);

  return (
    <TouchableOpacity
      style={[styles.button, style, (loading || disabled) && styles.buttonDisabled]}
      onPress={loading || disabled ? undefined : onPress}
      activeOpacity={0.7}
    >
      {loading && (
        <View style={styles.buttonLoadingContainer}>
          <ActivityIndicator size="small" color={loadingColor} />
        </View>
      )}
      <Animated.View style={[styles.buttonContent, animatedStyle]}>
        {children}
      </Animated.View>
    </TouchableOpacity>
  );
});

LoadingButton.displayName = 'LoadingButton';

// Optimized list loading component
interface ListLoadingProps {
  visible: boolean;
  itemCount?: number;
  itemHeight?: number;
}

export const ListLoading: React.FC<ListLoadingProps> = memo(({
  visible,
  itemCount = 5,
  itemHeight = 60
}) => {
  const { animatedStyle } = useLoadingAnimation();

  if (!visible) return null;

  return (
    <View style={styles.listLoadingContainer}>
      {Array.from({ length: itemCount }).map((_, index) => (
        <Animated.View
          key={index}
          style={[
            styles.listLoadingItem,
            { height: itemHeight },
            animatedStyle
          ]}
        />
      ))}
    </View>
  );
});

ListLoading.displayName = 'ListLoading';

// Optimized image loading placeholder
interface ImageLoadingProps {
  width: number;
  height: number;
  borderRadius?: number;
}

export const ImageLoading: React.FC<ImageLoadingProps> = memo(({
  width,
  height,
  borderRadius = 0
}) => {
  const { animatedStyle } = useLoadingAnimation();

  return (
    <Animated.View
      style={[
        styles.imageLoading,
        {
          width,
          height,
          borderRadius,
        },
        animatedStyle
      ]}
    />
  );
});

ImageLoading.displayName = 'ImageLoading';

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    zIndex: 1000,
  },
  text: {
    marginTop: 10,
    fontSize: 14,
    fontWeight: '500',
  },
  pulseContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  pulseCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  skeletonContainer: {
    padding: 16,
    width: '100%',
  },
  skeletonLine: {
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginBottom: 8,
  },
  skeletonTitle: {
    height: 20,
    width: '70%',
  },
  skeletonSubtitle: {
    height: 16,
    width: '50%',
  },
  skeletonText: {
    height: 14,
    width: '90%',
  },
  button: {
    position: 'relative',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#FF6B35',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonLoadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  listLoadingContainer: {
    padding: 16,
  },
  listLoadingItem: {
    backgroundColor: '#E0E0E0',
    borderRadius: 8,
    marginBottom: 12,
  },
  imageLoading: {
    backgroundColor: '#E0E0E0',
  },
});

export default OptimizedLoading;
