import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { AppIcon, RestaurantIcons } from './AppIcon';
import { PremiumColors } from '../../constants/PremiumTheme';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  iconCount: number;
  testName: string;
}

export const IconPerformanceTest: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<PerformanceMetrics[]>([]);
  const [currentTest, setCurrentTest] = useState<string>('');

  // Performance measurement utility
  const measurePerformance = useCallback(async (testName: string, testFunction: () => void): Promise<PerformanceMetrics> => {
    const startTime = performance.now();
    const startMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    testFunction();
    
    const endTime = performance.now();
    const endMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    return {
      testName,
      renderTime: endTime - startTime,
      memoryUsage: endMemory - startMemory,
      iconCount: 0, // Will be set by individual tests
    };
  }, []);

  // Test 1: Render 100 icons of different types
  const testMassIconRendering = useCallback(async () => {
    setCurrentTest('Mass Icon Rendering (100 icons)');
    
    const testComponent = () => {
      const icons = [];
      const allIcons = Object.values(RestaurantIcons).flatMap(category => Object.values(category));
      
      for (let i = 0; i < 100; i++) {
        const iconName = allIcons[i % allIcons.length];
        icons.push(
          <AppIcon
            key={i}
            name={iconName}
            size="md"
            color="primary"
          />
        );
      }
      return icons;
    };

    const metrics = await measurePerformance('Mass Icon Rendering', testComponent);
    metrics.iconCount = 100;
    return metrics;
  }, [measurePerformance]);

  // Test 2: Render icons with different sizes
  const testSizeVariations = useCallback(async () => {
    setCurrentTest('Size Variations Test');
    
    const testComponent = () => {
      const sizes = ['xs', 'sm', 'md', 'lg', 'xl'] as const;
      const icons = [];
      
      for (let i = 0; i < 50; i++) {
        const size = sizes[i % sizes.length];
        icons.push(
          <AppIcon
            key={i}
            name="star"
            size={size}
            color="primary"
          />
        );
      }
      return icons;
    };

    const metrics = await measurePerformance('Size Variations', testComponent);
    metrics.iconCount = 50;
    return metrics;
  }, [measurePerformance]);

  // Test 3: Render icons with different colors
  const testColorVariations = useCallback(async () => {
    setCurrentTest('Color Variations Test');
    
    const testComponent = () => {
      const colors = ['primary', 'success', 'warning', 'danger', 'black'] as const;
      const icons = [];
      
      for (let i = 0; i < 50; i++) {
        const color = colors[i % colors.length];
        icons.push(
          <AppIcon
            key={i}
            name="heart"
            size="md"
            color={color}
          />
        );
      }
      return icons;
    };

    const metrics = await measurePerformance('Color Variations', testComponent);
    metrics.iconCount = 50;
    return metrics;
  }, [measurePerformance]);

  // Test 4: Rapid icon switching
  const testRapidSwitching = useCallback(async () => {
    setCurrentTest('Rapid Icon Switching Test');
    
    const testComponent = () => {
      const icons = Object.values(RestaurantIcons.dashboard);
      const components = [];
      
      for (let i = 0; i < 20; i++) {
        const iconName = icons[i % icons.length];
        components.push(
          <AppIcon
            key={i}
            name={iconName}
            size="lg"
            color="primary"
          />
        );
      }
      return components;
    };

    const metrics = await measurePerformance('Rapid Switching', testComponent);
    metrics.iconCount = 20;
    return metrics;
  }, [measurePerformance]);

  // Test 5: Restaurant-specific icon categories
  const testRestaurantCategories = useCallback(async () => {
    setCurrentTest('Restaurant Categories Test');
    
    const testComponent = () => {
      const categories = Object.keys(RestaurantIcons) as Array<keyof typeof RestaurantIcons>;
      const icons = [];
      
      categories.forEach((category, categoryIndex) => {
        const categoryIcons = Object.values(RestaurantIcons[category]);
        categoryIcons.forEach((iconName, iconIndex) => {
          if (icons.length < 75) { // Limit to 75 icons
            icons.push(
              <AppIcon
                key={`${categoryIndex}-${iconIndex}`}
                name={iconName}
                size="md"
                color="primary"
              />
            );
          }
        });
      });
      
      return icons;
    };

    const metrics = await measurePerformance('Restaurant Categories', testComponent);
    metrics.iconCount = 75;
    return metrics;
  }, [measurePerformance]);

  // Run all performance tests
  const runAllTests = useCallback(async () => {
    setIsRunning(true);
    setResults([]);
    
    try {
      const tests = [
        testMassIconRendering,
        testSizeVariations,
        testColorVariations,
        testRapidSwitching,
        testRestaurantCategories,
      ];

      const testResults: PerformanceMetrics[] = [];
      
      for (const test of tests) {
        const result = await test();
        testResults.push(result);
        setResults([...testResults]); // Update results incrementally
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      setCurrentTest('All tests completed!');
      
      // Show summary
      const avgRenderTime = testResults.reduce((sum, r) => sum + r.renderTime, 0) / testResults.length;
      const totalIcons = testResults.reduce((sum, r) => sum + r.iconCount, 0);
      
      Alert.alert(
        'Performance Test Complete',
        `Average render time: ${avgRenderTime.toFixed(2)}ms\nTotal icons tested: ${totalIcons}`,
        [{ text: 'OK' }]
      );
      
    } catch (error) {
      Alert.alert('Test Error', 'An error occurred during testing');
      console.error('Performance test error:', error);
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  }, [testMassIconRendering, testSizeVariations, testColorVariations, testRapidSwitching, testRestaurantCategories]);

  // Get performance rating
  const getPerformanceRating = (renderTime: number): { rating: string; color: string } => {
    if (renderTime < 5) return { rating: 'Excellent', color: PremiumColors.success };
    if (renderTime < 15) return { rating: 'Good', color: PremiumColors.warning };
    if (renderTime < 30) return { rating: 'Fair', color: PremiumColors.primary };
    return { rating: 'Poor', color: PremiumColors.danger };
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <AppIcon name="speedometer" size="xl" color="white" />
        <Text style={styles.title}>Icon Performance Test</Text>
        <Text style={styles.subtitle}>Comprehensive performance analysis</Text>
      </View>

      {/* Test Controls */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={[styles.testButton, isRunning && styles.testButtonDisabled]}
          onPress={runAllTests}
          disabled={isRunning}
        >
          <AppIcon 
            name={isRunning ? "loading" : "play-circle"} 
            size="md" 
            color="white" 
          />
          <Text style={styles.testButtonText}>
            {isRunning ? 'Running Tests...' : 'Run Performance Tests'}
          </Text>
        </TouchableOpacity>

        {currentTest && (
          <View style={styles.currentTestContainer}>
            <AppIcon name="clock-outline" size="sm" color="primary" />
            <Text style={styles.currentTestText}>{currentTest}</Text>
          </View>
        )}
      </View>

      {/* Test Results */}
      {results.length > 0 && (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>Test Results</Text>
          
          {results.map((result, index) => {
            const performance = getPerformanceRating(result.renderTime);
            
            return (
              <View key={index} style={styles.resultCard}>
                <View style={styles.resultHeader}>
                  <Text style={styles.resultTestName}>{result.testName}</Text>
                  <View style={[styles.performanceBadge, { backgroundColor: performance.color }]}>
                    <Text style={styles.performanceBadgeText}>{performance.rating}</Text>
                  </View>
                </View>
                
                <View style={styles.resultMetrics}>
                  <View style={styles.metricItem}>
                    <AppIcon name="timer-outline" size="sm" color="gray" />
                    <Text style={styles.metricLabel}>Render Time</Text>
                    <Text style={styles.metricValue}>{result.renderTime.toFixed(2)}ms</Text>
                  </View>
                  
                  <View style={styles.metricItem}>
                    <AppIcon name="counter" size="sm" color="gray" />
                    <Text style={styles.metricLabel}>Icon Count</Text>
                    <Text style={styles.metricValue}>{result.iconCount}</Text>
                  </View>
                  
                  {result.memoryUsage > 0 && (
                    <View style={styles.metricItem}>
                      <AppIcon name="memory" size="sm" color="gray" />
                      <Text style={styles.metricLabel}>Memory</Text>
                      <Text style={styles.metricValue}>
                        {(result.memoryUsage / 1024).toFixed(1)}KB
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            );
          })}
        </View>
      )}

      {/* Performance Tips */}
      <View style={styles.tipsContainer}>
        <Text style={styles.tipsTitle}>Performance Optimization Tips</Text>
        
        <View style={styles.tipItem}>
          <AppIcon name="lightbulb-outline" size="sm" color="warning" />
          <Text style={styles.tipText}>Use vector icons for crisp rendering at any size</Text>
        </View>
        
        <View style={styles.tipItem}>
          <AppIcon name="lightbulb-outline" size="sm" color="warning" />
          <Text style={styles.tipText}>Consistent sizing system reduces layout calculations</Text>
        </View>
        
        <View style={styles.tipItem}>
          <AppIcon name="lightbulb-outline" size="sm" color="warning" />
          <Text style={styles.tipText}>Theme-integrated colors improve performance</Text>
        </View>
        
        <View style={styles.tipItem}>
          <AppIcon name="lightbulb-outline" size="sm" color="warning" />
          <Text style={styles.tipText}>MaterialCommunityIcons provides excellent performance</Text>
        </View>
      </View>

      {/* System Info */}
      <View style={styles.systemInfoContainer}>
        <Text style={styles.systemInfoTitle}>System Information</Text>
        <View style={styles.systemInfoGrid}>
          <View style={styles.systemInfoItem}>
            <Text style={styles.systemInfoLabel}>Icon Library</Text>
            <Text style={styles.systemInfoValue}>MaterialCommunityIcons</Text>
          </View>
          <View style={styles.systemInfoItem}>
            <Text style={styles.systemInfoLabel}>Total Categories</Text>
            <Text style={styles.systemInfoValue}>{Object.keys(RestaurantIcons).length}</Text>
          </View>
          <View style={styles.systemInfoItem}>
            <Text style={styles.systemInfoLabel}>Total Icons</Text>
            <Text style={styles.systemInfoValue}>
              {Object.values(RestaurantIcons).reduce((acc, category) => acc + Object.keys(category).length, 0)}
            </Text>
          </View>
          <View style={styles.systemInfoItem}>
            <Text style={styles.systemInfoLabel}>Size Options</Text>
            <Text style={styles.systemInfoValue}>6 (xs to xxl)</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PremiumColors.white,
  },
  header: {
    padding: 24,
    backgroundColor: PremiumColors.primary,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: PremiumColors.white,
    marginTop: 8,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: PremiumColors.white,
    opacity: 0.9,
  },
  controlsContainer: {
    padding: 16,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: PremiumColors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    gap: 8,
  },
  testButtonDisabled: {
    backgroundColor: PremiumColors.grayMedium,
  },
  testButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: PremiumColors.white,
  },
  currentTestContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    padding: 12,
    backgroundColor: PremiumColors.grayLight,
    borderRadius: 8,
    gap: 8,
  },
  currentTestText: {
    fontSize: 14,
    color: PremiumColors.black,
    fontWeight: '500',
  },
  resultsContainer: {
    margin: 16,
  },
  resultsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 16,
  },
  resultCard: {
    backgroundColor: PremiumColors.grayLight,
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  resultTestName: {
    fontSize: 16,
    fontWeight: '600',
    color: PremiumColors.black,
    flex: 1,
  },
  performanceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  performanceBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: PremiumColors.white,
  },
  resultMetrics: {
    gap: 8,
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  metricLabel: {
    fontSize: 14,
    color: PremiumColors.grayDark,
    flex: 1,
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '600',
    color: PremiumColors.black,
  },
  tipsContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: PremiumColors.grayLight,
    borderRadius: 8,
  },
  tipsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: PremiumColors.black,
    marginBottom: 12,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: PremiumColors.black,
    flex: 1,
    lineHeight: 20,
  },
  systemInfoContainer: {
    margin: 16,
    marginBottom: 32,
  },
  systemInfoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: PremiumColors.black,
    marginBottom: 12,
  },
  systemInfoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  systemInfoItem: {
    backgroundColor: PremiumColors.grayLight,
    padding: 12,
    borderRadius: 8,
    minWidth: '45%',
  },
  systemInfoLabel: {
    fontSize: 12,
    color: PremiumColors.grayDark,
    marginBottom: 4,
  },
  systemInfoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: PremiumColors.black,
  },
});

export default IconPerformanceTest;
