import api, { ApiResponse, handleApiError } from './api';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Registration form data interface
export interface RegistrationFormData {
  // Restaurant Information
  restaurantName: string;
  restaurantType: string[];
  description?: string;
  
  // Owner Information
  ownerName: string;
  email: string;
  phone: string;
  
  // Location
  address: string;
  city?: string;
  state?: string;
  zipCode?: string;
  
  // Authentication
  username: string;
  password: string;
  
  // Documents (file paths or base64 strings)
  documents: {
    businessLicense?: string;
    foodLicense?: string;
    taxCertificate?: string;
  };
}

export interface RegistrationResponse {
  user: {
    id: string;
    username: string;
    role: 'owner';
    email: string;
    name: string;
    verificationStatus: 'pending' | 'verified' | 'rejected';
    createdAt: string;
  };
  restaurant: {
    id: string;
    name: string;
    verificationStatus: 'pending' | 'verified' | 'rejected';
    documents: {
      businessLicense?: { url: string; status: string; };
      foodLicense?: { url: string; status: string; };
      taxCertificate?: { url: string; status: string; };
    };
  };
  token: string;
  message: string;
}

class RegistrationService {
  // Register new restaurant
  async registerRestaurant(formData: RegistrationFormData): Promise<ApiResponse<RegistrationResponse>> {
    try {
      // Prepare form data for multipart upload if documents are files
      const registrationData = new FormData();
      
      // Add text fields
      registrationData.append('restaurantName', formData.restaurantName);
      registrationData.append('restaurantType', JSON.stringify(formData.restaurantType));
      registrationData.append('ownerName', formData.ownerName);
      registrationData.append('email', formData.email);
      registrationData.append('phone', formData.phone);
      registrationData.append('address', formData.address);
      registrationData.append('username', formData.username);
      registrationData.append('password', formData.password);
      
      if (formData.description) {
        registrationData.append('description', formData.description);
      }
      
      if (formData.city) {
        registrationData.append('city', formData.city);
      }
      
      if (formData.state) {
        registrationData.append('state', formData.state);
      }
      
      if (formData.zipCode) {
        registrationData.append('zipCode', formData.zipCode);
      }
      
      // Add document files
      if (formData.documents.businessLicense) {
        registrationData.append('businessLicense', {
          uri: formData.documents.businessLicense,
          type: 'image/jpeg',
          name: 'business_license.jpg',
        } as any);
      }
      
      if (formData.documents.foodLicense) {
        registrationData.append('foodLicense', {
          uri: formData.documents.foodLicense,
          type: 'image/jpeg',
          name: 'food_license.jpg',
        } as any);
      }
      
      if (formData.documents.taxCertificate) {
        registrationData.append('taxCertificate', {
          uri: formData.documents.taxCertificate,
          type: 'image/jpeg',
          name: 'tax_certificate.jpg',
        } as any);
      }
      
      const response = await api.post<ApiResponse<RegistrationResponse>>(
        '/auth/register',
        registrationData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      
      if (response.data.success && response.data.data.token) {
        // Store auth data
        await AsyncStorage.setItem('authToken', response.data.data.token);
        await AsyncStorage.setItem('userData', JSON.stringify(response.data.data.user));
        await AsyncStorage.setItem('restaurantData', JSON.stringify(response.data.data.restaurant));
      }
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Check if username is available
  async checkUsernameAvailability(username: string): Promise<ApiResponse<{ available: boolean }>> {
    try {
      const response = await api.get<ApiResponse<{ available: boolean }>>(
        `/auth/check-username/${encodeURIComponent(username)}`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Check if email is available
  async checkEmailAvailability(email: string): Promise<ApiResponse<{ available: boolean }>> {
    try {
      const response = await api.get<ApiResponse<{ available: boolean }>>(
        `/auth/check-email/${encodeURIComponent(email)}`
      );
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Upload document
  async uploadDocument(
    documentType: 'businessLicense' | 'foodLicense' | 'taxCertificate',
    fileUri: string
  ): Promise<ApiResponse<{ url: string }>> {
    try {
      const formData = new FormData();
      formData.append('document', {
        uri: fileUri,
        type: 'image/jpeg',
        name: `${documentType}.jpg`,
      } as any);
      formData.append('documentType', documentType);

      const response = await api.post<ApiResponse<{ url: string }>>(
        '/documents/upload',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get registration status
  async getRegistrationStatus(): Promise<ApiResponse<{
    status: 'pending' | 'verified' | 'rejected';
    documents: any;
    rejectionReason?: string;
  }>> {
    try {
      const response = await api.get<ApiResponse<{
        status: 'pending' | 'verified' | 'rejected';
        documents: any;
        rejectionReason?: string;
      }>>('/auth/registration-status');
      
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Resubmit documents after rejection
  async resubmitDocuments(documents: {
    businessLicense?: string;
    foodLicense?: string;
    taxCertificate?: string;
  }): Promise<ApiResponse<{ message: string }>> {
    try {
      const formData = new FormData();
      
      if (documents.businessLicense) {
        formData.append('businessLicense', {
          uri: documents.businessLicense,
          type: 'image/jpeg',
          name: 'business_license.jpg',
        } as any);
      }
      
      if (documents.foodLicense) {
        formData.append('foodLicense', {
          uri: documents.foodLicense,
          type: 'image/jpeg',
          name: 'food_license.jpg',
        } as any);
      }
      
      if (documents.taxCertificate) {
        formData.append('taxCertificate', {
          uri: documents.taxCertificate,
          type: 'image/jpeg',
          name: 'tax_certificate.jpg',
        } as any);
      }

      const response = await api.post<ApiResponse<{ message: string }>>(
        '/auth/resubmit-documents',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export default new RegistrationService();
