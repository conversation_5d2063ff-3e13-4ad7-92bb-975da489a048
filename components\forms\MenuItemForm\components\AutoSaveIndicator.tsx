import { Colors } from '@/constants/Theme';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
  StyleSheet,
  Text,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSequence,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

interface AutoSaveIndicatorProps {
  lastSaved: Date | null;
  isDirty: boolean;
  isSubmitting: boolean;
}

export function AutoSaveIndicator({
  lastSaved,
  isDirty,
  isSubmitting,
}: AutoSaveIndicatorProps) {
  const fadeAnimation = useSharedValue(0);
  const scaleAnimation = useSharedValue(0.8);
  const pulseAnimation = useSharedValue(1);

  React.useEffect(() => {
    if (lastSaved || isDirty || isSubmitting) {
      fadeAnimation.value = withTiming(1, { duration: 300 });
      scaleAnimation.value = withSpring(1, { damping: 15, stiffness: 200 });
    } else {
      fadeAnimation.value = withDelay(2000, withTiming(0, { duration: 300 }));
      scaleAnimation.value = withTiming(0.8, { duration: 300 });
    }
  }, [lastSaved, isDirty, isSubmitting]);

  React.useEffect(() => {
    if (lastSaved && !isDirty) {
      // Success pulse animation
      pulseAnimation.value = withSequence(
        withTiming(1.2, { duration: 200 }),
        withTiming(1, { duration: 200 })
      );
    }
  }, [lastSaved, isDirty]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
    transform: [
      { scale: scaleAnimation.value * pulseAnimation.value },
    ],
  }));

  const getStatusInfo = () => {
    if (isSubmitting) {
      return {
        icon: 'arrow-up-circle',
        text: 'Submitting...',
        color: Colors.light.primary,
        backgroundColor: Colors.light.primaryLight,
      };
    }
    
    if (isDirty) {
      return {
        icon: 'time',
        text: 'Unsaved changes',
        color: Colors.light.warning,
        backgroundColor: Colors.light.warningLight,
      };
    }
    
    if (lastSaved) {
      const now = new Date();
      const diffInMinutes = Math.floor((now.getTime() - lastSaved.getTime()) / (1000 * 60));
      
      let timeText = '';
      if (diffInMinutes < 1) {
        timeText = 'Just now';
      } else if (diffInMinutes < 60) {
        timeText = `${diffInMinutes}m ago`;
      } else {
        const diffInHours = Math.floor(diffInMinutes / 60);
        timeText = `${diffInHours}h ago`;
      }
      
      return {
        icon: 'checkmark-circle',
        text: `Saved ${timeText}`,
        color: Colors.light.success,
        backgroundColor: Colors.light.successLight,
      };
    }
    
    return null;
  };

  const statusInfo = getStatusInfo();
  
  if (!statusInfo) {
    return null;
  }

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <View
        style={[
          styles.indicator,
          { backgroundColor: statusInfo.backgroundColor },
        ]}
      >
        <Ionicons
          name={statusInfo.icon as any}
          size={14}
          color={statusInfo.color}
        />
        <Text
          style={[
            styles.text,
            { color: statusInfo.color },
          ]}
        >
          {statusInfo.text}
        </Text>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: 8,
  },
  indicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  text: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
  },
});

export default AutoSaveIndicator;
