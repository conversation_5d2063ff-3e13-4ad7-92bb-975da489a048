import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IconSymbol } from '@/components/ui/IconSymbol';
import authService from '@/services/authService';
import { useAuth } from '@/contexts/AuthContext';

interface VerificationPendingProps {
  reason: string;
  onRetry?: () => void;
}

export const VerificationPending: React.FC<VerificationPendingProps> = ({ reason, onRetry }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [verificationData, setVerificationData] = useState<any>(null);
  const { logout } = useAuth();

  const checkVerificationStatus = async () => {
    try {
      setRefreshing(true);
      const response = await authService.checkVerificationStatus();
      
      if (response.success) {
        setVerificationData(response.data);
        
        // If verified, trigger retry
        if (response.data.verificationStatus === 'verified') {
          onRetry?.();
        }
      }
    } catch (error) {
      console.error('Error checking verification:', error);
      Alert.alert('Error', 'Failed to check verification status');
    } finally {
      setRefreshing(false);
    }
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error) {
              console.error('Logout error:', error);
            }
          }
        }
      ]
    );
  };

  const getStatusIcon = () => {
    if (verificationData?.verificationStatus === 'rejected') {
      return { name: 'close-circle', color: '#EF4444' };
    }
    return { name: 'clock', color: '#F59E0B' };
  };

  const getStatusText = () => {
    if (verificationData?.verificationStatus === 'rejected') {
      return 'Verification Rejected';
    }
    return 'Verification Pending';
  };

  const getStatusDescription = () => {
    if (verificationData?.verificationStatus === 'rejected') {
      return 'Your documents were rejected. Please contact support or resubmit your documents.';
    }
    return 'Your documents are being reviewed. This usually takes 1-3 business days.';
  };

  useEffect(() => {
    checkVerificationStatus();
  }, []);

  return (
    <LinearGradient
      colors={['#DC143C', '#B91C1C']}
      style={styles.container}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={checkVerificationStatus}
            tintColor="#FFFFFF"
          />
        }
      >
        <View style={styles.content}>
          {/* Status Icon */}
          <View style={styles.iconContainer}>
            <IconSymbol 
              name={getStatusIcon().name} 
              size={80} 
              color={getStatusIcon().color} 
            />
          </View>

          {/* Status Title */}
          <Text style={styles.title}>{getStatusText()}</Text>

          {/* Status Description */}
          <Text style={styles.description}>
            {getStatusDescription()}
          </Text>

          {/* Reason */}
          <View style={styles.reasonContainer}>
            <Text style={styles.reasonText}>{reason}</Text>
          </View>

          {/* Document Status */}
          {verificationData?.restaurant?.documents && (
            <View style={styles.documentsContainer}>
              <Text style={styles.documentsTitle}>Document Status</Text>
              
              {Object.entries(verificationData.restaurant.documents).map(([key, doc]: [string, any]) => (
                <View key={key} style={styles.documentItem}>
                  <View style={styles.documentInfo}>
                    <Text style={styles.documentName}>
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Text>
                    <View style={[
                      styles.statusBadge,
                      { backgroundColor: doc.status === 'approved' ? '#22C55E' : 
                                        doc.status === 'rejected' ? '#EF4444' : '#F59E0B' }
                    ]}>
                      <Text style={styles.statusBadgeText}>
                        {doc.status?.toUpperCase() || 'PENDING'}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          )}

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={checkVerificationStatus}
              disabled={refreshing}
            >
              <IconSymbol name="refresh" size={20} color="#FFFFFF" />
              <Text style={styles.refreshButtonText}>
                {refreshing ? 'Checking...' : 'Check Status'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.logoutButton}
              onPress={handleLogout}
            >
              <IconSymbol name="logout" size={20} color="#DC143C" />
              <Text style={styles.logoutButtonText}>Logout</Text>
            </TouchableOpacity>
          </View>

          {/* Help Text */}
          <View style={styles.helpContainer}>
            <Text style={styles.helpText}>
              Need help? Contact our support <NAME_EMAIL>
            </Text>
          </View>
        </View>
      </ScrollView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  content: {
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 24,
    padding: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 50,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#F8F9FA',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  reasonContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    width: '100%',
  },
  reasonText: {
    fontSize: 14,
    color: '#F8F9FA',
    textAlign: 'center',
    lineHeight: 20,
  },
  documentsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    width: '100%',
  },
  documentsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  documentItem: {
    marginBottom: 8,
  },
  documentInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  documentName: {
    fontSize: 14,
    color: '#F8F9FA',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  actionButtons: {
    width: '100%',
    gap: 12,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  refreshButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#DC143C',
  },
  helpContainer: {
    marginTop: 24,
    padding: 16,
  },
  helpText: {
    fontSize: 12,
    color: '#F8F9FA',
    textAlign: 'center',
    opacity: 0.8,
  },
});
