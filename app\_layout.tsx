import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import 'react-native-reanimated';

import AuthWrapper from '@/components/AuthWrapper';
import { AuthProvider } from '@/contexts/AuthContext';
import { SimpleThemeProvider } from '@/contexts/SimpleThemeContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import store from '@/store';
import { performanceMonitor } from '@/utils/performance';
import { Provider } from 'react-redux';

// Custom Red & White Themes
const RedLightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: Colors.light.primary,
    background: Colors.light.primary, // Red background instead of white
    card: Colors.light.primary,
    text: Colors.light.textInverse,
    border: Colors.light.border,
    notification: Colors.light.primary,
  },
};

const RedDarkTheme = {
  ...DarkTheme,
  colors: {
    ...DarkTheme.colors,
    primary: Colors.dark.primary,
    background: Colors.dark.primary, // Red background instead of black
    card: Colors.dark.primary,
    text: Colors.dark.textInverse,
    border: Colors.dark.border,
    notification: Colors.dark.primary,
  },
};

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  // Performance monitoring setup
  React.useEffect(() => {
    if (__DEV__) {
      performanceMonitor.startTiming('app_startup');

      // Monitor memory usage periodically
      const memoryInterval = setInterval(() => {
        const memoryUsage = performanceMonitor.getMemoryUsage();
        if (memoryUsage > 100) { // Alert if memory usage > 100MB
          console.warn(`High memory usage detected: ${memoryUsage.toFixed(2)}MB`);
        }
      }, 30000); // Check every 30 seconds

      return () => {
        clearInterval(memoryInterval);
        performanceMonitor.endTiming('app_startup', 'screenLoadTime');
        performanceMonitor.logMetrics('app_startup');
      };
    }
  }, []);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <Provider store={store}>
      <SimpleThemeProvider>
        <AuthProvider>
          <ThemeProvider value={colorScheme === 'dark' ? RedDarkTheme : RedLightTheme}>
            <AuthWrapper>
              <Stack>
                <Stack.Screen name="login" options={{ headerShown: false }} />
                <Stack.Screen name="staff-management" options={{ headerShown: false }} />
                <Stack.Screen name="(auth)" options={{ headerShown: false }} />
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="onboarding" options={{ headerShown: false }} />
              </Stack>
            </AuthWrapper>
            <StatusBar style="auto" translucent={false} />
          </ThemeProvider>
        </AuthProvider>
      </SimpleThemeProvider>
    </Provider>
  );
}
