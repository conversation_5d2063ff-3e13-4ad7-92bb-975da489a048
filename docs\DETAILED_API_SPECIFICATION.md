# Detailed API Specification

## 📋 Table of Contents
1. [Database Schema](#database-schema)
2. [Authentication Endpoints](#authentication-endpoints)
3. [Restaurant Management](#restaurant-management)
4. [Menu Management](#menu-management)
5. [Orders Management](#orders-management)
6. [Staff Management](#staff-management)
7. [Analytics](#analytics)
8. [Notifications](#notifications)
9. [File Upload](#file-upload)
10. [Error Handling](#error-handling)

## 🗄️ Database Schema

### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('owner', 'admin', 'staff') NOT NULL,
    verification_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    is_demo BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    restaurant_id UUID REFERENCES restaurants(id),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);
```

### Restaurants Table
```sql
CREATE TABLE restaurants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT NOT NULL,
    city VARCHAR(100),
    state VARCHAR(100),
    zip_code VARCHAR(20),
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    type JSON NOT NULL, -- Array of restaurant types
    logo_url VARCHAR(500),
    cover_image_url VARCHAR(500),
    gallery_images JSON, -- Array of image URLs
    verification_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    is_open BOOLEAN DEFAULT false,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    owner_id UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Restaurant Settings Table
```sql
CREATE TABLE restaurant_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id UUID NOT NULL REFERENCES restaurants(id),
    is_open BOOLEAN DEFAULT false,
    opening_hours JSON NOT NULL, -- {"monday": {"open": "09:00", "close": "22:00", "closed": false}}
    delivery_radius DECIMAL(5,2) DEFAULT 5.00,
    minimum_order_amount DECIMAL(10,2) DEFAULT 0.00,
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    estimated_delivery_time INTEGER DEFAULT 30, -- minutes
    accepting_orders BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Documents Table
```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id UUID NOT NULL REFERENCES restaurants(id),
    type ENUM('business_license', 'food_license', 'tax_certificate') NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    rejection_reason TEXT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP,
    reviewed_by UUID REFERENCES users(id)
);
```

### Menu Categories Table
```sql
CREATE TABLE menu_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id UUID NOT NULL REFERENCES restaurants(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Menu Items Table
```sql
CREATE TABLE menu_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id UUID NOT NULL REFERENCES restaurants(id),
    category_id UUID NOT NULL REFERENCES menu_categories(id),
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    images JSON, -- Array of image URLs
    is_available BOOLEAN DEFAULT true,
    is_vegetarian BOOLEAN DEFAULT false,
    is_vegan BOOLEAN DEFAULT false,
    is_gluten_free BOOLEAN DEFAULT false,
    spice_level ENUM('mild', 'medium', 'hot', 'extra-hot') DEFAULT 'mild',
    preparation_time INTEGER DEFAULT 15, -- minutes
    calories INTEGER,
    ingredients JSON, -- Array of ingredients
    allergens JSON, -- Array of allergens
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Menu Item Customizations Table
```sql
CREATE TABLE menu_item_customizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    menu_item_id UUID NOT NULL REFERENCES menu_items(id),
    name VARCHAR(255) NOT NULL,
    type ENUM('single', 'multiple') NOT NULL,
    required BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Customization Options Table
```sql
CREATE TABLE customization_options (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customization_id UUID NOT NULL REFERENCES menu_item_customizations(id),
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) DEFAULT 0.00,
    is_default BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Orders Table
```sql
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    restaurant_id UUID NOT NULL REFERENCES restaurants(id),
    customer_id UUID,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    customer_email VARCHAR(255),
    subtotal DECIMAL(10,2) NOT NULL,
    tax DECIMAL(10,2) DEFAULT 0.00,
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    discount DECIMAL(10,2) DEFAULT 0.00,
    total DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'confirmed', 'preparing', 'ready', 'out-for-delivery', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method ENUM('cash', 'card', 'online') NOT NULL,
    order_type ENUM('delivery', 'pickup', 'dine-in') NOT NULL,
    delivery_address JSON, -- {"street": "", "city": "", "state": "", "zipCode": "", "coordinates": {"lat": 0, "lng": 0}}
    estimated_delivery_time TIMESTAMP,
    actual_delivery_time TIMESTAMP,
    special_instructions TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Order Items Table
```sql
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id),
    menu_item_id UUID NOT NULL REFERENCES menu_items(id),
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL,
    customizations JSON, -- Array of selected customizations
    special_instructions TEXT,
    total DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Notifications Table
```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    restaurant_id UUID NOT NULL REFERENCES restaurants(id),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    data JSON, -- Additional data for the notification
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Push Tokens Table
```sql
CREATE TABLE push_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    token VARCHAR(500) NOT NULL,
    platform ENUM('ios', 'android') NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔐 Authentication Endpoints

### 1. Register Restaurant Owner

**Endpoint:** `POST /api/auth/register`

**Request Headers:**
```
Content-Type: multipart/form-data
```

**Request Body (FormData):**
```javascript
{
  // Restaurant Information
  restaurantName: "Pizza Palace",
  restaurantType: ["italian", "pizza", "fast-food"], // JSON string
  description: "Best pizza in town",
  
  // Owner Information
  ownerName: "John Doe",
  email: "<EMAIL>",
  phone: "******-123-4567",
  
  // Location
  address: "123 Main St, City, State 12345",
  city: "New York",
  state: "NY",
  zipCode: "12345",
  
  // Authentication
  username: "johndoe",
  password: "SecurePass123!",
  
  // Documents (Files)
  businessLicense: File, // Image file
  foodLicense: File,     // Image file
  taxCertificate: File   // Image file
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Registration successful. Your documents are being reviewed.",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "username": "johndoe",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "owner",
      "verificationStatus": "pending",
      "isDemo": false,
      "createdAt": "2024-01-15T10:30:00Z"
    },
    "restaurant": {
      "id": "660e8400-e29b-41d4-a716-446655440001",
      "name": "Pizza Palace",
      "verificationStatus": "pending",
      "documents": {
        "businessLicense": {
          "url": "https://storage.example.com/docs/business_license_123.jpg",
          "status": "pending"
        },
        "foodLicense": {
          "url": "https://storage.example.com/docs/food_license_123.jpg",
          "status": "pending"
        },
        "taxCertificate": {
          "url": "https://storage.example.com/docs/tax_cert_123.jpg",
          "status": "pending"
        }
      }
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 2. Login User

**Endpoint:** `POST /api/auth/login`

**Request Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "username": "johndoe",
  "password": "SecurePass123!",
  "role": "owner"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "username": "johndoe",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "owner",
      "verificationStatus": "verified",
      "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
      "isDemo": false,
      "lastLogin": "2024-01-15T10:30:00Z"
    },
    "restaurant": {
      "id": "660e8400-e29b-41d4-a716-446655440001",
      "name": "Pizza Palace",
      "verificationStatus": "verified",
      "isOpen": true,
      "status": "active"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 3. Check Verification Status

**Endpoint:** `GET /api/auth/verification-status`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "verificationStatus": "verified",
    "restaurant": {
      "id": "660e8400-e29b-41d4-a716-446655440001",
      "name": "Pizza Palace",
      "verificationStatus": "verified",
      "documents": {
        "businessLicense": {
          "url": "https://storage.example.com/docs/business_license_123.jpg",
          "status": "approved",
          "reviewedAt": "2024-01-14T15:30:00Z"
        },
        "foodLicense": {
          "url": "https://storage.example.com/docs/food_license_123.jpg",
          "status": "approved",
          "reviewedAt": "2024-01-14T15:30:00Z"
        },
        "taxCertificate": {
          "url": "https://storage.example.com/docs/tax_cert_123.jpg",
          "status": "approved",
          "reviewedAt": "2024-01-14T15:30:00Z"
        }
      }
    }
  }
}
```

### 4. Check Username Availability

**Endpoint:** `GET /api/auth/check-username/:username`

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "available": true
  }
}
```

### 5. Check Email Availability

**Endpoint:** `GET /api/auth/check-email/:email`

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "available": false
  }
}
```

### 6. Logout

**Endpoint:** `POST /api/auth/logout`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

## 🏪 Restaurant Management

### 1. Get Restaurant Profile

**Endpoint:** `GET /api/restaurant/profile`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "660e8400-e29b-41d4-a716-446655440001",
    "name": "Pizza Palace",
    "description": "Best pizza in town with authentic Italian recipes",
    "address": "123 Main St, New York, NY 12345",
    "phone": "******-123-4567",
    "email": "<EMAIL>",
    "type": ["italian", "pizza", "fast-food"],
    "logoUrl": "https://storage.example.com/logos/pizza_palace_logo.jpg",
    "coverImageUrl": "https://storage.example.com/covers/pizza_palace_cover.jpg",
    "galleryImages": [
      "https://storage.example.com/gallery/pizza1.jpg",
      "https://storage.example.com/gallery/pizza2.jpg"
    ],
    "verificationStatus": "verified",
    "isOpen": true,
    "status": "active",
    "ownerId": "550e8400-e29b-41d4-a716-************",
    "createdAt": "2024-01-10T08:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### 2. Update Restaurant Profile

**Endpoint:** `PUT /api/restaurant/profile`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Pizza Palace Deluxe",
  "description": "Premium pizza experience with authentic Italian recipes",
  "phone": "******-123-4568",
  "type": ["italian", "pizza", "fine-dining"]
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Restaurant profile updated successfully",
  "data": {
    "id": "660e8400-e29b-41d4-a716-446655440001",
    "name": "Pizza Palace Deluxe",
    "description": "Premium pizza experience with authentic Italian recipes",
    "phone": "******-123-4568",
    "type": ["italian", "pizza", "fine-dining"],
    "updatedAt": "2024-01-15T11:00:00Z"
  }
}
```

### 3. Get Restaurant Statistics

**Endpoint:** `GET /api/restaurant/stats?period=today`

**Query Parameters:**
- `period`: `today` | `week` | `month` | `year`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "totalOrders": 45,
    "totalRevenue": 1250.75,
    "averageOrderValue": 27.79,
    "totalCustomers": 38,
    "activeMenuItems": 24,
    "pendingOrders": 3
  }
}
```

### 4. Get Restaurant Settings

**Endpoint:** `GET /api/restaurant/settings`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "isOpen": true,
    "openingHours": {
      "monday": {"open": "09:00", "close": "22:00", "closed": false},
      "tuesday": {"open": "09:00", "close": "22:00", "closed": false},
      "wednesday": {"open": "09:00", "close": "22:00", "closed": false},
      "thursday": {"open": "09:00", "close": "22:00", "closed": false},
      "friday": {"open": "09:00", "close": "23:00", "closed": false},
      "saturday": {"open": "10:00", "close": "23:00", "closed": false},
      "sunday": {"open": "10:00", "close": "21:00", "closed": false}
    },
    "deliveryRadius": 5.0,
    "minimumOrderAmount": 15.00,
    "deliveryFee": 3.99,
    "estimatedDeliveryTime": 30,
    "acceptingOrders": true
  }
}
```

### 5. Update Restaurant Settings

**Endpoint:** `PUT /api/restaurant/settings`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "deliveryRadius": 7.5,
  "minimumOrderAmount": 20.00,
  "deliveryFee": 4.99,
  "estimatedDeliveryTime": 25,
  "openingHours": {
    "friday": {"open": "09:00", "close": "24:00", "closed": false}
  }
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Restaurant settings updated successfully",
  "data": {
    "deliveryRadius": 7.5,
    "minimumOrderAmount": 20.00,
    "deliveryFee": 4.99,
    "estimatedDeliveryTime": 25,
    "updatedAt": "2024-01-15T11:15:00Z"
  }
}
```

### 6. Toggle Restaurant Status

**Endpoint:** `PATCH /api/restaurant/toggle-status`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "isOpen": false
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Restaurant status updated successfully",
  "data": {
    "isOpen": false
  }
}
```

## 🍽️ Menu Management

### 1. Get Menu Categories

**Endpoint:** `GET /api/menu/categories`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "770e8400-e29b-41d4-a716-446655440002",
      "name": "Appetizers",
      "description": "Start your meal with our delicious appetizers",
      "displayOrder": 1,
      "isActive": true,
      "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
      "createdAt": "2024-01-10T08:30:00Z",
      "updatedAt": "2024-01-10T08:30:00Z"
    },
    {
      "id": "770e8400-e29b-41d4-a716-446655440003",
      "name": "Main Courses",
      "description": "Our signature pizzas and pasta dishes",
      "displayOrder": 2,
      "isActive": true,
      "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
      "createdAt": "2024-01-10T08:30:00Z",
      "updatedAt": "2024-01-10T08:30:00Z"
    }
  ]
}
```

### 2. Create Menu Category

**Endpoint:** `POST /api/menu/categories`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Desserts",
  "description": "Sweet endings to your perfect meal",
  "displayOrder": 3,
  "isActive": true
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Category created successfully",
  "data": {
    "id": "770e8400-e29b-41d4-a716-446655440004",
    "name": "Desserts",
    "description": "Sweet endings to your perfect meal",
    "displayOrder": 3,
    "isActive": true,
    "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
    "createdAt": "2024-01-15T12:00:00Z",
    "updatedAt": "2024-01-15T12:00:00Z"
  }
}
```

### 3. Get Menu Items

**Endpoint:** `GET /api/menu/items?categoryId=770e8400-e29b-41d4-a716-446655440003`

**Query Parameters:**
- `categoryId` (optional): Filter by category ID

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "880e8400-e29b-41d4-a716-446655440005",
      "name": "Margherita Pizza",
      "description": "Classic pizza with fresh tomatoes, mozzarella, and basil",
      "price": 18.99,
      "categoryId": "770e8400-e29b-41d4-a716-446655440003",
      "images": [
        "https://storage.example.com/menu/margherita_1.jpg",
        "https://storage.example.com/menu/margherita_2.jpg"
      ],
      "isAvailable": true,
      "isVegetarian": true,
      "isVegan": false,
      "isGlutenFree": false,
      "spiceLevel": "mild",
      "preparationTime": 15,
      "calories": 280,
      "ingredients": ["tomato sauce", "mozzarella cheese", "fresh basil", "olive oil"],
      "allergens": ["gluten", "dairy"],
      "customizations": [
        {
          "id": "990e8400-e29b-41d4-a716-446655440006",
          "name": "Size",
          "type": "single",
          "required": true,
          "options": [
            {
              "id": "aa0e8400-e29b-41d4-a716-446655440007",
              "name": "Small (10\")",
              "price": 0.00,
              "isDefault": true
            },
            {
              "id": "aa0e8400-e29b-41d4-a716-446655440008",
              "name": "Medium (12\")",
              "price": 4.00,
              "isDefault": false
            },
            {
              "id": "aa0e8400-e29b-41d4-a716-446655440009",
              "name": "Large (14\")",
              "price": 8.00,
              "isDefault": false
            }
          ]
        },
        {
          "id": "990e8400-e29b-41d4-a716-44665544000a",
          "name": "Extra Toppings",
          "type": "multiple",
          "required": false,
          "options": [
            {
              "id": "aa0e8400-e29b-41d4-a716-44665544000b",
              "name": "Extra Cheese",
              "price": 2.50,
              "isDefault": false
            },
            {
              "id": "aa0e8400-e29b-41d4-a716-44665544000c",
              "name": "Mushrooms",
              "price": 1.50,
              "isDefault": false
            }
          ]
        }
      ],
      "displayOrder": 1,
      "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
      "createdAt": "2024-01-10T09:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z"
    }
  ]
}
```

### 4. Create Menu Item

**Endpoint:** `POST /api/menu/items`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Pepperoni Pizza",
  "description": "Classic pepperoni pizza with mozzarella cheese",
  "price": 21.99,
  "categoryId": "770e8400-e29b-41d4-a716-446655440003",
  "images": [
    "https://storage.example.com/menu/pepperoni_1.jpg"
  ],
  "isVegetarian": false,
  "isVegan": false,
  "isGlutenFree": false,
  "spiceLevel": "mild",
  "preparationTime": 18,
  "calories": 320,
  "ingredients": ["tomato sauce", "mozzarella cheese", "pepperoni", "oregano"],
  "allergens": ["gluten", "dairy", "pork"],
  "customizations": [
    {
      "name": "Size",
      "type": "single",
      "required": true,
      "options": [
        {
          "name": "Small (10\")",
          "price": 0.00,
          "isDefault": true
        },
        {
          "name": "Medium (12\")",
          "price": 4.00,
          "isDefault": false
        },
        {
          "name": "Large (14\")",
          "price": 8.00,
          "isDefault": false
        }
      ]
    }
  ]
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Menu item created successfully",
  "data": {
    "id": "880e8400-e29b-41d4-a716-44665544000d",
    "name": "Pepperoni Pizza",
    "description": "Classic pepperoni pizza with mozzarella cheese",
    "price": 21.99,
    "categoryId": "770e8400-e29b-41d4-a716-446655440003",
    "images": [
      "https://storage.example.com/menu/pepperoni_1.jpg"
    ],
    "isAvailable": true,
    "isVegetarian": false,
    "spiceLevel": "mild",
    "preparationTime": 18,
    "customizations": [
      {
        "id": "990e8400-e29b-41d4-a716-44665544000e",
        "name": "Size",
        "type": "single",
        "required": true,
        "options": [
          {
            "id": "aa0e8400-e29b-41d4-a716-44665544000f",
            "name": "Small (10\")",
            "price": 0.00,
            "isDefault": true
          }
        ]
      }
    ],
    "createdAt": "2024-01-15T12:30:00Z",
    "updatedAt": "2024-01-15T12:30:00Z"
  }
}
```

### 5. Upload Menu Item Image

**Endpoint:** `POST /api/menu/items/:itemId/upload-image`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: multipart/form-data
```

**Request Body (FormData):**
```javascript
{
  image: File // Image file
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Image uploaded successfully",
  "data": {
    "url": "https://storage.example.com/menu/pepperoni_new_image.jpg"
  }
}
```

### 6. Toggle Item Availability

**Endpoint:** `PATCH /api/menu/items/:itemId/availability`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "isAvailable": false
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Item availability updated successfully",
  "data": {
    "id": "880e8400-e29b-41d4-a716-446655440005",
    "name": "Margherita Pizza",
    "isAvailable": false,
    "updatedAt": "2024-01-15T13:00:00Z"
  }
}

## 📦 Orders Management

### 1. Get Orders (with Pagination)

**Endpoint:** `GET /api/orders?page=1&limit=20&status=pending,confirmed&orderType=delivery`

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `status`: Comma-separated order statuses
- `orderType`: Comma-separated order types
- `paymentStatus`: Comma-separated payment statuses
- `dateFrom`: Start date (YYYY-MM-DD)
- `dateTo`: End date (YYYY-MM-DD)
- `customerId`: Filter by customer ID

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "bb0e8400-e29b-41d4-a716-446655440010",
      "orderNumber": "ORD-2024-001",
      "customerId": "cc0e8400-e29b-41d4-a716-446655440011",
      "customerName": "Jane Smith",
      "customerPhone": "******-987-6543",
      "customerEmail": "<EMAIL>",
      "items": [
        {
          "id": "dd0e8400-e29b-41d4-a716-446655440012",
          "menuItemId": "880e8400-e29b-41d4-a716-446655440005",
          "name": "Margherita Pizza",
          "price": 18.99,
          "quantity": 2,
          "customizations": [
            {
              "name": "Size",
              "options": ["Medium (12\")"],
              "additionalPrice": 4.00
            },
            {
              "name": "Extra Toppings",
              "options": ["Extra Cheese", "Mushrooms"],
              "additionalPrice": 4.00
            }
          ],
          "specialInstructions": "Extra crispy crust",
          "total": 49.98
        }
      ],
      "subtotal": 49.98,
      "tax": 4.50,
      "deliveryFee": 3.99,
      "discount": 0.00,
      "total": 58.47,
      "status": "confirmed",
      "paymentStatus": "paid",
      "paymentMethod": "card",
      "orderType": "delivery",
      "deliveryAddress": {
        "street": "456 Oak Avenue",
        "city": "New York",
        "state": "NY",
        "zipCode": "12346",
        "coordinates": {
          "latitude": 40.7128,
          "longitude": -74.0060
        }
      },
      "estimatedDeliveryTime": "2024-01-15T14:30:00Z",
      "actualDeliveryTime": null,
      "specialInstructions": "Ring doorbell twice",
      "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
      "createdAt": "2024-01-15T13:15:00Z",
      "updatedAt": "2024-01-15T13:20:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 156,
    "totalPages": 8
  }
}
```

### 2. Get Single Order

**Endpoint:** `GET /api/orders/:orderId`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "bb0e8400-e29b-41d4-a716-446655440010",
    "orderNumber": "ORD-2024-001",
    "customerName": "Jane Smith",
    "customerPhone": "******-987-6543",
    "items": [
      {
        "id": "dd0e8400-e29b-41d4-a716-446655440012",
        "menuItemId": "880e8400-e29b-41d4-a716-446655440005",
        "name": "Margherita Pizza",
        "price": 18.99,
        "quantity": 2,
        "customizations": [
          {
            "name": "Size",
            "options": ["Medium (12\")"],
            "additionalPrice": 4.00
          }
        ],
        "total": 49.98
      }
    ],
    "total": 58.47,
    "status": "confirmed",
    "paymentStatus": "paid",
    "orderType": "delivery",
    "createdAt": "2024-01-15T13:15:00Z"
  }
}
```

### 3. Update Order Status

**Endpoint:** `PATCH /api/orders/:orderId/status`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": "preparing"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Order status updated successfully",
  "data": {
    "id": "bb0e8400-e29b-41d4-a716-446655440010",
    "orderNumber": "ORD-2024-001",
    "status": "preparing",
    "updatedAt": "2024-01-15T13:25:00Z"
  }
}
```

### 4. Get Active Orders

**Endpoint:** `GET /api/orders/active`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "bb0e8400-e29b-41d4-a716-446655440010",
      "orderNumber": "ORD-2024-001",
      "customerName": "Jane Smith",
      "status": "preparing",
      "orderType": "delivery",
      "total": 58.47,
      "estimatedDeliveryTime": "2024-01-15T14:30:00Z",
      "createdAt": "2024-01-15T13:15:00Z"
    },
    {
      "id": "bb0e8400-e29b-41d4-a716-446655440013",
      "orderNumber": "ORD-2024-002",
      "customerName": "Bob Johnson",
      "status": "confirmed",
      "orderType": "pickup",
      "total": 32.50,
      "estimatedDeliveryTime": "2024-01-15T14:00:00Z",
      "createdAt": "2024-01-15T13:30:00Z"
    }
  ]
}
```

### 5. Cancel Order

**Endpoint:** `PATCH /api/orders/:orderId/cancel`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "reason": "Customer requested cancellation"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Order cancelled successfully",
  "data": {
    "id": "bb0e8400-e29b-41d4-a716-446655440010",
    "orderNumber": "ORD-2024-001",
    "status": "cancelled",
    "cancellationReason": "Customer requested cancellation",
    "updatedAt": "2024-01-15T13:35:00Z"
  }
}
```

### 6. Get Order Statistics

**Endpoint:** `GET /api/orders/stats?period=today`

**Query Parameters:**
- `period`: `today` | `week` | `month` | `year`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "totalOrders": 45,
    "pendingOrders": 3,
    "completedOrders": 38,
    "cancelledOrders": 4,
    "totalRevenue": 1250.75,
    "averageOrderValue": 27.79,
    "averagePreparationTime": 18
  }
}

## 👥 Staff Management

### 1. Get Staff Members

**Endpoint:** `GET /api/staff`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "ee0e8400-e29b-41d4-a716-446655440014",
      "username": "alice_admin",
      "email": "<EMAIL>",
      "name": "Alice Johnson",
      "role": "admin",
      "phone": "******-111-2222",
      "isActive": true,
      "permissions": [
        "manage_orders",
        "manage_menu",
        "view_analytics",
        "manage_staff"
      ],
      "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
      "createdBy": "550e8400-e29b-41d4-a716-************",
      "createdAt": "2024-01-12T10:00:00Z",
      "updatedAt": "2024-01-15T09:00:00Z",
      "lastLogin": "2024-01-15T08:30:00Z"
    },
    {
      "id": "ee0e8400-e29b-41d4-a716-446655440015",
      "username": "bob_staff",
      "email": "<EMAIL>",
      "name": "Bob Wilson",
      "role": "staff",
      "phone": "******-333-4444",
      "isActive": true,
      "permissions": [
        "manage_orders",
        "view_menu"
      ],
      "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
      "createdBy": "550e8400-e29b-41d4-a716-************",
      "createdAt": "2024-01-13T14:00:00Z",
      "updatedAt": "2024-01-13T14:00:00Z",
      "lastLogin": "2024-01-15T07:45:00Z"
    }
  ]
}
```

### 2. Create Staff Member

**Endpoint:** `POST /api/staff`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Carol Davis",
  "email": "<EMAIL>",
  "phone": "******-555-6666",
  "role": "staff",
  "permissions": [
    "manage_orders",
    "view_menu"
  ]
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Staff member created successfully",
  "data": {
    "staff": {
      "id": "ee0e8400-e29b-41d4-a716-446655440016",
      "username": "carol_staff",
      "email": "<EMAIL>",
      "name": "Carol Davis",
      "role": "staff",
      "phone": "******-555-6666",
      "isActive": true,
      "permissions": [
        "manage_orders",
        "view_menu"
      ],
      "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
      "createdBy": "550e8400-e29b-41d4-a716-************",
      "createdAt": "2024-01-15T14:00:00Z",
      "updatedAt": "2024-01-15T14:00:00Z"
    },
    "credentials": {
      "username": "carol_staff",
      "password": "TempPass123!",
      "temporaryPassword": true
    }
  }
}
```

### 3. Update Staff Member

**Endpoint:** `PUT /api/staff/:staffId`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Carol Davis-Smith",
  "phone": "******-555-7777",
  "permissions": [
    "manage_orders",
    "view_menu",
    "view_analytics"
  ]
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Staff member updated successfully",
  "data": {
    "id": "ee0e8400-e29b-41d4-a716-446655440016",
    "name": "Carol Davis-Smith",
    "phone": "******-555-7777",
    "permissions": [
      "manage_orders",
      "view_menu",
      "view_analytics"
    ],
    "updatedAt": "2024-01-15T14:30:00Z"
  }
}
```

### 4. Reset Staff Password

**Endpoint:** `POST /api/staff/:staffId/reset-password`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Password reset successfully",
  "data": {
    "username": "carol_staff",
    "password": "NewTemp456!",
    "temporaryPassword": true
  }
}
```

### 5. Toggle Staff Status

**Endpoint:** `PATCH /api/staff/:staffId/status`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "isActive": false
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Staff status updated successfully",
  "data": {
    "id": "ee0e8400-e29b-41d4-a716-446655440016",
    "name": "Carol Davis-Smith",
    "isActive": false,
    "updatedAt": "2024-01-15T15:00:00Z"
  }
}
```

### 6. Get Available Permissions

**Endpoint:** `GET /api/staff/permissions`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "manage_orders",
      "name": "Manage Orders",
      "description": "Create, update, and manage customer orders",
      "category": "Orders"
    },
    {
      "id": "manage_menu",
      "name": "Manage Menu",
      "description": "Add, edit, and remove menu items and categories",
      "category": "Menu"
    },
    {
      "id": "view_analytics",
      "name": "View Analytics",
      "description": "Access restaurant analytics and reports",
      "category": "Analytics"
    },
    {
      "id": "manage_staff",
      "name": "Manage Staff",
      "description": "Add, edit, and manage staff members",
      "category": "Staff"
    },
    {
      "id": "manage_settings",
      "name": "Manage Settings",
      "description": "Update restaurant settings and configuration",
      "category": "Settings"
    }
  ]
}
```

### 7. Get Staff Statistics

**Endpoint:** `GET /api/staff/stats`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "totalStaff": 3,
    "activeStaff": 2,
    "inactiveStaff": 1,
    "adminCount": 1,
    "staffCount": 2
  }
}

## 📊 Analytics

### 1. Get Dashboard Statistics

**Endpoint:** `GET /api/analytics/dashboard?period=month`

**Query Parameters:**
- `period`: `today` | `week` | `month` | `year`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "totalRevenue": 15750.50,
    "totalOrders": 567,
    "averageOrderValue": 27.79,
    "totalCustomers": 234,
    "revenueGrowth": 12.5,
    "ordersGrowth": 8.3,
    "customerGrowth": 15.2,
    "topSellingItems": [
      {
        "id": "880e8400-e29b-41d4-a716-446655440005",
        "name": "Margherita Pizza",
        "quantity": 89,
        "revenue": 1689.11
      },
      {
        "id": "880e8400-e29b-41d4-a716-44665544000d",
        "name": "Pepperoni Pizza",
        "quantity": 76,
        "revenue": 1671.24
      }
    ],
    "recentOrders": [
      {
        "id": "bb0e8400-e29b-41d4-a716-446655440010",
        "orderNumber": "ORD-2024-001",
        "customerName": "Jane Smith",
        "total": 58.47,
        "status": "delivered",
        "createdAt": "2024-01-15T13:15:00Z"
      }
    ]
  }
}
```

### 2. Get Revenue Analytics

**Endpoint:** `GET /api/analytics/revenue?startDate=2024-01-01&endDate=2024-01-31&groupBy=day`

**Query Parameters:**
- `startDate`: Start date (YYYY-MM-DD)
- `endDate`: End date (YYYY-MM-DD)
- `groupBy`: `day` | `week` | `month`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "period": "2024-01-01 to 2024-01-31",
    "data": [
      {
        "date": "2024-01-01",
        "revenue": 450.75,
        "orders": 18,
        "averageOrderValue": 25.04
      },
      {
        "date": "2024-01-02",
        "revenue": 523.25,
        "orders": 21,
        "averageOrderValue": 24.92
      }
    ],
    "totalRevenue": 15750.50,
    "totalOrders": 567,
    "growth": {
      "revenue": 12.5,
      "orders": 8.3
    }
  }
}
```

### 3. Get Real-time Statistics

**Endpoint:** `GET /api/analytics/realtime`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "activeOrders": 5,
    "todayRevenue": 1250.75,
    "todayOrders": 45,
    "averageOrderValue": 27.79,
    "busyLevel": "medium"
  }
}
```

## 🔔 Notifications

### 1. Get Notifications

**Endpoint:** `GET /api/notifications?page=1&limit=20&unreadOnly=false`

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `unreadOnly`: Show only unread notifications (default: false)

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "ff0e8400-e29b-41d4-a716-446655440017",
      "title": "New Order Received",
      "message": "Order #ORD-2024-001 has been placed by Jane Smith",
      "type": "order_new",
      "priority": "high",
      "isRead": false,
      "data": {
        "orderId": "bb0e8400-e29b-41d4-a716-446655440010",
        "orderNumber": "ORD-2024-001",
        "customerName": "Jane Smith",
        "total": 58.47
      },
      "userId": "550e8400-e29b-41d4-a716-************",
      "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
      "createdAt": "2024-01-15T13:15:00Z",
      "readAt": null
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "totalPages": 3
  }
}
```

### 2. Mark Notification as Read

**Endpoint:** `PATCH /api/notifications/:notificationId/read`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Notification marked as read",
  "data": {
    "id": "ff0e8400-e29b-41d4-a716-446655440017",
    "isRead": true,
    "readAt": "2024-01-15T14:00:00Z"
  }
}
```

### 3. Get Notification Settings

**Endpoint:** `GET /api/notifications/settings`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "emailNotifications": true,
    "pushNotifications": true,
    "smsNotifications": false,
    "notificationTypes": {
      "order_new": {
        "enabled": true,
        "email": true,
        "push": true,
        "sms": false
      },
      "order_cancelled": {
        "enabled": true,
        "email": true,
        "push": true,
        "sms": true
      },
      "payment_received": {
        "enabled": true,
        "email": false,
        "push": true,
        "sms": false
      }
    },
    "quietHours": {
      "enabled": true,
      "startTime": "22:00",
      "endTime": "08:00"
    }
  }
}
```

## 📁 File Upload

### 1. Upload Document (Registration)

**Endpoint:** `POST /api/documents/upload`

**Request Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: multipart/form-data
```

**Request Body (FormData):**
```javascript
{
  document: File, // Image file
  documentType: "business_license" // "business_license" | "food_license" | "tax_certificate"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Document uploaded successfully",
  "data": {
    "url": "https://storage.example.com/documents/business_license_123.jpg"
  }
}
```

## ❌ Error Handling

### Standard Error Response Format

**Response (400/401/403/404/422/500):**
```json
{
  "success": false,
  "message": "Validation failed",
  "error": "VALIDATION_ERROR",
  "details": {
    "field": "email",
    "message": "Email is already in use"
  }
}
```

### Common Error Codes

| HTTP Status | Error Code | Description |
|-------------|------------|-------------|
| 400 | `VALIDATION_ERROR` | Request validation failed |
| 401 | `UNAUTHORIZED` | Authentication required |
| 401 | `INVALID_TOKEN` | JWT token is invalid or expired |
| 403 | `FORBIDDEN` | Insufficient permissions |
| 403 | `VERIFICATION_PENDING` | Account verification pending |
| 404 | `NOT_FOUND` | Resource not found |
| 409 | `CONFLICT` | Resource already exists |
| 422 | `UNPROCESSABLE_ENTITY` | Request cannot be processed |
| 429 | `RATE_LIMIT_EXCEEDED` | Too many requests |
| 500 | `INTERNAL_ERROR` | Server error |

### Authentication Errors

**Invalid Credentials (401):**
```json
{
  "success": false,
  "message": "Invalid username or password",
  "error": "INVALID_CREDENTIALS"
}
```

**Verification Pending (403):**
```json
{
  "success": false,
  "message": "Your restaurant documents are still under verification",
  "error": "VERIFICATION_PENDING",
  "details": {
    "verificationStatus": "pending",
    "documentsStatus": {
      "businessLicense": "pending",
      "foodLicense": "approved",
      "taxCertificate": "rejected"
    }
  }
}
```

## 🔧 Implementation Notes

### JWT Token Structure
```json
{
  "sub": "550e8400-e29b-41d4-a716-************",
  "username": "johndoe",
  "role": "owner",
  "restaurantId": "660e8400-e29b-41d4-a716-446655440001",
  "permissions": ["manage_orders", "manage_menu"],
  "iat": **********,
  "exp": **********
}
```

### File Upload Constraints
- **Maximum file size**: 10MB per file
- **Allowed formats**: JPG, PNG, PDF
- **Document types**: business_license, food_license, tax_certificate
- **Image types**: menu_item, restaurant_logo, restaurant_cover

### Rate Limiting
- **Authentication endpoints**: 5 requests per minute
- **File upload endpoints**: 10 requests per minute
- **General API endpoints**: 100 requests per minute
- **Real-time endpoints**: 60 requests per minute

### Database Indexes Required
```sql
-- Users table
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_restaurant_id ON users(restaurant_id);

-- Orders table
CREATE INDEX idx_orders_restaurant_id ON orders(restaurant_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_customer_phone ON orders(customer_phone);

-- Menu items table
CREATE INDEX idx_menu_items_restaurant_id ON menu_items(restaurant_id);
CREATE INDEX idx_menu_items_category_id ON menu_items(category_id);
CREATE INDEX idx_menu_items_is_available ON menu_items(is_available);

-- Notifications table
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
```

This comprehensive API specification provides everything needed for backend implementation! 🚀
```
```
```
