import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { FormValidationError, MenuItem } from '../../types';

interface ReviewPublishStepProps {
  formData: Partial<MenuItem>;
  onUpdateField: (field: string, value: any) => void;
  onValidateField: (field: string) => Promise<FormValidationError[]>;
  onSubmitForm: () => Promise<void>;
  errors: FormValidationError[];
  isSubmitting: boolean;
}

export function ReviewPublishStep({
  formData,
  onUpdateField,
  onValidateField: _onValidateField,
  onSubmitForm,
  errors,
  isSubmitting,
}: ReviewPublishStepProps) {
  const [publishOptions, setPublishOptions] = useState({
    publishImmediately: true,
    notifyCustomers: true,
    markAsNew: true,
  });
  
  const fadeAnimation = useSharedValue(0);
  const slideAnimation = useSharedValue(0);

  useEffect(() => {
    fadeAnimation.value = withTiming(1, { duration: 500 });
    slideAnimation.value = withSpring(1, { damping: 20, stiffness: 100 });
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
    transform: [{ translateY: (1 - slideAnimation.value) * 50 }],
  }));

  const handlePublishOptionChange = (option: string, value: boolean) => {
    setPublishOptions(prev => ({
      ...prev,
      [option]: value,
    }));
  };

  const handlePublish = async () => {
    try {
      // Update form data with publish options
      onUpdateField('isActive', publishOptions.publishImmediately);
      
      await onSubmitForm();
      
      Alert.alert(
        'Success!',
        'Your menu item has been saved successfully.',
        [{ text: 'OK', style: 'default' }]
      );
    } catch (_error) {
      Alert.alert(
        'Error',
        'Failed to save menu item. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  const getCompletionPercentage = () => {
    const requiredFields = ['name', 'description', 'category', 'basePrice'];
    const completedFields = requiredFields.filter(field => 
      formData[field as keyof MenuItem] && 
      String(formData[field as keyof MenuItem]).trim() !== ''
    );
    return Math.round((completedFields.length / requiredFields.length) * 100);
  };

  const hasErrors = errors.length > 0;
  const completionPercentage = getCompletionPercentage();
  const canPublish = completionPercentage >= 75 && !hasErrors;

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Review & Publish</Text>
          <Text style={styles.subtitle}>
            Review your menu item details and publish when ready
          </Text>
        </View>

        {/* Completion Status */}
        <View style={styles.completionCard}>
          <View style={styles.completionHeader}>
            <IconSymbol
              name={completionPercentage === 100 ? 'checkmark.circle.fill' : 'clock'}
              size={24}
              color={completionPercentage === 100 ? Colors.light.success : Colors.light.warning}
            />
            <Text style={styles.completionTitle}>
              {completionPercentage}% Complete
            </Text>
          </View>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${completionPercentage}%` }
              ]} 
            />
          </View>
          <Text style={styles.completionDescription}>
            {completionPercentage === 100 
              ? 'Your menu item is ready to publish!'
              : 'Complete all required fields to publish'
            }
          </Text>
        </View>

        {/* Validation Errors */}
        {hasErrors && (
          <View style={styles.errorsCard}>
            <View style={styles.errorsHeader}>
              <IconSymbol
                name="exclamationmark.triangle.fill"
                size={20}
                color={Colors.light.error}
              />
              <Text style={styles.errorsTitle}>Issues to Fix</Text>
            </View>
            {errors.map((error, index) => (
              <View key={index} style={styles.errorItem}>
                <Text style={styles.errorText}>• {error.message}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Menu Item Preview */}
        <View style={styles.previewCard}>
          <Text style={styles.previewTitle}>Menu Item Preview</Text>
          
          <View style={styles.previewContent}>
            {/* Primary Image */}
            {formData.images && formData.images.length > 0 && (
              <Image
                source={{ uri: formData.images[0].url }}
                style={styles.previewImage}
                resizeMode="cover"
              />
            )}
            
            {/* Basic Info */}
            <View style={styles.previewInfo}>
              <Text style={styles.previewName}>{formData.name || 'Untitled Item'}</Text>
              <Text style={styles.previewCategory}>{formData.category || 'No Category'}</Text>
              <Text style={styles.previewPrice}>
                ${(formData as any).basePrice?.toFixed(2) || '0.00'}
              </Text>
              <Text style={styles.previewDescription} numberOfLines={3}>
                {formData.description || 'No description provided'}
              </Text>
            </View>
            
            {/* Tags */}
            {formData.tags && formData.tags.length > 0 && (
              <View style={styles.previewTags}>
                {formData.tags.slice(0, 3).map((tag, index) => (
                  <View key={index} style={styles.previewTag}>
                    <Text style={styles.previewTagText}>{tag}</Text>
                  </View>
                ))}
                {formData.tags.length > 3 && (
                  <Text style={styles.moreTagsText}>+{formData.tags.length - 3} more</Text>
                )}
              </View>
            )}
          </View>
        </View>

        {/* Publishing Options */}
        <View style={styles.publishOptionsCard}>
          <Text style={styles.publishOptionsTitle}>Publishing Options</Text>
          
          <PublishOption
            title="Publish Immediately"
            description="Make this item available to customers right away"
            value={publishOptions.publishImmediately}
            onToggle={(value) => handlePublishOptionChange('publishImmediately', value)}
            icon="bolt.fill"
          />
          
          <PublishOption
            title="Notify Customers"
            description="Send push notification to customers who have saved your restaurant in their favorites"
            value={publishOptions.notifyCustomers}
            onToggle={(value) => handlePublishOptionChange('notifyCustomers', value)}
            icon="bell.fill"
            disabled={!publishOptions.publishImmediately}
          />

          <PublishOption
            title="Mark as New Item"
            description="Display 'NEW' badge on this item to attract customer attention"
            value={publishOptions.markAsNew}
            onToggle={(value) => handlePublishOptionChange('markAsNew', value)}
            icon="star.fill"
            disabled={!publishOptions.publishImmediately}
          />
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.publishButton,
              (!canPublish || isSubmitting) && styles.publishButtonDisabled,
            ]}
            onPress={handlePublish}
            disabled={!canPublish || isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color={Colors.light.textInverse} />
            ) : (
              <IconSymbol
                name="checkmark.circle.fill"
                size={20}
                color={Colors.light.textInverse}
              />
            )}
            <Text style={styles.publishButtonText}>
              {isSubmitting ? 'Publishing...' : 'Publish Menu Item'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.saveDraftButton}
            onPress={() => {
              onUpdateField('isActive', false);
              onSubmitForm();
            }}
            disabled={isSubmitting}
          >
            <IconSymbol
              name="doc.text"
              size={20}
              color={Colors.light.textInverse}
            />
            <Text style={styles.saveDraftButtonText}>Save as Draft</Text>
          </TouchableOpacity>
        </View>

        {/* Final Tips */}
        <View style={styles.tipsContainer}>
          <View style={styles.tipsHeader}>
            <IconSymbol
              name="lightbulb"
              size={20}
              color={Colors.light.textInverse}
            />
            <Text style={styles.tipsTitle}>Final Checklist</Text>
          </View>
          <View style={styles.tipsList}>
            <View style={styles.tipItemContainer}>
              <Text style={styles.tipCheckmark}>✓</Text>
              <Text style={styles.tipItem}>Item name is clear and appetizing</Text>
            </View>
            <View style={styles.tipItemContainer}>
              <Text style={styles.tipCheckmark}>✓</Text>
              <Text style={styles.tipItem}>Description highlights key ingredients and preparation</Text>
            </View>
            <View style={styles.tipItemContainer}>
              <Text style={styles.tipCheckmark}>✓</Text>
              <Text style={styles.tipItem}>Price is competitive and profitable</Text>
            </View>
            <View style={styles.tipItemContainer}>
              <Text style={styles.tipCheckmark}>✓</Text>
              <Text style={styles.tipItem}>High-quality images are uploaded</Text>
            </View>
            <View style={styles.tipItemContainer}>
              <Text style={styles.tipCheckmark}>✓</Text>
              <Text style={styles.tipItem}>Allergen information is accurate</Text>
            </View>
            <View style={styles.tipItemContainer}>
              <Text style={styles.tipCheckmark}>✓</Text>
              <Text style={styles.tipItem}>Customization options are configured</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </Animated.View>
  );
}

interface PublishOptionProps {
  title: string;
  description: string;
  value: boolean;
  onToggle: (value: boolean) => void;
  icon: string;
  disabled?: boolean;
}

function PublishOption({ 
  title, 
  description, 
  value, 
  onToggle, 
  icon, 
  disabled = false 
}: PublishOptionProps) {
  return (
    <View style={[styles.publishOption, disabled && styles.publishOptionDisabled]}>
      <View style={styles.publishOptionIcon}>
        <IconSymbol
          name={icon}
          size={20}
          color={disabled ? Colors.light.textTertiary : Colors.light.primary}
        />
      </View>
      <View style={styles.publishOptionContent}>
        <Text style={[styles.publishOptionTitle, disabled && styles.publishOptionTitleDisabled]}>
          {title}
        </Text>
        <Text style={[styles.publishOptionDescription, disabled && styles.publishOptionDescriptionDisabled]}>
          {description}
        </Text>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        disabled={disabled}
        trackColor={{
          false: Colors.light.border,
          true: Colors.light.primaryLight,
        }}
        thumbColor={
          value ? Colors.light.primary : Colors.light.textTertiary
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    lineHeight: 22,
  },
  completionCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  completionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  completionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: Colors.light.border,
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.light.success,
    borderRadius: 4,
  },
  completionDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  errorsCard: {
    backgroundColor: Colors.light.errorLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.error,
  },
  errorsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  errorsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 8,
  },
  errorItem: {
    marginBottom: 4,
  },
  errorText: {
    fontSize: 14,
    color: Colors.light.error,
    lineHeight: 18,
  },
  previewCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  previewContent: {
    gap: 12,
  },
  previewImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: Colors.light.border,
  },
  previewInfo: {
    gap: 4,
  },
  previewName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  previewCategory: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: '600',
  },
  previewPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.success,
    marginVertical: 4,
  },
  previewDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  previewTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    alignItems: 'center',
  },
  previewTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: Colors.light.primary, // Use primary color for better contrast with white text
  },
  previewTagText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF', // Snow white color
  },
  moreTagsText: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    fontStyle: 'italic',
  },
  publishOptionsCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  publishOptionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 16,
  },
  publishOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  publishOptionDisabled: {
    opacity: 0.5,
  },
  publishOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.primaryLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  publishOptionContent: {
    flex: 1,
    marginRight: 12,
  },
  publishOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 2,
  },
  publishOptionTitleDisabled: {
    color: Colors.light.textTertiary,
  },
  publishOptionDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  publishOptionDescriptionDisabled: {
    color: Colors.light.textTertiary,
  },
  actionButtons: {
    gap: 12,
    marginBottom: 20,
  },
  publishButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.success,
    gap: 8,
  },
  publishButtonDisabled: {
    backgroundColor: Colors.light.border,
  },
  publishButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
  },
  saveDraftButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.primary, // Use primary color for better contrast with white text
    borderWidth: 2,
    borderColor: Colors.light.primary,
    gap: 8,
  },
  saveDraftButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF', // Snow white color
  },
  tipsContainer: {
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textInverse,
    marginLeft: 8,
  },
  tipsList: {
    gap: 8,
  },
  tipItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  tipCheckmark: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.success,
  },
  tipItem: {
    fontSize: 14,
    color: Colors.light.textInverse,
    lineHeight: 20,
    flex: 1,
  },
});

export default ReviewPublishStep;
