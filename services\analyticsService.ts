import api, { ApiResponse, handleApiError } from './api';

// Analytics types
export interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  totalCustomers: number;
  revenueGrowth: number;
  ordersGrowth: number;
  customerGrowth: number;
  topSellingItems: Array<{
    id: string;
    name: string;
    quantity: number;
    revenue: number;
  }>;
  recentOrders: Array<{
    id: string;
    orderNumber: string;
    customerName: string;
    total: number;
    status: string;
    createdAt: string;
  }>;
}

export interface RevenueAnalytics {
  period: string;
  data: Array<{
    date: string;
    revenue: number;
    orders: number;
    averageOrderValue: number;
  }>;
  totalRevenue: number;
  totalOrders: number;
  growth: {
    revenue: number;
    orders: number;
  };
}

export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
  customerRetentionRate: number;
  averageOrdersPerCustomer: number;
  customerLifetimeValue: number;
  topCustomers: Array<{
    id: string;
    name: string;
    totalOrders: number;
    totalSpent: number;
    lastOrderDate: string;
  }>;
  customerGrowth: Array<{
    date: string;
    newCustomers: number;
    totalCustomers: number;
  }>;
}

export interface MenuAnalytics {
  totalItems: number;
  activeItems: number;
  topSellingItems: Array<{
    id: string;
    name: string;
    category: string;
    totalSold: number;
    revenue: number;
    averageRating: number;
  }>;
  lowPerformingItems: Array<{
    id: string;
    name: string;
    category: string;
    totalSold: number;
    revenue: number;
  }>;
  categoryPerformance: Array<{
    categoryId: string;
    categoryName: string;
    totalSold: number;
    revenue: number;
    itemCount: number;
  }>;
}

export interface OrderAnalytics {
  totalOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  averagePreparationTime: number;
  ordersByType: {
    delivery: number;
    pickup: number;
    dineIn: number;
  };
  ordersByStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  peakHours: Array<{
    hour: number;
    orderCount: number;
  }>;
  orderTrends: Array<{
    date: string;
    orders: number;
    completed: number;
    cancelled: number;
  }>;
}

export interface FinancialAnalytics {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  profitMargin: number;
  revenueByPaymentMethod: Array<{
    method: string;
    amount: number;
    percentage: number;
  }>;
  monthlyFinancials: Array<{
    month: string;
    revenue: number;
    expenses: number;
    profit: number;
  }>;
  taxSummary: {
    totalTax: number;
    taxRate: number;
    taxableAmount: number;
  };
}

class AnalyticsService {
  // Dashboard overview
  async getDashboardStats(period: 'today' | 'week' | 'month' | 'year' = 'month'): Promise<ApiResponse<DashboardStats>> {
    try {
      const response = await api.get<ApiResponse<DashboardStats>>('/analytics/dashboard', {
        params: { period }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Revenue analytics
  async getRevenueAnalytics(
    startDate: string,
    endDate: string,
    groupBy: 'day' | 'week' | 'month' = 'day'
  ): Promise<ApiResponse<RevenueAnalytics>> {
    try {
      const response = await api.get<ApiResponse<RevenueAnalytics>>('/analytics/revenue', {
        params: { startDate, endDate, groupBy }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Customer analytics
  async getCustomerAnalytics(period: 'month' | 'quarter' | 'year' = 'month'): Promise<ApiResponse<CustomerAnalytics>> {
    try {
      const response = await api.get<ApiResponse<CustomerAnalytics>>('/analytics/customers', {
        params: { period }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Menu analytics
  async getMenuAnalytics(period: 'month' | 'quarter' | 'year' = 'month'): Promise<ApiResponse<MenuAnalytics>> {
    try {
      const response = await api.get<ApiResponse<MenuAnalytics>>('/analytics/menu', {
        params: { period }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Order analytics
  async getOrderAnalytics(
    startDate: string,
    endDate: string
  ): Promise<ApiResponse<OrderAnalytics>> {
    try {
      const response = await api.get<ApiResponse<OrderAnalytics>>('/analytics/orders', {
        params: { startDate, endDate }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Financial analytics
  async getFinancialAnalytics(
    startDate: string,
    endDate: string
  ): Promise<ApiResponse<FinancialAnalytics>> {
    try {
      const response = await api.get<ApiResponse<FinancialAnalytics>>('/analytics/financial', {
        params: { startDate, endDate }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Export analytics data
  async exportAnalytics(
    type: 'revenue' | 'customers' | 'orders' | 'menu' | 'financial',
    format: 'csv' | 'excel' | 'pdf',
    startDate: string,
    endDate: string
  ): Promise<ApiResponse<{ downloadUrl: string }>> {
    try {
      const response = await api.post<ApiResponse<{ downloadUrl: string }>>('/analytics/export', {
        type,
        format,
        startDate,
        endDate
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Real-time analytics
  async getRealTimeStats(): Promise<ApiResponse<{
    activeOrders: number;
    todayRevenue: number;
    todayOrders: number;
    averageOrderValue: number;
    busyLevel: 'low' | 'medium' | 'high';
  }>> {
    try {
      const response = await api.get<ApiResponse<{
        activeOrders: number;
        todayRevenue: number;
        todayOrders: number;
        averageOrderValue: number;
        busyLevel: 'low' | 'medium' | 'high';
      }>>('/analytics/realtime');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Comparative analytics
  async getComparativeAnalytics(
    metric: 'revenue' | 'orders' | 'customers',
    currentPeriod: { start: string; end: string },
    previousPeriod: { start: string; end: string }
  ): Promise<ApiResponse<{
    current: { value: number; data: Array<{ date: string; value: number }> };
    previous: { value: number; data: Array<{ date: string; value: number }> };
    growth: number;
    growthPercentage: number;
  }>> {
    try {
      const response = await api.post<ApiResponse<{
        current: { value: number; data: Array<{ date: string; value: number }> };
        previous: { value: number; data: Array<{ date: string; value: number }> };
        growth: number;
        growthPercentage: number;
      }>>('/analytics/compare', {
        metric,
        currentPeriod,
        previousPeriod
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Forecasting
  async getRevenueForecast(
    period: 'week' | 'month' | 'quarter',
    basedOnDays: number = 30
  ): Promise<ApiResponse<{
    forecast: Array<{ date: string; predicted: number; confidence: number }>;
    accuracy: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  }>> {
    try {
      const response = await api.get<ApiResponse<{
        forecast: Array<{ date: string; predicted: number; confidence: number }>;
        accuracy: number;
        trend: 'increasing' | 'decreasing' | 'stable';
      }>>('/analytics/forecast', {
        params: { period, basedOnDays }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export default new AnalyticsService();
