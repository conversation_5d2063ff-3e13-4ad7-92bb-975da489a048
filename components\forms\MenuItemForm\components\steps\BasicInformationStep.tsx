import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { FormValidationError, MenuItem } from '../../types';
import { getCategories } from '../../utils/api';

interface BasicInformationStepProps {
  formData: Partial<MenuItem>;
  onUpdateField: (field: string, value: any) => void;
  onValidateField: (field: string) => Promise<FormValidationError[]>;
  errors: FormValidationError[];
  onGenerateAIDescription: () => Promise<string>;
}

export function BasicInformationStep({
  formData,
  onUpdateField,
  onValidateField,
  errors,
  onGenerateAIDescription,
}: BasicInformationStepProps) {
  const [categories, setCategories] = useState<any[]>([]);
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  
  const fadeAnimation = useSharedValue(0);
  const slideAnimation = useSharedValue(0);

  useEffect(() => {
    fadeAnimation.value = withTiming(1, { duration: 500 });
    slideAnimation.value = withSpring(1, { damping: 20, stiffness: 100 });
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const categoriesData = await getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
    transform: [{ translateY: (1 - slideAnimation.value) * 50 }],
  }));

  const getFieldError = (fieldName: string) => {
    return errors.find(error => error.field === fieldName);
  };

  const handleFieldChange = async (field: string, value: string) => {
    onUpdateField(field, value);
    
    // Debounced validation
    setTimeout(() => {
      onValidateField(field);
    }, 500);
  };

  const handleGenerateDescription = async () => {
    if (!formData.name) {
      Alert.alert('Missing Information', 'Please enter a menu item name first.');
      return;
    }

    setIsGeneratingDescription(true);
    try {
      await onGenerateAIDescription();
      Alert.alert('Success', 'AI description has been generated!');
    } catch (_error) {
      Alert.alert('Error', 'Failed to generate description. Please try again.');
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  const handleCategorySelect = (category: any) => {
    onUpdateField('category', category.name);
    setShowCategoryDropdown(false);
    onValidateField('category');
  };

  const nameError = getFieldError('name');
  const descriptionError = getFieldError('description');
  const categoryError = getFieldError('category');

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >

        {/* Name */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>
            Name <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={[
              styles.textInput,
              nameError && styles.textInputError,
            ]}
            value={formData.name || ''}
            onChangeText={(text) => handleFieldChange('name', text)}
            placeholder="Menu item name"
            placeholderTextColor={Colors.light.textTertiary}
            maxLength={100}
          />
          {nameError && (
            <Text style={styles.errorText}>{nameError.message}</Text>
          )}
        </View>

        {/* Category Selection */}
        <View style={styles.fieldContainer}>
          <Text style={styles.fieldLabel}>
            Category <Text style={styles.required}>*</Text>
          </Text>
          <TouchableOpacity
            style={[
              styles.dropdownButton,
              categoryError && styles.dropdownButtonError,
            ]}
            onPress={() => setShowCategoryDropdown(!showCategoryDropdown)}
          >
            <Text
              style={[
                styles.dropdownButtonText,
                !formData.category && styles.dropdownPlaceholder,
              ]}
            >
              {formData.category || 'Select a category'}
            </Text>
            <Ionicons
              name={showCategoryDropdown ? 'chevron-up' : 'chevron-down'}
              size={16}
              color={Colors.light.textSecondary}
            />
          </TouchableOpacity>
          
          {showCategoryDropdown && (
            <View style={styles.dropdown}>
              <ScrollView style={styles.dropdownScroll} nestedScrollEnabled>
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category.id}
                    style={styles.dropdownItem}
                    onPress={() => handleCategorySelect(category)}
                  >
                    <View
                      style={[
                        styles.categoryColor,
                        { backgroundColor: category.color },
                      ]}
                    />
                    <Text style={styles.dropdownItemText}>{category.name}</Text>
                    {formData.category === category.name && (
                      <Ionicons
                        name="checkmark-circle"
                        size={16}
                        color={Colors.light.primary}
                      />
                    )}
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
          
          {categoryError && (
            <Text style={styles.errorText}>{categoryError.message}</Text>
          )}
        </View>

        {/* Description */}
        <View style={styles.fieldContainer}>
          <View style={styles.descriptionHeader}>
            <Text style={styles.fieldLabel}>
              Description <Text style={styles.required}>*</Text>
            </Text>
            <TouchableOpacity
              style={styles.aiButton}
              onPress={handleGenerateDescription}
              disabled={isGeneratingDescription}
            >
              {isGeneratingDescription ? (
                <ActivityIndicator size="small" color={Colors.light.primary} />
              ) : (
                <IconSymbol
                  name="star"
                  size={16}
                  color={Colors.light.primary}
                />
              )}
              <Text style={styles.aiButtonText}>
                {isGeneratingDescription ? 'Generating...' : 'AI Generate'}
              </Text>
            </TouchableOpacity>
          </View>

          <TextInput
            style={[
              styles.textArea,
              descriptionError && styles.textInputError,
            ]}
            value={formData.description || ''}
            onChangeText={(text) => handleFieldChange('description', text)}
            placeholder="Brief description"
            placeholderTextColor={Colors.light.textTertiary}
            multiline
            numberOfLines={4}
            maxLength={500}
            textAlignVertical="top"
          />

          <View style={styles.descriptionFooter}>
            <Text style={styles.characterCount}>
              {(formData.description || '').length}/500
            </Text>
            {descriptionError && (
              <Text style={styles.errorText}>{descriptionError.message}</Text>
            )}
          </View>
        </View>

        {/* Tips Section */}
        <View style={styles.tipsContainer}>
          <View style={styles.tipsHeader}>
            <IconSymbol
              name="lightbulb"
              size={20}
              color={Colors.light.warning}
            />
            <Text style={styles.tipsTitle}>Pro Tips</Text>
          </View>
          <View style={styles.tipsList}>
            <Text style={styles.tipItem}>
              • Use descriptive words that appeal to the senses
            </Text>
            <Text style={styles.tipItem}>
              • Mention key ingredients and cooking methods
            </Text>
            <Text style={styles.tipItem}>
              • Highlight what makes your dish unique
            </Text>
            <Text style={styles.tipItem}>
              • Keep it concise but informative (150-300 words ideal)
            </Text>
          </View>
        </View>
      </ScrollView>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 12, // Reduced from 16
  },
  header: {
    marginBottom: 16, // Reduced from 24
  },
  title: {
    fontSize: 20, // Reduced from 24
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 6, // Reduced from 8
  },
  subtitle: {
    fontSize: 14, // Reduced from 16
    color: Colors.light.textSecondary,
    lineHeight: 18, // Reduced from 22
  },
  fieldContainer: {
    marginBottom: 16, // Reduced from 24
  },
  fieldLabel: {
    fontSize: 14, // Reduced from 16
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 6, // Reduced from 8
  },
  required: {
    color: Colors.light.error,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8, // Reduced from 12
    paddingHorizontal: 12, // Reduced from 16
    paddingVertical: 8, // Reduced from 12
    fontSize: 14, // Reduced from 16
    color: Colors.light.text,
    backgroundColor: Colors.light.surface,
  },
  textInputError: {
    borderColor: Colors.light.error,
  },
  textArea: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8, // Reduced from 12
    paddingHorizontal: 12, // Reduced from 16
    paddingVertical: 8, // Reduced from 12
    fontSize: 14, // Reduced from 16
    color: Colors.light.text,
    backgroundColor: Colors.light.surface,
    minHeight: 80, // Reduced from 100
    textAlignVertical: 'top',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8, // Reduced from 12
    paddingHorizontal: 12, // Reduced from 16
    paddingVertical: 8, // Reduced from 12
    backgroundColor: Colors.light.surface,
  },
  dropdownButtonError: {
    borderColor: Colors.light.error,
  },
  dropdownButtonText: {
    fontSize: 14, // Reduced from 16
    color: Colors.light.text,
  },
  dropdownPlaceholder: {
    color: Colors.light.textTertiary,
  },
  dropdown: {
    marginTop: 4,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8, // Reduced from 12
    backgroundColor: Colors.light.surface,
    maxHeight: 150, // Reduced from 200
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dropdownScroll: {
    maxHeight: 150, // Reduced from 200
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12, // Reduced from 16
    paddingVertical: 8, // Reduced from 12
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  categoryColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  dropdownItemText: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
  },
  descriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  aiButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10, // Reduced from 12
    paddingVertical: 5, // Reduced from 6
    borderRadius: 6, // Reduced from 8
    backgroundColor: Colors.light.primary, // Use primary color for better contrast with white text
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  aiButtonText: {
    fontSize: 12, // Reduced from 14
    fontWeight: '600',
    color: '#FFFFFF', // Snow white color
    marginLeft: 4, // Reduced from 6
  },
  descriptionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 6, // Reduced from 8
  },
  characterCount: {
    fontSize: 11, // Reduced from 12
    color: Colors.light.textTertiary,
  },
  errorText: {
    fontSize: 11, // Reduced from 12
    color: Colors.light.error,
    marginTop: 3, // Reduced from 4
  },
  helperText: {
    fontSize: 11, // Reduced from 12
    color: Colors.light.textSecondary,
    marginTop: 3, // Reduced from 4
    lineHeight: 14, // Reduced from 16
  },
  tipsContainer: {
    backgroundColor: Colors.light.warningLight,
    borderRadius: 8, // Reduced from 12
    padding: 12, // Reduced from 16
    marginBottom: 16, // Reduced from 24
    borderWidth: 1,
    borderColor: Colors.light.warning,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8, // Reduced from 12
  },
  tipsTitle: {
    fontSize: 14, // Reduced from 16
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 6, // Reduced from 8
  },
  tipsList: {
    gap: 3, // Reduced from 4
  },
  tipItem: {
    fontSize: 12, // Reduced from 14
    color: Colors.light.textSecondary,
    lineHeight: 16, // Reduced from 20
  },
});

export default BasicInformationStep;
