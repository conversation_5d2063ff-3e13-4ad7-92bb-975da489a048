import { ListOptimization } from '@/utils/memoryOptimization';
import React, { memo, useCallback, useMemo } from 'react';
import { FlatList, FlatListProps, ListRenderItem, SectionList, TouchableOpacity, ViewStyle } from 'react-native';

interface OptimizedFlatListProps<T> extends Omit<FlatListProps<T>, 'renderItem'> {
  data: T[];
  renderItem: ListRenderItem<T>;
  itemHeight?: number;
  estimatedItemSize?: number;
  enableVirtualization?: boolean;
  optimizeMemory?: boolean;
  cacheSize?: number;
}

function OptimizedFlatListComponent<T extends { id?: string | number; key?: string | number }>({
  data,
  renderItem,
  itemHeight,
  estimatedItemSize = 80,
  enableVirtualization = true,
  optimizeMemory = true,
  cacheSize,
  style,
  ...props
}: OptimizedFlatListProps<T>) {
  
  // Optimized key extractor
  const keyExtractor = useCallback((item: T, index: number) => {
    return ListOptimization.keyExtractor(item, index);
  }, []);

  // Optimized getItemLayout for known item heights
  const getItemLayout = useMemo(() => {
    if (itemHeight) {
      return ListOptimization.getItemLayout(itemHeight);
    }
    return undefined;
  }, [itemHeight]);

  // Memory-optimized render item
  const optimizedRenderItem = useCallback<ListRenderItem<T>>((info) => {
    return renderItem(info);
  }, [renderItem]);

  // Calculate optimal performance settings
  const performanceSettings = useMemo(() => {
    const itemCount = data.length;
    const windowSize = ListOptimization.getOptimalWindowSize(itemCount);
    
    return {
      windowSize,
      initialNumToRender: Math.min(10, itemCount),
      maxToRenderPerBatch: Math.min(5, Math.ceil(itemCount / 10)),
      updateCellsBatchingPeriod: 50,
      removeClippedSubviews: enableVirtualization && itemCount > 20,
    };
  }, [data.length, enableVirtualization]);

  // Optimized style
  const optimizedStyle = useMemo<ViewStyle>(() => ({
    flex: 1,
    ...style as ViewStyle,
  }), [style]);

  return (
    <FlatList
      {...props}
      data={data}
      renderItem={optimizedRenderItem}
      keyExtractor={keyExtractor}
      getItemLayout={getItemLayout}
      style={optimizedStyle}
      
      // Performance optimizations
      windowSize={performanceSettings.windowSize}
      initialNumToRender={performanceSettings.initialNumToRender}
      maxToRenderPerBatch={performanceSettings.maxToRenderPerBatch}
      updateCellsBatchingPeriod={performanceSettings.updateCellsBatchingPeriod}
      removeClippedSubviews={performanceSettings.removeClippedSubviews}
      
      // Memory optimizations
      legacyImplementation={false}
      disableVirtualization={!enableVirtualization}
      
      // Additional optimizations
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={true}
      showsHorizontalScrollIndicator={false}
      
      // Override cache size if provided
      {...(cacheSize && { 
        initialNumToRender: Math.min(cacheSize, performanceSettings.initialNumToRender) 
      })}
    />
  );
}

// Memoize the component to prevent unnecessary re-renders
export const OptimizedFlatList = memo(OptimizedFlatListComponent) as <T>(
  props: OptimizedFlatListProps<T>
) => React.ReactElement;

// Optimized list item wrapper
interface OptimizedListItemProps {
  children: React.ReactNode;
  itemId: string | number;
  onPress?: () => void;
  style?: ViewStyle;
}

export const OptimizedListItem = memo<OptimizedListItemProps>(({
  children,
  itemId,
  onPress,
  style
}) => {
  const handlePress = useCallback(() => {
    onPress?.();
  }, [onPress]);

  return (
    <TouchableOpacity
      style={style}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      {children}
    </TouchableOpacity>
  );
}, (prevProps, nextProps) => {
  // Only re-render if itemId or essential props change
  return (
    prevProps.itemId === nextProps.itemId &&
    prevProps.onPress === nextProps.onPress &&
    prevProps.style === nextProps.style
  );
});

OptimizedListItem.displayName = 'OptimizedListItem';

// Hook for optimized list data
export function useOptimizedListData<T>(
  data: T[],
  filterFn?: (item: T) => boolean,
  sortFn?: (a: T, b: T) => number
) {
  return useMemo(() => {
    let processedData = data;

    if (filterFn) {
      processedData = processedData.filter(filterFn);
    }

    if (sortFn) {
      processedData = [...processedData].sort(sortFn);
    }

    return processedData;
  }, [data, filterFn, sortFn]);
}

// Optimized section list component
interface OptimizedSectionListProps<T> {
  sections: Array<{
    title: string;
    data: T[];
  }>;
  renderItem: ListRenderItem<T>;
  renderSectionHeader?: (info: { section: { title: string } }) => React.ReactElement | null;
  keyExtractor?: (item: T, index: number) => string;
  style?: ViewStyle;
}

export function OptimizedSectionList<T extends { id?: string | number }>({
  sections,
  renderItem,
  renderSectionHeader,
  keyExtractor,
  style
}: OptimizedSectionListProps<T>) {
  const optimizedKeyExtractor = useCallback((item: T, index: number) => {
    return keyExtractor ? keyExtractor(item, index) : ListOptimization.keyExtractor(item, index);
  }, [keyExtractor]);

  const optimizedRenderItem = useCallback<ListRenderItem<T>>((info) => {
    return renderItem(info);
  }, [renderItem]);

  const optimizedRenderSectionHeader = useCallback((info: { section: { title: string } }) => {
    return renderSectionHeader ? renderSectionHeader(info) : null;
  }, [renderSectionHeader]);

  return (
    <SectionList
      sections={sections}
      renderItem={optimizedRenderItem}
      renderSectionHeader={optimizedRenderSectionHeader}
      keyExtractor={optimizedKeyExtractor}
      style={style}
      
      // Performance optimizations
      removeClippedSubviews={true}
      initialNumToRender={10}
      maxToRenderPerBatch={5}
      windowSize={10}
      updateCellsBatchingPeriod={50}
      
      // Memory optimizations
      legacyImplementation={false}
      keyboardShouldPersistTaps="handled"
    />
  );
}
