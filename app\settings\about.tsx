import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Linking } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { router } from 'expo-router';

export default function AboutApp() {
  const handleLinkPress = (url: string) => {
    Linking.openURL(url);
  };

  const renderInfoItem = (label: string, value: string, icon?: string) => (
    <View style={styles.infoItem}>
      {icon && (
        <View style={styles.infoIcon}>
          <IconSymbol name={icon} size={20} color="#DC143C" />
        </View>
      )}
      <View style={styles.infoText}>
        <Text style={styles.infoLabel}>{label}</Text>
        <Text style={styles.infoValue}>{value}</Text>
      </View>
    </View>
  );

  const renderLinkItem = (title: string, url: string, icon: string) => (
    <TouchableOpacity
      style={styles.linkItem}
      onPress={() => handleLinkPress(url)}
    >
      <View style={styles.linkIcon}>
        <IconSymbol name={icon} size={20} color="#DC143C" />
      </View>
      <Text style={styles.linkText}>{title}</Text>
      <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#DC143C', '#B91C3C']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <IconSymbol name="chevron.left" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>About FoodWay</Text>
          <View style={styles.headerRight} />
        </View>
      </LinearGradient>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* App Logo & Info */}
        <View style={styles.section}>
          <View style={styles.logoSection}>
            <View style={styles.logoContainer}>
              <IconSymbol name="store" size={64} color="#DC143C" />
            </View>
            <Text style={styles.appName}>FoodWay</Text>
            <Text style={styles.appTagline}>Restaurant Management System</Text>
            <Text style={styles.appDescription}>
              Streamline your restaurant operations with our comprehensive management solution.
            </Text>
          </View>
        </View>

        {/* App Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Information</Text>
          <View style={styles.card}>
            {renderInfoItem('Version', '1.0.0', 'info.circle')}
            {renderInfoItem('Build', '2024.01.15', 'hammer')}
            {renderInfoItem('Platform', 'React Native', 'iphone')}
            {renderInfoItem('Last Updated', 'January 15, 2024', 'calendar')}
          </View>
        </View>

        {/* Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Features</Text>
          <View style={styles.card}>
            <View style={styles.featureItem}>
              <IconSymbol name="bag" size={20} color="#22C55E" />
              <Text style={styles.featureText}>Order Management</Text>
            </View>
            <View style={styles.featureItem}>
              <IconSymbol name="list.bullet" size={20} color="#3B82F6" />
              <Text style={styles.featureText}>Menu Management</Text>
            </View>
            <View style={styles.featureItem}>
              <IconSymbol name="chart.bar" size={20} color="#F59E0B" />
              <Text style={styles.featureText}>Analytics & Reports</Text>
            </View>
            <View style={styles.featureItem}>
              <IconSymbol name="person.2" size={20} color="#8B5CF6" />
              <Text style={styles.featureText}>Staff Management</Text>
            </View>
            <View style={styles.featureItem}>
              <IconSymbol name="bell" size={20} color="#EF4444" />
              <Text style={styles.featureText}>Real-time Notifications</Text>
            </View>
            <View style={styles.featureItem}>
              <IconSymbol name="gear" size={20} color="#6B7280" />
              <Text style={styles.featureText}>Customizable Settings</Text>
            </View>
          </View>
        </View>

        {/* Company Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Company</Text>
          <View style={styles.card}>
            {renderInfoItem('Developer', 'FoodWay Technologies', 'building.2')}
            {renderInfoItem('Founded', '2024', 'calendar')}
            {renderInfoItem('Location', 'Pakistan', 'location')}
            {renderInfoItem('Support', '<EMAIL>', 'envelope')}
          </View>
        </View>

        {/* Links */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Links</Text>
          <View style={styles.card}>
            {renderLinkItem('Privacy Policy', 'https://foodway.com/privacy', 'shield')}
            {renderLinkItem('Terms of Service', 'https://foodway.com/terms', 'doc.text')}
            {renderLinkItem('Website', 'https://foodway.com', 'globe')}
            {renderLinkItem('Support Center', 'https://foodway.com/support', 'questionmark.circle')}
          </View>
        </View>

        {/* Credits */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Credits</Text>
          <View style={styles.card}>
            <Text style={styles.creditsText}>
              Built with React Native and Expo. Icons provided by SF Symbols and Material Community Icons.
            </Text>
            <Text style={styles.creditsText}>
              Special thanks to the open-source community for making this app possible.
            </Text>
          </View>
        </View>

        {/* Copyright */}
        <View style={styles.section}>
          <View style={styles.copyrightContainer}>
            <Text style={styles.copyrightText}>
              © 2024 FoodWay Technologies. All rights reserved.
            </Text>
            <Text style={styles.copyrightSubtext}>
              Made with ❤️ for restaurant owners
            </Text>
          </View>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F5E8',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerRight: {
    width: 40,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  logoSection: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(220, 20, 60, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  appTagline: {
    fontSize: 16,
    color: '#DC143C',
    fontWeight: '600',
    marginBottom: 12,
  },
  appDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  card: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  infoIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(220, 20, 60, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  infoText: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  featureText: {
    fontSize: 16,
    color: '#1F2937',
    marginLeft: 12,
  },
  linkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  linkIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(220, 20, 60, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  linkText: {
    fontSize: 16,
    color: '#1F2937',
    flex: 1,
  },
  creditsText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 8,
  },
  copyrightContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  copyrightText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 4,
  },
  copyrightSubtext: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center',
  },
  bottomSpacing: {
    height: 40,
  },
});
