const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Basic performance optimizations
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Add WebP support
config.resolver.assetExts = [...config.resolver.assetExts, 'webp'];

// Add path alias
config.resolver.alias = {
  '@': '.',
};

// Basic transformer optimizations
config.transformer = {
  ...config.transformer,
  enableBabelRCLookup: false,
};

module.exports = config;
