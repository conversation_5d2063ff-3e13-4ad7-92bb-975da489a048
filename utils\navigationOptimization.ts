import { router } from 'expo-router';
import React from 'react';
import { InteractionManager } from 'react-native';

// Navigation performance optimization utilities
export class NavigationOptimizer {
  private static instance: NavigationOptimizer;
  private navigationQueue: Array<() => void> = [];
  private isNavigating = false;
  private preloadedScreens = new Set<string>();

  static getInstance(): NavigationOptimizer {
    if (!NavigationOptimizer.instance) {
      NavigationOptimizer.instance = new NavigationOptimizer();
    }
    return NavigationOptimizer.instance;
  }

  // Optimized navigation with interaction manager
  navigate(route: string, params?: any): void {
    if (this.isNavigating) {
      this.navigationQueue.push(() => this.navigate(route, params));
      return;
    }

    this.isNavigating = true;

    InteractionManager.runAfterInteractions(() => {
      try {
        if (params) {
          router.push({ pathname: route as any, params });
        } else {
          router.push(route as any);
        }
      } catch (error) {
        console.warn('Navigation error:', error);
      } finally {
        this.isNavigating = false;
        this.processNavigationQueue();
      }
    });
  }

  // Replace current screen
  replace(route: string, params?: any): void {
    InteractionManager.runAfterInteractions(() => {
      try {
        if (params) {
          router.replace({ pathname: route as any, params });
        } else {
          router.replace(route as any);
        }
      } catch (error) {
        console.warn('Navigation replace error:', error);
      }
    });
  }

  // Go back with optimization
  goBack(): void {
    InteractionManager.runAfterInteractions(() => {
      try {
        router.back();
      } catch (error) {
        console.warn('Navigation back error:', error);
      }
    });
  }

  // Process navigation queue
  private processNavigationQueue(): void {
    if (this.navigationQueue.length > 0) {
      const nextNavigation = this.navigationQueue.shift();
      if (nextNavigation) {
        setTimeout(nextNavigation, 100); // Small delay between navigations
      }
    }
  }

  // Preload screen for faster navigation
  preloadScreen(route: string): void {
    if (!this.preloadedScreens.has(route)) {
      this.preloadedScreens.add(route);
      // In a real implementation, this would preload the screen component
      console.log(`Preloaded screen: ${route}`);
    }
  }

  // Batch preload multiple screens
  preloadScreens(routes: string[]): void {
    routes.forEach(route => this.preloadScreen(route));
  }

  // Clear navigation queue
  clearQueue(): void {
    this.navigationQueue = [];
  }

  // Get navigation statistics
  getStats(): {
    queueLength: number;
    isNavigating: boolean;
    preloadedScreens: number;
  } {
    return {
      queueLength: this.navigationQueue.length,
      isNavigating: this.isNavigating,
      preloadedScreens: this.preloadedScreens.size,
    };
  }
}

// Screen transition configurations
export const TransitionConfigs = {
  // Slide transitions
  slideFromRight: {
    gestureDirection: 'horizontal',
    transitionSpec: {
      open: { animation: 'timing', config: { duration: 250 } },
      close: { animation: 'timing', config: { duration: 200 } },
    },
    cardStyleInterpolator: ({ current, layouts }: any) => ({
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    }),
  },

  // Fade transition
  fade: {
    transitionSpec: {
      open: { animation: 'timing', config: { duration: 200 } },
      close: { animation: 'timing', config: { duration: 150 } },
    },
    cardStyleInterpolator: ({ current }: any) => ({
      cardStyle: {
        opacity: current.progress,
      },
    }),
  },

  // Modal transition
  modal: {
    gestureDirection: 'vertical',
    transitionSpec: {
      open: { animation: 'timing', config: { duration: 300 } },
      close: { animation: 'timing', config: { duration: 250 } },
    },
    cardStyleInterpolator: ({ current, layouts }: any) => ({
      cardStyle: {
        transform: [
          {
            translateY: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.height, 0],
            }),
          },
        ],
      },
    }),
  },
};

// Navigation performance hooks
export const useOptimizedNavigation = () => {
  const navigationOptimizer = NavigationOptimizer.getInstance();

  const navigate = (route: string, params?: any) => {
    navigationOptimizer.navigate(route, params);
  };

  const replace = (route: string, params?: any) => {
    navigationOptimizer.replace(route, params);
  };

  const goBack = () => {
    navigationOptimizer.goBack();
  };

  const preloadScreen = (route: string) => {
    navigationOptimizer.preloadScreen(route);
  };

  return {
    navigate,
    replace,
    goBack,
    preloadScreen,
  };
};

// Screen focus optimization
export const useScreenFocus = (callback: () => void, deps: any[] = []) => {
  React.useEffect(() => {
    const unsubscribe = router.addListener?.('focus', () => {
      InteractionManager.runAfterInteractions(callback);
    });

    return unsubscribe;
  }, deps);
};

// Lazy screen loading utility
export const createLazyScreen = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  return React.lazy(() => {
    return new Promise((resolve) => {
      InteractionManager.runAfterInteractions(async () => {
        try {
          const module = await importFn();
          resolve(module);
        } catch (error) {
          console.warn('Lazy screen loading error:', error);
          if (fallback) {
            resolve({ default: fallback } as any);
          }
        }
      });
    });
  });
};

// Navigation gesture optimization
export const NavigationGestures = {
  // Optimized swipe back gesture
  swipeBack: {
    enabled: true,
    edgeWidth: 20,
    gestureResponseDistance: {
      horizontal: 50,
      vertical: 135,
    },
  },

  // Optimized modal dismiss gesture
  modalDismiss: {
    enabled: true,
    gestureResponseDistance: {
      vertical: 100,
    },
  },
};

// Tab navigation optimization
export const TabOptimization = {
  // Lazy load tab screens
  lazy: true,
  
  // Optimize tab bar
  tabBarOptions: {
    activeTintColor: '#FF6B35',
    inactiveTintColor: '#666666',
    style: {
      backgroundColor: '#FFFFFF',
      borderTopWidth: 1,
      borderTopColor: '#E0E0E0',
      height: 60,
    },
    labelStyle: {
      fontSize: 12,
      fontWeight: '500',
    },
    iconStyle: {
      marginBottom: 4,
    },
  },

  // Preload adjacent tabs
  preloadAdjacent: true,
};

// Stack navigation optimization
export const StackOptimization = {
  // Default screen options
  screenOptions: {
    headerShown: true,
    gestureEnabled: true,
    cardOverlayEnabled: true,
    animationTypeForReplace: 'push' as const,
    
    // Performance optimizations
    freezeOnBlur: true,
    detachPreviousScreen: true,
  },

  // Header optimization
  headerOptions: {
    headerTitleAlign: 'center' as const,
    headerStyle: {
      backgroundColor: '#FFFFFF',
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    headerTitleStyle: {
      fontSize: 18,
      fontWeight: '600',
      color: '#1A1A1A',
    },
    headerBackTitleVisible: false,
  },
};

// Export singleton
export const navigationOptimizer = NavigationOptimizer.getInstance();

// Utility functions
export const optimizedNavigate = (route: string, params?: any) => {
  navigationOptimizer.navigate(route, params);
};

export const optimizedReplace = (route: string, params?: any) => {
  navigationOptimizer.replace(route, params);
};

export const optimizedGoBack = () => {
  navigationOptimizer.goBack();
};
