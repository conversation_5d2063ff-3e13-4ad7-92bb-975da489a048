export type UserRole = 'owner' | 'admin' | 'staff';

export interface User {
  id: string;
  username: string;
  password: string;
  role: User<PERSON><PERSON>;
  createdAt: string;
  name?: string;
  email?: string;
  isActive?: boolean;
}

export interface AuthContextType {
  user: User | null;
  validateCredentials: (username: string, password: string) => Promise<User | null>;
  login: (username: string, password: string) => Promise<User | null>;
  logout: () => void;
  isAuthenticated: boolean;
  hasRole: (role: UserRole | UserRole[]) => boolean;
}

export interface GeneratedUser {
  id: string;
  username: string;
  password: string;
  role: UserRole;
  createdAt: string;
}

// Mock users for development
export const mockUsers: User[] = [
  {
    id: 'user_owner_1',
    username: 'owner',
    password: 'owner123',
    role: 'owner',
    name: 'Restaurant Owner',
    email: '<EMAIL>',
    createdAt: '01/01/2025',
    isActive: true
  },
  {
    id: 'user_admin_1',
    username: 'admin',
    password: 'admin123',
    role: 'admin',
    name: 'Restaurant Admin',
    email: '<EMAIL>',
    createdAt: '01/02/2025',
    isActive: true
  },
  {
    id: 'user_staff_1',
    username: 'staff',
    password: 'staff123',
    role: 'staff',
    name: 'Restaurant Staff',
    email: '<EMAIL>',
    createdAt: '01/03/2025',
    isActive: true
  }
];

// Role-based access permissions
export const rolePermissions = {
  owner: ['dashboard', 'orders', 'menu', 'analytics', 'settings', 'staff-management'],
  admin: ['dashboard', 'orders', 'menu', 'analytics', 'settings'],
  staff: ['orders']
} as const;

export type Permission = typeof rolePermissions[UserRole][number];
