{"cli": {"version": ">= 12.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "ios": {"buildConfiguration": "Debug"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "ios": {"buildConfiguration": "Release"}}, "production": {"android": {"buildType": "aab", "gradleCommand": ":app:bundleRelease"}, "ios": {"buildConfiguration": "Release"}}, "production-optimized": {"extends": "production", "env": {"NODE_ENV": "production"}, "android": {"buildType": "aab", "gradleCommand": ":app:bundleRelease", "config": "release.gradle"}, "ios": {"buildConfiguration": "Release", "config": "release.xcconfig"}}}, "submit": {"production": {}}}