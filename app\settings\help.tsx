import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Linking } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { router } from 'expo-router';

export default function HelpSupport() {
  const handleContactSupport = (method: string) => {
    switch (method) {
      case 'email':
        Linking.openURL('mailto:<EMAIL>?subject=Restaurant App Support');
        break;
      case 'phone':
        Linking.openURL('tel:+1234567890');
        break;
      case 'chat':
        Alert.alert('Live Chat', 'Live chat feature will be available soon!');
        break;
      case 'whatsapp':
        Linking.openURL('https://wa.me/1234567890');
        break;
    }
  };

  const handleFAQ = (question: string) => {
    const faqs = {
      'orders': 'To manage orders, go to the Orders tab. You can view, accept, and update order status from there.',
      'menu': 'To update your menu, go to the Menu tab. You can add, edit, or remove items as needed.',
      'staff': 'Staff management is available in the dashboard. Only owners can add or remove staff members.',
      'payments': 'Payment settings can be configured in the Settings tab under Business Information.',
      'notifications': 'Notification preferences can be managed in Settings > Notifications.',
      'analytics': 'View your restaurant analytics in the Analytics tab for insights on performance.',
    };
    
    Alert.alert('FAQ Answer', faqs[question as keyof typeof faqs] || 'More detailed help coming soon!');
  };

  const renderContactOption = (
    title: string,
    description: string,
    icon: string,
    method: string,
    color: string = '#DC143C'
  ) => (
    <TouchableOpacity
      style={styles.contactOption}
      onPress={() => handleContactSupport(method)}
    >
      <View style={[styles.contactIcon, { backgroundColor: `${color}15` }]}>
        <IconSymbol name={icon} size={24} color={color} />
      </View>
      <View style={styles.contactText}>
        <Text style={styles.contactTitle}>{title}</Text>
        <Text style={styles.contactDescription}>{description}</Text>
      </View>
      <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
    </TouchableOpacity>
  );

  const renderFAQItem = (question: string, key: string) => (
    <TouchableOpacity
      style={styles.faqItem}
      onPress={() => handleFAQ(key)}
    >
      <Text style={styles.faqQuestion}>{question}</Text>
      <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#DC143C', '#B91C3C']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <IconSymbol name="chevron.left" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Help & Support</Text>
          <View style={styles.headerRight} />
        </View>
      </LinearGradient>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Contact Support */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Support</Text>
          <View style={styles.card}>
            {renderContactOption(
              'Email Support',
              'Get help via email - <EMAIL>',
              'envelope',
              'email'
            )}
            {renderContactOption(
              'Phone Support',
              'Call us at +1 (234) 567-890',
              'phone',
              'phone',
              '#22C55E'
            )}
            {renderContactOption(
              'Live Chat',
              'Chat with our support team',
              'message.circle',
              'chat',
              '#3B82F6'
            )}
            {renderContactOption(
              'WhatsApp',
              'Message us on WhatsApp',
              'message',
              'whatsapp',
              '#25D366'
            )}
          </View>
        </View>

        {/* Frequently Asked Questions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
          <View style={styles.card}>
            {renderFAQItem('How do I manage orders?', 'orders')}
            {renderFAQItem('How do I update my menu?', 'menu')}
            {renderFAQItem('How do I add staff members?', 'staff')}
            {renderFAQItem('How do I set up payments?', 'payments')}
            {renderFAQItem('How do I manage notifications?', 'notifications')}
            {renderFAQItem('How do I view analytics?', 'analytics')}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.card}>
            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => Alert.alert('Feature Request', 'Thank you! We\'ll consider your request.')}
            >
              <View style={styles.quickActionIcon}>
                <IconSymbol name="lightbulb" size={20} color="#F59E0B" />
              </View>
              <Text style={styles.quickActionText}>Request a Feature</Text>
              <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => Alert.alert('Bug Report', 'Thank you for reporting! We\'ll investigate.')}
            >
              <View style={styles.quickActionIcon}>
                <IconSymbol name="exclamationmark.triangle" size={20} color="#EF4444" />
              </View>
              <Text style={styles.quickActionText}>Report a Bug</Text>
              <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => Linking.openURL('https://foodway.com/documentation')}
            >
              <View style={styles.quickActionIcon}>
                <IconSymbol name="book" size={20} color="#8B5CF6" />
              </View>
              <Text style={styles.quickActionText}>View Documentation</Text>
              <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Support Hours */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support Hours</Text>
          <View style={styles.card}>
            <View style={styles.supportHours}>
              <Text style={styles.supportHoursTitle}>We're here to help!</Text>
              <Text style={styles.supportHoursText}>Monday - Friday: 9:00 AM - 6:00 PM</Text>
              <Text style={styles.supportHoursText}>Saturday: 10:00 AM - 4:00 PM</Text>
              <Text style={styles.supportHoursText}>Sunday: Closed</Text>
              <Text style={styles.supportHoursNote}>
                For urgent issues outside business hours, please email us and we'll respond as soon as possible.
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F5E8',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerRight: {
    width: 40,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  card: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  contactOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  contactIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  contactText: {
    flex: 1,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 2,
  },
  contactDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  faqItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  faqQuestion: {
    fontSize: 16,
    color: '#1F2937',
    flex: 1,
  },
  quickAction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  quickActionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  quickActionText: {
    fontSize: 16,
    color: '#1F2937',
    flex: 1,
  },
  supportHours: {
    alignItems: 'center',
  },
  supportHoursTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  supportHoursText: {
    fontSize: 16,
    color: '#4B5563',
    marginBottom: 4,
  },
  supportHoursNote: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 12,
    fontStyle: 'italic',
  },
  bottomSpacing: {
    height: 40,
  },
});
