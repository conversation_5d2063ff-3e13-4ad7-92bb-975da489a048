import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { AvailabilitySchedule, FormValidationError, MenuItem, PricingTier } from '../../types';

interface PricingAvailabilityStepProps {
  formData: Partial<MenuItem>;
  onUpdateField: (field: string, value: any) => void;
  onValidateField: (field: string) => Promise<FormValidationError[]>;
  errors: FormValidationError[];
  analytics: any;
}

export function PricingAvailabilityStep({
  formData,
  onUpdateField,
  onValidateField,
  errors,
  analytics: _analytics,
}: PricingAvailabilityStepProps) {
  const [showAdvancedPricing, setShowAdvancedPricing] = useState(false);
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  
  const fadeAnimation = useSharedValue(0);
  const slideAnimation = useSharedValue(0);

  useEffect(() => {
    fadeAnimation.value = withTiming(1, { duration: 500 });
    slideAnimation.value = withSpring(1, { damping: 20, stiffness: 100 });
    
    // Initialize availability if not set
    if (!formData.availability) {
      const defaultAvailability: AvailabilitySchedule = {
        isAvailable: true,
        schedules: [],
        specialDates: [],
        isAlwaysAvailable: true,
        schedule: [],
        specialHours: [],
        isSeasonalItem: false,

      };
      onUpdateField('availability', defaultAvailability);
    }
    
    // Initialize pricing if not set
    if (!formData.price || formData.price.length === 0) {
      const defaultPricing: PricingTier[] = [{
        id: '1',
        name: 'Regular',
        price: 0,
        currency: 'PKR',
        taxRate: 0,
        isDefault: true,
        description: 'Standard price',
      }];
      onUpdateField('price', defaultPricing);
    }
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
    transform: [{ translateY: (1 - slideAnimation.value) * 50 }],
  }));

  const getFieldError = (fieldName: string) => {
    return errors.find(error => error.field === fieldName);
  };

  const handlePriceChange = (tierIndex: number, field: string, value: any) => {
    const currentPricing = formData.price || [];
    const updatedPricing = [...currentPricing];
    updatedPricing[tierIndex] = {
      ...updatedPricing[tierIndex],
      [field]: value,
    };
    onUpdateField('price', updatedPricing);
    onValidateField('price');
  };

  const addPricingTier = () => {
    const currentPricing = formData.price || [];
    const newTier: PricingTier = {
      id: Date.now().toString(),
      name: `Tier ${currentPricing.length + 1}`,
      price: 0,
      currency: 'PKR',
      taxRate: 0,
      isDefault: false,
      description: '',
    };
    onUpdateField('price', [...currentPricing, newTier]);
  };

  const removePricingTier = (tierIndex: number) => {
    const currentPricing = formData.price || [];
    if (currentPricing.length <= 1) {
      Alert.alert('Cannot Remove', 'At least one pricing tier is required.');
      return;
    }
    
    const updatedPricing = currentPricing.filter((_, index) => index !== tierIndex);
    onUpdateField('price', updatedPricing);
  };

  const handleAvailabilityChange = (field: string, value: any) => {
    const currentAvailability = formData.availability || {};
    const updatedAvailability = {
      ...currentAvailability,
      [field]: value,
    };
    onUpdateField('availability', updatedAvailability);
    onValidateField('availability');
  };

  const handlePreparationTimeChange = (value: string) => {
    const numericValue = parseInt(value) || 0;
    onUpdateField('preparationTime', numericValue);
    onValidateField('preparationTime');
  };

  const handlePreparationTimeSuggestion = (minutes: number) => {
    onUpdateField('preparationTime', minutes);
    onValidateField('preparationTime');
  };

  const handlePriceSuggestion = (tierIndex: number, price: number) => {
    handlePriceChange(tierIndex, 'price', price);
  };

  const priceError = getFieldError('price');
  const prepTimeError = getFieldError('preparationTime');

  // Preparation time suggestions (5-30 minutes with 5-minute gaps)
  const prepTimeSuggestions = [5, 10, 15, 20, 25, 30];

  // Price suggestions in PKR
  const priceSuggestions = [50, 100, 150, 200, 250, 300, 400, 500, 750, 1000, 1500, 2000];

  const weekDays = [
    { key: 'monday', label: 'Mon' },
    { key: 'tuesday', label: 'Tue' },
    { key: 'wednesday', label: 'Wed' },
    { key: 'thursday', label: 'Thu' },
    { key: 'friday', label: 'Fri' },
    { key: 'saturday', label: 'Sat' },
    { key: 'sunday', label: 'Sun' },
  ];

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Pricing */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Price</Text>

          {/* Pricing Tiers */}
          {(formData.price || []).map((tier, index) => (
            <View key={tier.id} style={styles.pricingTier}>
              <View style={styles.pricingTierHeader}>
                <TextInput
                  style={styles.tierNameInput}
                  value={tier.name}
                  onChangeText={(text) => handlePriceChange(index, 'name', text)}
                  placeholder="Tier name"
                  placeholderTextColor={Colors.light.textTertiary}
                />
                {!tier.isDefault && (
                  <TouchableOpacity
                    style={styles.removeTierButton}
                    onPress={() => removePricingTier(index)}
                  >
                    <IconSymbol
                      name="trash"
                      size={16}
                      color={Colors.light.error}
                    />
                  </TouchableOpacity>
                )}
              </View>
              
              <View style={styles.priceInputContainer}>
                <Text style={styles.currencySymbol}>PKR</Text>
                <TextInput
                  style={[
                    styles.priceInput,
                    priceError && styles.textInputError,
                  ]}
                  value={tier.price?.toString() || ''}
                  onChangeText={(text) => {
                    const numericValue = parseFloat(text) || 0;
                    handlePriceChange(index, 'price', numericValue);
                  }}
                  placeholder="0"
                  placeholderTextColor={Colors.light.textTertiary}
                  keyboardType="numeric"
                />
              </View>

              {/* Price Suggestions */}
              <View style={styles.suggestionsContainer}>
                <Text style={styles.suggestionsLabel}>Quick Price Options:</Text>
                <View style={styles.suggestionsGrid}>
                  {priceSuggestions.map((price) => (
                    <TouchableOpacity
                      key={price}
                      style={[
                        styles.suggestionButton,
                        tier.price === price && styles.suggestionButtonSelected,
                      ]}
                      onPress={() => handlePriceSuggestion(index, price)}
                    >
                      <Text
                        style={[
                          styles.suggestionButtonText,
                          tier.price === price && styles.suggestionButtonTextSelected,
                        ]}
                      >
                        PKR {price}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              
              {showAdvancedPricing && (
                <TextInput
                  style={styles.tierDescriptionInput}
                  value={tier.description || ''}
                  onChangeText={(text) => handlePriceChange(index, 'description', text)}
                  placeholder="Description (e.g., Small size, Large portion)"
                  placeholderTextColor={Colors.light.textTertiary}
                />
              )}
            </View>
          ))}

          {showAdvancedPricing && (
            <TouchableOpacity
              style={styles.addTierButton}
              onPress={addPricingTier}
            >
              <IconSymbol
                name="plus"
                size={16}
                color={Colors.light.primary}
              />
              <Text style={styles.addTierButtonText}>Add Pricing Tier</Text>
            </TouchableOpacity>
          )}

          {priceError && (
            <Text style={styles.errorText}>{priceError.message}</Text>
          )}
        </View>

        {/* Preparation Time */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preparation Time</Text>
          <View style={styles.prepTimeContainer}>
            <TextInput
              style={[
                styles.prepTimeInput,
                prepTimeError && styles.textInputError,
              ]}
              value={formData.preparationTime?.toString() || ''}
              onChangeText={handlePreparationTimeChange}
              placeholder="15"
              placeholderTextColor={Colors.light.textTertiary}
              keyboardType="numeric"
            />
            <Text style={styles.prepTimeUnit}>minutes</Text>
          </View>

          {/* Preparation Time Suggestions */}
          <View style={styles.suggestionsContainer}>
            <Text style={styles.suggestionsLabel}>Quick Time Options:</Text>
            <View style={styles.suggestionsGrid}>
              {prepTimeSuggestions.map((minutes) => (
                <TouchableOpacity
                  key={minutes}
                  style={[
                    styles.suggestionButton,
                    formData.preparationTime === minutes && styles.suggestionButtonSelected,
                  ]}
                  onPress={() => handlePreparationTimeSuggestion(minutes)}
                >
                  <Text
                    style={[
                      styles.suggestionButtonText,
                      formData.preparationTime === minutes && styles.suggestionButtonTextSelected,
                    ]}
                  >
                    {minutes} min
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {prepTimeError && (
            <Text style={styles.errorText}>{prepTimeError.message}</Text>
          )}
        </View>

        {/* Availability */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Availability</Text>

          <View style={styles.availabilityOption}>
            <Text style={styles.availabilityOptionTitle}>Always Available</Text>
            <Switch
              value={formData.availability?.isAlwaysAvailable || false}
              onValueChange={(value) => handleAvailabilityChange('isAlwaysAvailable', value)}
              trackColor={{
                false: Colors.light.border,
                true: Colors.light.primaryLight,
              }}
              thumbColor={
                formData.availability?.isAlwaysAvailable
                  ? Colors.light.primary
                  : Colors.light.textTertiary
              }
            />
          </View>

          {/* Custom Schedule */}
          {!formData.availability?.isAlwaysAvailable && (
            <View style={styles.customSchedule}>
              <Text style={styles.customScheduleTitle}>Custom Schedule</Text>
              <View style={styles.weekDaysContainer}>
                {weekDays.map((day) => (
                  <TouchableOpacity
                    key={day.key}
                    style={[
                      styles.dayButton,
                      selectedDays.includes(day.key) && styles.dayButtonSelected,
                    ]}
                    onPress={() => {
                      const updatedDays = selectedDays.includes(day.key)
                        ? selectedDays.filter(d => d !== day.key)
                        : [...selectedDays, day.key];
                      setSelectedDays(updatedDays);
                    }}
                  >
                    <Text
                      style={[
                        styles.dayButtonText,
                        selectedDays.includes(day.key) && styles.dayButtonTextSelected,
                      ]}
                    >
                      {day.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}


        </View>
      </ScrollView>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 12, // Reduced from 16
  },
  header: {
    marginBottom: 16, // Reduced from 24
  },
  title: {
    fontSize: 20, // Reduced from 24
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 6, // Reduced from 8
  },
  subtitle: {
    fontSize: 14, // Reduced from 16
    color: Colors.light.textSecondary,
    lineHeight: 18, // Reduced from 22
  },
  section: {
    marginBottom: 20, // Reduced from 32
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12, // Reduced from 16
  },
  sectionTitle: {
    fontSize: 16, // Reduced from 20
    fontWeight: '600',
    color: Colors.light.text,
  },
  advancedToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10, // Reduced from 12
    paddingVertical: 5, // Reduced from 6
    borderRadius: 6, // Reduced from 8
    backgroundColor: Colors.light.primaryLight,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  advancedToggleText: {
    fontSize: 12, // Reduced from 14
    fontWeight: '600',
    color: Colors.light.primary,
    marginRight: 3, // Reduced from 4
  },
  pricingTier: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8, // Reduced from 12
    padding: 12, // Reduced from 16
    marginBottom: 10, // Reduced from 12
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  pricingTierHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8, // Reduced from 12
  },
  tierNameInput: {
    flex: 1,
    fontSize: 14, // Reduced from 16
    fontWeight: '600',
    color: Colors.light.text,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    paddingVertical: 3, // Reduced from 4
    marginRight: 10, // Reduced from 12
  },
  removeTierButton: {
    padding: 6, // Reduced from 8
    borderRadius: 6, // Reduced from 8
    backgroundColor: Colors.light.errorLight,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6, // Reduced from 8
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginRight: 8,
  },
  priceInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    borderBottomWidth: 2,
    borderBottomColor: Colors.light.primary,
    paddingVertical: 8,
    textAlign: 'left',
  },
  tierDescriptionInput: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginTop: 8,
  },
  addTierButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    borderStyle: 'dashed',
    marginTop: 8,
  },
  addTierButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
    marginLeft: 8,
  },
  prepTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  prepTimeInput: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    borderBottomWidth: 2,
    borderBottomColor: Colors.light.primary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    minWidth: 80,
    textAlign: 'center',
    marginRight: 12,
  },
  prepTimeUnit: {
    fontSize: 16,
    color: Colors.light.textSecondary,
  },
  availabilityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  availabilityOptionContent: {
    flex: 1,
    marginRight: 16,
  },
  availabilityOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  availabilityOptionSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  customSchedule: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  customScheduleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  weekDaysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  dayButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  dayButtonSelected: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  dayButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  dayButtonTextSelected: {
    color: '#FFFFFF', // Snow white color
  },
  suggestionsContainer: {
    marginTop: 12,
  },
  suggestionsLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  suggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  suggestionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.light.primary,
    borderWidth: 1,
    borderColor: Colors.light.primary,
    minWidth: 70,
    alignItems: 'center',
  },
  suggestionButtonSelected: {
    backgroundColor: Colors.light.primaryDark,
    borderColor: Colors.light.primaryDark,
  },
  suggestionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF', // Snow white color
  },
  suggestionButtonTextSelected: {
    color: '#FFFFFF', // Snow white color
  },
  analyticsSection: {
    backgroundColor: Colors.light.infoLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: Colors.light.info,
  },
  analyticsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  analyticsCard: {
    flex: 1,
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  analyticsValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 4,
  },
  analyticsLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  textInputError: {
    borderColor: Colors.light.error,
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.error,
    marginTop: 4,
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: 4,
    lineHeight: 16,
  },
});

export default PricingAvailabilityStep;
