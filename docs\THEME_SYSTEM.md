# Simple Light & Dark Mode Theme System

## Overview

This restaurant app now includes a simple, unified light and dark mode theme system that provides:

- **Light/Dark/System modes** with automatic switching
- **Persistent theme preferences** using AsyncStorage
- **Orange brand colors** consistent across both themes
- **Time-based order status colors** (Green/Orange/Red)
- **Easy-to-use theme components** and hooks

## Quick Start

### 1. Using Theme Colors in Components

```tsx
import { useThemeColors } from '@/contexts/SimpleThemeContext';

export default function MyComponent() {
  const colors = useThemeColors();
  
  return (
    <View style={{ backgroundColor: colors.background }}>
      <Text style={{ color: colors.text }}>Hello World</Text>
    </View>
  );
}
```

### 2. Adding Theme Toggle Buttons

```tsx
import { ThemeToggle, ThemeToggleIcon, ThemeSelector } from '@/components/ThemeToggle';

// Full toggle button with label
<ThemeToggle size="medium" />

// Icon-only toggle
<ThemeToggleIcon size={24} />

// Full theme selector (Light/Dark/System)
<ThemeSelector />
```

### 3. Dynamic Styling with Theme

```tsx
import { useThemeColors } from '@/contexts/SimpleThemeContext';

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.background,
    borderColor: colors.border,
  },
  text: {
    color: colors.text,
  },
  button: {
    backgroundColor: colors.primary,
  },
});

export default function MyComponent() {
  const colors = useThemeColors();
  const styles = createStyles(colors);
  
  return <View style={styles.container}>...</View>;
}
```

## Available Colors

### Light Theme
- **Primary**: `#FF6B35` (Orange)
- **Background**: `#FFFFFF` (White)
- **Text**: `#212121` (Dark Gray)
- **Surface**: `#FFFFFF` (White)
- **Border**: `#E0E0E0` (Light Gray)

### Dark Theme
- **Primary**: `#FF6B35` (Orange - same as light)
- **Background**: `#121212` (Dark)
- **Text**: `#FFFFFF` (White)
- **Surface**: `#1E1E1E` (Dark Gray)
- **Border**: `#404040` (Medium Gray)

### Status Colors (Both Themes)
- **Success**: `#4CAF50` (Green)
- **Warning**: `#FF9800` (Orange)
- **Error**: `#F44336` (Red)
- **Info**: `#2196F3` (Blue)

### Order Status Colors (Time-based)
- **On Time** (0-14 min): `#4CAF50` (Green)
- **In Progress** (15-29 min): `#FF9800` (Orange)
- **Urgent** (30+ min): `#F44336` (Red)

## Theme Modes

1. **Light Mode**: Always uses light theme
2. **Dark Mode**: Always uses dark theme  
3. **System Mode**: Follows device system preference

## Components

### ThemeToggle
- Full toggle button with theme name label
- Cycles through: Light → Dark → System → Light
- Available sizes: `small`, `medium`, `large`

### ThemeToggleIcon
- Icon-only toggle button
- Same cycling behavior as ThemeToggle
- Customizable size

### ThemeSelector
- Complete theme selector with all three options
- Shows current selection with visual indicators
- Best for settings pages

## Hooks

### useSimpleTheme()
Returns complete theme context:
```tsx
const { theme, colors, isDark, setTheme, toggleTheme } = useSimpleTheme();
```

### useThemeColors()
Returns just the color palette:
```tsx
const colors = useThemeColors();
```

## Utility Functions

### getOrderStatusColor()
```tsx
import { getOrderStatusColor } from '@/contexts/SimpleThemeContext';

const statusColor = getOrderStatusColor(25, colors); // Returns orange for 25 minutes
```

### getOrderStatusText()
```tsx
import { getOrderStatusText } from '@/contexts/SimpleThemeContext';

const statusText = getOrderStatusText(25); // Returns "In Progress"
```

## Integration

The theme system is already integrated into:

1. **App Layout** (`app/_layout.tsx`) - Theme provider wrapper
2. **Settings Screen** (`app/(tabs)/settings.tsx`) - Theme selector
3. **Theme Demo Screen** (`app/(tabs)/theme-demo.tsx`) - Complete showcase

## Migration from Existing Themes

If you have existing screens using other theme systems:

1. Replace theme imports:
   ```tsx
   // Old
   import { Colors } from '@/constants/Theme';
   
   // New
   import { useThemeColors } from '@/contexts/SimpleThemeContext';
   ```

2. Update component structure:
   ```tsx
   // Old
   const styles = StyleSheet.create({...});
   
   // New
   const colors = useThemeColors();
   const styles = createStyles(colors);
   ```

3. Replace static colors with dynamic ones:
   ```tsx
   // Old
   backgroundColor: Colors.light.primary
   
   // New
   backgroundColor: colors.primary
   ```

## Testing

Visit the **Theme Demo** tab to see all colors, components, and functionality in action. The demo includes:

- Theme toggle controls
- Color showcase
- Order status examples
- Text style examples
- Surface examples

## Performance

- Theme changes are instant with no flicker
- Colors are memoized for optimal performance
- AsyncStorage operations are non-blocking
- System theme detection is automatic
