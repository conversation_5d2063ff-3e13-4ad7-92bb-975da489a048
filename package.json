{"name": "restaurant_app1", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "lint": "expo lint", "analyze-bundle": "node scripts/analyze-bundle.js", "analyze-bundle-visual": "npx expo export --platform android && npx react-native-bundle-visualizer", "performance-test": "expo start --dev-client", "build:android": "eas build --platform android --profile production-optimized", "build:preview": "eas build --platform all --profile preview", "test:optimizations": "node scripts/test-optimizations.js", "optimize:complete": "npm run analyze-bundle && npm run test:optimizations"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@reduxjs/toolkit": "^2.8.2", "date-fns": "^4.1.0", "expo": "53.0.19", "expo-blur": "~14.1.5", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.6", "expo-device": "^7.1.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "^18.1.6", "expo-notifications": "~0.31.4", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "react": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-super-grid": "^6.0.1", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "victory-native": "^41.17.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "depcheck": "^1.4.7", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "react-native-bundle-visualizer": "^3.1.3", "typescript": "~5.8.3"}, "private": true}