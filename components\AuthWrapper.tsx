import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSegments } from 'expo-router';
import React, { ReactNode, useEffect } from 'react';

interface AuthWrapperProps {
  children: ReactNode;
}

export default function AuthWrapper({ children }: AuthWrapperProps) {
  const router = useRouter();
  const segments = useSegments();
  const { isAuthenticated, user } = useAuth();

  // Handle navigation based on auth state
  useEffect(() => {
    const inAuthGroup = segments[0] === '(auth)';
    const inLoginScreen = segments[0] === 'login';
    const inOnboardingScreen = segments[0] === 'onboarding';

    // Add a small delay to ensure router is ready
    const timer = setTimeout(() => {
      // If not authenticated, show onboarding (always for non-authenticated users)
      if (!isAuthenticated && !inAuthGroup && !inLoginScreen && !inOnboardingScreen) {
        router.replace('/onboarding');
      } else if (isAuthenticated && (inAuthGroup || inLoginScreen || inOnboardingScreen)) {
        // User is authenticated but in auth screens, redirect based on role
        if (user?.role === 'staff') {
          router.replace('/(tabs)/orders');
        } else {
          router.replace('/(tabs)');
        }
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [isAuthenticated, segments, router, user?.role]);

  // Wrap authenticated users with verification guard
  if (isAuthenticated) {
    return (
      <VerificationGuard>
        {children}
      </VerificationGuard>
    );
  }

  return <>{children}</>;
}


