import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { BlurView } from 'expo-blur';
import { StyleSheet } from 'react-native';

export default function BlurTabBarBackground() {
  return (
    <BlurView
      // Use a red tint for restaurant theme
      tint="dark"
      intensity={80}
      style={[StyleSheet.absoluteFill, { backgroundColor: 'rgba(220, 20, 60, 0.7)' }]}
    />
  );
}

export function useBottomTabOverflow() {
  return useBottomTabBarHeight();
}
