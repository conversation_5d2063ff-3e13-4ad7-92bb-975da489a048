import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, getOrderStatusColor, getOrderStatusText } from '@/constants/Theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchOrders, updateOrderStatus } from '@/store/slices/ordersSlice';
import { Order, OrderStatus } from '@/types';
import { format, isToday, isYesterday } from 'date-fns';
import { useEffect, useMemo, useState } from 'react';
import {
    Alert,
    FlatList,
    Modal,
    RefreshControl,
    ScrollView,
    StyleSheet,
    Switch,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

// const { width } = Dimensions.get('window'); // Removed unused variable

export default function OrdersScreen() {
  return (
    <ProtectedRoute requiredPermission="orders">
      <OrdersContent />
    </ProtectedRoute>
  );
}

function OrdersContent() {
  const dispatch = useAppDispatch();
  const { orders, loading } = useAppSelector(state => state.orders);

  // Enhanced state management
  const [selectedFilter, setSelectedFilter] = useState<OrderStatus | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'time' | 'total' | 'customer'>('time');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [selectedOrderDetails, setSelectedOrderDetails] = useState<Order | null>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [dateFilter, setDateFilter] = useState<'today' | 'yesterday' | 'week' | 'all'>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    dispatch(fetchOrders());

    // Auto-refresh every 30 seconds if enabled
    let interval: ReturnType<typeof setInterval>;
    if (autoRefresh) {
      interval = setInterval(() => {
        dispatch(fetchOrders());
      }, 30000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [dispatch, autoRefresh]);

  const handleRefresh = () => {
    dispatch(fetchOrders());
  };

  // Enhanced filtering and sorting logic
  const filteredAndSortedOrders = useMemo(() => {
    let filtered = orders.filter(order => {
      // Status filter
      const statusMatch = selectedFilter === 'all' || order.status === selectedFilter;

      // Search filter
      const searchMatch = searchQuery === '' ||
        order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.customer.phone.includes(searchQuery);

      // Date filter
      const orderDate = new Date(order.orderTime);
      let dateMatch = true;

      switch (dateFilter) {
        case 'today':
          dateMatch = isToday(orderDate);
          break;
        case 'yesterday':
          dateMatch = isYesterday(orderDate);
          break;
        case 'week':
          const weekAgo = new Date();
          weekAgo.setDate(weekAgo.getDate() - 7);
          dateMatch = orderDate >= weekAgo;
          break;
        default:
          dateMatch = true;
      }

      return statusMatch && searchMatch && dateMatch;
    });

    // Sort orders
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'time':
          comparison = new Date(a.orderTime).getTime() - new Date(b.orderTime).getTime();
          break;
        case 'total':
          comparison = a.total - b.total;
          break;
        case 'customer':
          comparison = a.customer.name.localeCompare(b.customer.name);
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [orders, selectedFilter, searchQuery, dateFilter, sortBy, sortOrder]);

  // Order statistics
  const orderStats = useMemo(() => {
    const today = filteredAndSortedOrders.filter(order => isToday(new Date(order.orderTime)));
    const pending = filteredAndSortedOrders.filter(order => order.status === 'pending').length;
    const preparing = filteredAndSortedOrders.filter(order => order.status === 'preparing').length;
    const ready = filteredAndSortedOrders.filter(order => order.status === 'ready').length;
    const totalRevenue = today.reduce((sum, order) => sum + order.total, 0);

    return {
      todayOrders: today.length,
      pending,
      preparing,
      ready,
      totalRevenue
    };
  }, [filteredAndSortedOrders]);

  const handleStatusUpdate = (orderId: string, newStatus: OrderStatus) => {
    Alert.alert(
      'Update Order Status',
      `Change order status to ${getOrderStatusText(newStatus)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            dispatch(updateOrderStatus({ orderId, status: newStatus }));
            // Remove from selection if it was selected
            setSelectedOrders(prev => prev.filter(id => id !== orderId));
          }
        }
      ]
    );
  };

  // Bulk operations
  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => {
      const isSelected = prev.includes(orderId);
      const newSelection = isSelected
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId];

      setShowBulkActions(newSelection.length > 0);
      return newSelection;
    });
  };

  const handleSelectAll = () => {
    const allOrderIds = filteredAndSortedOrders.map(order => order.id);
    const allSelected = selectedOrders.length === allOrderIds.length;

    if (allSelected) {
      setSelectedOrders([]);
      setShowBulkActions(false);
    } else {
      setSelectedOrders(allOrderIds);
      setShowBulkActions(true);
    }
  };

  const handleBulkStatusUpdate = (newStatus: OrderStatus) => {
    Alert.alert(
      'Bulk Update',
      `Update ${selectedOrders.length} orders to ${getOrderStatusText(newStatus)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            selectedOrders.forEach(orderId => {
              dispatch(updateOrderStatus({ orderId, status: newStatus }));
            });
            setSelectedOrders([]);
            setShowBulkActions(false);
          }
        }
      ]
    );
  };

  const handleOrderPress = (order: Order) => {
    setSelectedOrderDetails(order);
    setShowOrderModal(true);
  };

  const handleCallCustomer = (phone: string) => {
    Alert.alert('Call Customer', `Call ${phone}?`, [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Call', onPress: () => {
        // In a real app, this would open the phone dialer
        Alert.alert('Feature', 'Phone dialer would open here');
      }}
    ]);
  };

  // This is now handled by filteredAndSortedOrders above

  const getStatusActions = (order: Order) => {
    switch (order.status) {
      case 'pending':
        return [
          { status: 'confirmed' as OrderStatus, label: 'Accept', color: Colors.light.success },
          { status: 'rejected' as OrderStatus, label: 'Reject', color: Colors.light.error }
        ];
      case 'confirmed':
        return [
          { status: 'preparing' as OrderStatus, label: 'Start Preparing', color: Colors.light.preparing }
        ];
      case 'preparing':
        return [
          { status: 'ready' as OrderStatus, label: 'Mark Ready', color: Colors.light.ready }
        ];
      default:
        return [];
    }
  };

  const renderOrderItem = ({ item }: { item: Order }) => {
    const isSelected = selectedOrders.includes(item.id);
    const orderTime = new Date(item.orderTime);
    const timeAgo = format(orderTime, 'HH:mm');
    const isToday = format(orderTime, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');

    return (
      <TouchableOpacity
        style={[styles.orderCard, isSelected && styles.selectedOrderCard]}
        onPress={() => handleOrderPress(item)}
        onLongPress={() => handleSelectOrder(item.id)}
      >
        {/* Selection Checkbox */}
        {showBulkActions && (
          <TouchableOpacity
            style={styles.selectionCheckbox}
            onPress={() => handleSelectOrder(item.id)}
          >
            <View style={[styles.checkbox, isSelected && styles.checkedBox]}>
              {isSelected && (
                <IconSymbol name="checkmark" size={16} color={Colors.light.textInverse} />
              )}
            </View>
          </TouchableOpacity>
        )}

        <View style={styles.orderHeader}>
          <View style={styles.orderHeaderLeft}>
            <View style={styles.orderNumberRow}>
              <Text style={styles.orderNumber}>#{item.orderNumber}</Text>
              {item.orderType === 'delivery' && (
                <View style={styles.deliveryBadge}>
                  <IconSymbol name="car.fill" size={12} color={Colors.light.primary} />
                  <Text style={styles.deliveryText}>Delivery</Text>
                </View>
              )}
            </View>
            <TouchableOpacity
              style={styles.customerRow}
              onPress={() => handleCallCustomer(item.customer.phone)}
            >
              <IconSymbol name="person.fill" size={16} color={Colors.light.textSecondary} />
              <Text style={styles.customerName}>{item.customer.name}</Text>
              <IconSymbol name="phone.fill" size={14} color={Colors.light.primary} />
            </TouchableOpacity>
            <View style={styles.timeRow}>
              <IconSymbol name="clock.fill" size={14} color={Colors.light.textTertiary} />
              <Text style={styles.orderTime}>
                {isToday ? timeAgo : format(orderTime, 'MMM dd, HH:mm')}
              </Text>
              {item.estimatedPreparationTime && (
                <Text style={styles.prepTime}>
                  • {item.estimatedPreparationTime}min prep
                </Text>
              )}
            </View>
          </View>
          <View style={styles.orderMeta}>
            <View style={[styles.statusBadge, { backgroundColor: getOrderStatusColor(item.status) }]}>
              <Text style={styles.statusText}>{getOrderStatusText(item.status)}</Text>
            </View>
            <Text style={styles.orderTotal}>PKR {item.total.toFixed(0)}</Text>
            <Text style={styles.itemCount}>
              {item.items.length} item{item.items.length !== 1 ? 's' : ''}
            </Text>
          </View>
        </View>

        {/* Order Items Preview */}
        <View style={styles.orderItemsPreview}>
          <Text style={styles.itemsTitle}>Items:</Text>
          <View style={styles.itemsList}>
            {item.items.slice(0, 2).map((orderItem, index) => (
              <Text key={index} style={styles.itemText}>
                {orderItem.quantity}x {orderItem.menuItem.name}
              </Text>
            ))}
            {item.items.length > 2 && (
              <Text style={styles.moreItems}>+{item.items.length - 2} more</Text>
            )}
          </View>
        </View>

        {/* Delivery Address */}
        {item.orderType === 'delivery' && item.deliveryAddress && (
          <View style={styles.addressContainer}>
            <IconSymbol name="location.fill" size={14} color={Colors.light.textSecondary} />
            <Text style={styles.address} numberOfLines={2}>
              {item.deliveryAddress.street}, {item.deliveryAddress.city}
            </Text>
          </View>
        )}

        {/* Special Instructions */}
        {item.specialInstructions && (
          <View style={styles.instructionsContainer}>
            <IconSymbol name="note.text" size={14} color={Colors.light.warning} />
            <Text style={styles.instructions}>{item.specialInstructions}</Text>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {getStatusActions(item).map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.actionButton, { backgroundColor: action.color }]}
              onPress={() => handleStatusUpdate(item.id, action.status)}
            >
              <Text style={styles.actionButtonText}>{action.label}</Text>
            </TouchableOpacity>
          ))}

          {/* Quick Actions */}
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => handleOrderPress(item)}
          >
            <IconSymbol name="eye.fill" size={16} color={Colors.light.primary} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const filterOptions = [
    { key: 'all', label: 'All Orders' },
    { key: 'pending', label: 'Pending' },
    { key: 'confirmed', label: 'Confirmed' },
    { key: 'preparing', label: 'Preparing' },
    { key: 'ready', label: 'Ready' }
  ];

  return (
    <View style={styles.container}>
      {/* Enhanced Header */}
      <View style={styles.headerContainer}>
        {/* Stats Row */}
        <View style={styles.statsRow}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{orderStats.todayOrders}</Text>
            <Text style={styles.statLabel}>Today</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{orderStats.pending}</Text>
            <Text style={styles.statLabel}>Pending</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{orderStats.preparing}</Text>
            <Text style={styles.statLabel}>Preparing</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>PKR {orderStats.totalRevenue.toFixed(0)}</Text>
            <Text style={styles.statLabel}>Revenue</Text>
          </View>
        </View>

        {/* Search and Controls */}
        <View style={styles.searchRow}>
          <View style={styles.searchContainer}>
            <IconSymbol name="magnifyingglass" size={20} color={Colors.light.textSecondary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search orders, customers..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={Colors.light.textTertiary}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <IconSymbol name="xmark.circle.fill" size={20} color={Colors.light.textSecondary} />
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setShowFilters(!showFilters)}
          >
            <IconSymbol name="line.3.horizontal.decrease.circle" size={24} color={Colors.light.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.refreshButton}
            onPress={handleRefresh}
          >
            <IconSymbol name="arrow.clockwise" size={24} color={Colors.light.primary} />
          </TouchableOpacity>
        </View>

        {/* Auto-refresh toggle */}
        <View style={styles.autoRefreshRow}>
          <Text style={styles.autoRefreshLabel}>Auto-refresh (30s)</Text>
          <Switch
            value={autoRefresh}
            onValueChange={setAutoRefresh}
            trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
            thumbColor={autoRefresh ? Colors.light.surface : Colors.light.textTertiary}
          />
        </View>
      </View>

      {/* Advanced Filters */}
      {showFilters && (
        <View style={styles.advancedFilters}>
          {/* Date Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>Date Range</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {[
                { key: 'all', label: 'All Time' },
                { key: 'today', label: 'Today' },
                { key: 'yesterday', label: 'Yesterday' },
                { key: 'week', label: 'This Week' }
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.filterChip,
                    dateFilter === option.key && styles.activeFilterChip
                  ]}
                  onPress={() => setDateFilter(option.key as any)}
                >
                  <Text style={[
                    styles.filterChipText,
                    dateFilter === option.key && styles.activeFilterChipText
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* Sort Options */}
          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>Sort By</Text>
            <View style={styles.sortControls}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {[
                  { key: 'time', label: 'Time' },
                  { key: 'total', label: 'Amount' },
                  { key: 'customer', label: 'Customer' }
                ].map((option) => (
                  <TouchableOpacity
                    key={option.key}
                    style={[
                      styles.filterChip,
                      sortBy === option.key && styles.activeFilterChip
                    ]}
                    onPress={() => setSortBy(option.key as any)}
                  >
                    <Text style={[
                      styles.filterChipText,
                      sortBy === option.key && styles.activeFilterChipText
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              <TouchableOpacity
                style={styles.sortOrderButton}
                onPress={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              >
                <IconSymbol
                  name={sortOrder === 'asc' ? 'arrow.up' : 'arrow.down'}
                  size={16}
                  color={Colors.light.primary}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Status Filter Tabs */}
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {filterOptions.map((item) => (
            <TouchableOpacity
              key={item.key}
              style={[
                styles.filterTab,
                selectedFilter === item.key && styles.activeFilterTab
              ]}
              onPress={() => setSelectedFilter(item.key as OrderStatus | 'all')}
            >
              <Text style={[
                styles.filterTabText,
                selectedFilter === item.key && styles.activeFilterTabText
              ]}>
                {item.label}
              </Text>
              {item.key !== 'all' && (
                <View style={styles.filterBadge}>
                  <Text style={styles.filterBadgeText}>
                    {orders.filter(o => o.status === item.key).length}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Bulk Actions Bar */}
        {showBulkActions && (
          <View style={styles.bulkActionsBar}>
            <TouchableOpacity onPress={handleSelectAll} style={styles.selectAllButton}>
              <Text style={styles.bulkActionText}>
                {selectedOrders.length === filteredAndSortedOrders.length ? 'Deselect All' : 'Select All'}
              </Text>
            </TouchableOpacity>
            <Text style={styles.selectedCount}>{selectedOrders.length} selected</Text>
            <View style={styles.bulkActions}>
              <TouchableOpacity
                style={[styles.bulkActionButton, { backgroundColor: Colors.light.success }]}
                onPress={() => handleBulkStatusUpdate('confirmed')}
              >
                <Text style={styles.bulkActionButtonText}>Accept</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.bulkActionButton, { backgroundColor: Colors.light.preparing }]}
                onPress={() => handleBulkStatusUpdate('preparing')}
              >
                <Text style={styles.bulkActionButtonText}>Prepare</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>

      {/* Orders List */}
      <FlatList
        data={filteredAndSortedOrders}
        keyExtractor={(item) => item.id}
        renderItem={renderOrderItem}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={handleRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <IconSymbol name="tray" size={64} color={Colors.light.textTertiary} />
            <Text style={styles.emptyText}>No orders found</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery ? `No orders match "${searchQuery}"` :
               selectedFilter === 'all'
                ? 'Orders will appear here when customers place them'
                : `No ${selectedFilter} orders at the moment`
              }
            </Text>
          </View>
        }
      />

      {/* Order Details Modal */}
      <Modal
        visible={showOrderModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowOrderModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Order Details</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowOrderModal(false)}
            >
              <IconSymbol name="xmark" size={24} color={Colors.light.text} />
            </TouchableOpacity>
          </View>

          {selectedOrderDetails && (
            <ScrollView style={styles.modalContent}>
              {/* Order Header */}
              <View style={styles.modalOrderHeader}>
                <View style={styles.modalOrderInfo}>
                  <Text style={styles.modalOrderNumber}>#{selectedOrderDetails.orderNumber}</Text>
                  <View style={[styles.modalStatusBadge, { backgroundColor: getOrderStatusColor(selectedOrderDetails.status) }]}>
                    <Text style={styles.modalStatusText}>{getOrderStatusText(selectedOrderDetails.status)}</Text>
                  </View>
                </View>
                <Text style={styles.modalOrderTotal}>PKR {selectedOrderDetails.total.toFixed(0)}</Text>
              </View>

              {/* Customer Information */}
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Customer Information</Text>
                <View style={styles.customerInfo}>
                  <View style={styles.customerRow}>
                    <IconSymbol name="person.fill" size={20} color={Colors.light.primary} />
                    <Text style={styles.customerInfoText}>{selectedOrderDetails.customer.name}</Text>
                  </View>
                  <TouchableOpacity
                    style={styles.customerRow}
                    onPress={() => handleCallCustomer(selectedOrderDetails.customer.phone)}
                  >
                    <IconSymbol name="phone.fill" size={20} color={Colors.light.primary} />
                    <Text style={styles.customerInfoText}>{selectedOrderDetails.customer.phone}</Text>
                  </TouchableOpacity>
                  <View style={styles.customerRow}>
                    <IconSymbol name="clock.fill" size={20} color={Colors.light.primary} />
                    <Text style={styles.customerInfoText}>
                      {format(new Date(selectedOrderDetails.orderTime), 'MMM dd, yyyy HH:mm')}
                    </Text>
                  </View>
                </View>
              </View>

              {/* Order Items */}
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Order Items</Text>
                {selectedOrderDetails.items.map((item, index) => (
                  <View key={index} style={styles.orderItemRow}>
                    <View style={styles.itemInfo}>
                      <Text style={styles.itemName}>{item.menuItem.name}</Text>
                      {item.customizations && item.customizations.length > 0 && (
                        <Text style={styles.itemCustomizations}>
                          {item.customizations.map(c => c.customizationName).join(', ')}
                        </Text>
                      )}
                    </View>
                    <View style={styles.itemPricing}>
                      <Text style={styles.itemQuantity}>x{item.quantity}</Text>
                      <Text style={styles.itemPrice}>PKR {item.itemTotal.toFixed(0)}</Text>
                    </View>
                  </View>
                ))}
              </View>

              {/* Delivery Information */}
              {selectedOrderDetails.orderType === 'delivery' && selectedOrderDetails.deliveryAddress && (
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Delivery Address</Text>
                  <View style={styles.addressInfo}>
                    <IconSymbol name="location.fill" size={20} color={Colors.light.primary} />
                    <View style={styles.addressText}>
                      <Text style={styles.addressLine}>{selectedOrderDetails.deliveryAddress.street}</Text>
                      <Text style={styles.addressLine}>
                        {selectedOrderDetails.deliveryAddress.city}, {selectedOrderDetails.deliveryAddress.state}
                      </Text>
                    </View>
                  </View>
                </View>
              )}

              {/* Special Instructions */}
              {selectedOrderDetails.specialInstructions && (
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Special Instructions</Text>
                  <View style={styles.instructionsBox}>
                    <IconSymbol name="note.text" size={20} color={Colors.light.warning} />
                    <Text style={styles.instructionsText}>{selectedOrderDetails.specialInstructions}</Text>
                  </View>
                </View>
              )}

              {/* Order Timeline */}
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Order Timeline</Text>
                <View style={styles.timeline}>
                  <View style={styles.timelineItem}>
                    <View style={[styles.timelineIcon, { backgroundColor: Colors.light.primary }]}>
                      <IconSymbol name="plus" size={12} color={Colors.light.textInverse} />
                    </View>
                    <View style={styles.timelineContent}>
                      <Text style={styles.timelineTitle}>Order Placed</Text>
                      <Text style={styles.timelineTime}>
                        {format(new Date(selectedOrderDetails.orderTime), 'MMM dd, HH:mm')}
                      </Text>
                    </View>
                  </View>
                  {/* Add more timeline items based on order status */}
                </View>
              </View>

              {/* Action Buttons */}
              <View style={styles.modalActions}>
                {getStatusActions(selectedOrderDetails).map((action, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.modalActionButton, { backgroundColor: action.color }]}
                    onPress={() => {
                      handleStatusUpdate(selectedOrderDetails.id, action.status);
                      setShowOrderModal(false);
                    }}
                  >
                    <Text style={styles.modalActionButtonText}>{action.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          )}
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA', // Light kitchen-themed background
  },

  // Enhanced Header Styles
  headerContainer: {
    backgroundColor: Colors.light.surface,
    paddingTop: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: Colors.light.text,
  },
  filterButton: {
    padding: 8,
  },
  refreshButton: {
    padding: 8,
  },
  autoRefreshRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 12,
  },
  autoRefreshLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },

  // Advanced Filters
  advancedFilters: {
    backgroundColor: Colors.light.surface,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  filterSection: {
    marginBottom: 12,
  },
  filterSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: Colors.light.backgroundSecondary,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  activeFilterChip: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  filterChipText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  activeFilterChipText: {
    color: Colors.light.textInverse,
  },
  sortControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortOrderButton: {
    padding: 8,
    marginLeft: 8,
  },

  // Filter Container
  filterContainer: {
    backgroundColor: Colors.light.surface,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  filterTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  activeFilterTab: {
    backgroundColor: Colors.light.primary,
  },
  filterTabText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: Colors.light.textInverse,
  },
  filterBadge: {
    backgroundColor: Colors.light.error,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 6,
    minWidth: 20,
    alignItems: 'center',
  },
  filterBadgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
  },

  // Bulk Actions
  bulkActionsBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 8,
    borderRadius: 8,
  },
  selectAllButton: {
    marginRight: 12,
  },
  bulkActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  selectedCount: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.textInverse,
    textAlign: 'center',
  },
  bulkActions: {
    flexDirection: 'row',
    gap: 8,
  },
  bulkActionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  bulkActionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  listContainer: {
    padding: 16,
  },
  orderCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)', // Semi-transparent white for kitchen theme
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
    borderWidth: 1,
    borderColor: 'rgba(220, 20, 60, 0.1)', // Subtle red border
  },
  selectedOrderCard: {
    borderWidth: 2,
    borderColor: Colors.light.primary,
  },
  selectionCheckbox: {
    position: 'absolute',
    top: 12,
    left: 12,
    zIndex: 1,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedBox: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
    marginLeft: 32, // Space for checkbox when visible
  },
  orderHeaderLeft: {
    flex: 1,
  },
  orderNumberRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  orderNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginRight: 8,
  },
  deliveryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    gap: 4,
  },
  deliveryText: {
    fontSize: 10,
    color: Colors.light.primary,
    fontWeight: '600',
  },
  customerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 6,
  },
  customerName: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    flex: 1,
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  orderTime: {
    fontSize: 12,
    color: Colors.light.textTertiary,
  },
  prepTime: {
    fontSize: 12,
    color: Colors.light.warning,
    fontWeight: '500',
  },
  orderMeta: {
    alignItems: 'flex-end',
    gap: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  orderTotal: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  itemCount: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },

  // Order Items Preview
  orderItemsPreview: {
    marginBottom: 12,
  },
  itemsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  itemsList: {
    gap: 2,
  },
  itemText: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  moreItems: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    fontStyle: 'italic',
  },

  // Address Container
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 8,
    borderRadius: 8,
    marginBottom: 12,
    gap: 6,
  },
  address: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    flex: 1,
    lineHeight: 16,
  },
  instructionsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 8,
    borderRadius: 8,
    marginBottom: 12,
    gap: 6,
  },
  instructions: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    flex: 1,
    lineHeight: 18,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  actionButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  quickActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.light.textTertiary,
    textAlign: 'center',
    paddingHorizontal: 32,
  },

  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    backgroundColor: Colors.light.surface,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  closeButton: {
    padding: 8,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalOrderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
    padding: 16,
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
  },
  modalOrderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  modalOrderNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  modalStatusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  modalStatusText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  modalOrderTotal: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  modalSection: {
    marginBottom: 24,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 12,
  },
  customerInfo: {
    gap: 12,
  },
  customerInfoText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    flex: 1,
  },
  orderItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  itemCustomizations: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
  },
  itemPricing: {
    alignItems: 'flex-end',
    gap: 4,
  },
  itemQuantity: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  addressInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  addressText: {
    flex: 1,
  },
  addressLine: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    lineHeight: 22,
  },
  instructionsBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 12,
    borderRadius: 8,
    gap: 12,
  },
  instructionsText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    flex: 1,
    lineHeight: 22,
  },
  timeline: {
    gap: 16,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timelineContent: {
    flex: 1,
  },
  timelineTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 2,
  },
  timelineTime: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    paddingTop: 16,
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  modalActionButton: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalActionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
});
