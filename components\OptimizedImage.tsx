import { Image } from 'expo-image';
import React, { memo, useState } from 'react';
import { ActivityIndicator, StyleSheet, View, ViewStyle } from 'react-native';

interface OptimizedImageProps {
  source: string | { uri: string } | number;
  style?: ViewStyle;
  placeholder?: string;
  contentFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
  transition?: number;
  cachePolicy?: 'memory' | 'disk' | 'memory-disk' | 'none';
  priority?: 'low' | 'normal' | 'high';
  lazy?: boolean;
  onLoad?: () => void;
  onError?: (error: any) => void;
  fallbackSource?: string | { uri: string } | number;
  blurhash?: string;
}

const OptimizedImage: React.FC<OptimizedImageProps> = memo(({
  source,
  style,
  placeholder,
  contentFit = 'cover',
  transition = 200,
  cachePolicy = 'memory-disk',
  priority = 'normal',
  lazy = true,
  onLoad,
  onError,
  fallbackSource,
  blurhash
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = (error: any) => {
    setIsLoading(false);
    setHasError(true);
    onError?.(error);
  };

  // Convert source to optimized format if it's a URI
  const getOptimizedSource = () => {
    if (typeof source === 'object' && 'uri' in source) {
      // Add WebP format preference and optimization parameters
      const uri = source.uri;
      if (uri.includes('http')) {
        // Add image optimization parameters for external URLs
        const separator = uri.includes('?') ? '&' : '?';
        return {
          uri: `${uri}${separator}format=webp&quality=80&auto=compress`
        };
      }
    }
    return source;
  };

  const imageSource = hasError && fallbackSource ? fallbackSource : getOptimizedSource();

  return (
    <View style={[styles.container, style]}>
      <Image
        source={imageSource}
        style={styles.image}
        contentFit={contentFit}
        transition={transition}
        cachePolicy={cachePolicy}
        priority={priority}
        placeholder={placeholder || blurhash}
        onLoad={handleLoad}
        onError={handleError}
        recyclingKey={typeof source === 'object' && 'uri' in source ? source.uri : undefined}
      />
      
      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#FF6B35" />
        </View>
      )}
    </View>
  );
});

OptimizedImage.displayName = 'OptimizedImage';

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
});

export default OptimizedImage;

// Hook for lazy loading images
export const useLazyImage = (threshold = 100) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);

  const onLayout = (event: any) => {
    const { y } = event.nativeEvent.layout;
    // Simple visibility check - in production, use Intersection Observer equivalent
    if (y < threshold && !hasLoaded) {
      setIsVisible(true);
      setHasLoaded(true);
    }
  };

  return { isVisible, onLayout };
};

// Optimized image cache manager
export class ImageCacheManager {
  private static cache = new Map<string, string>();
  private static maxCacheSize = 50; // Maximum number of cached images

  static async preloadImage(uri: string): Promise<void> {
    try {
      await Image.prefetch(uri);
      this.addToCache(uri);
    } catch (error) {
      console.warn('Failed to preload image:', uri, error);
    }
  }

  static preloadImages(uris: string[]): Promise<void[]> {
    return Promise.all(uris.map(uri => this.preloadImage(uri)));
  }

  private static addToCache(uri: string): void {
    if (this.cache.size >= this.maxCacheSize) {
      // Remove oldest entry
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(uri, uri);
  }

  static clearCache(): void {
    this.cache.clear();
    Image.clearMemoryCache();
    Image.clearDiskCache();
  }

  static getCacheSize(): number {
    return this.cache.size;
  }
}

// Image optimization utilities
export const ImageUtils = {
  // Generate blurhash placeholder for better UX
  generateBlurhash: (width: number, height: number): string => {
    // Simple blurhash generation - in production, generate server-side
    return 'LKO2?U%2Tw=w]~RBVZRi};RPxuwH';
  },

  // Get optimized image dimensions
  getOptimizedDimensions: (
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): { width: number; height: number } => {
    const aspectRatio = originalWidth / originalHeight;
    
    let width = originalWidth;
    let height = originalHeight;
    
    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }
    
    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }
    
    return { width: Math.round(width), height: Math.round(height) };
  },

  // Convert image format for optimization
  getOptimizedFormat: (originalFormat: string): string => {
    const supportedFormats = ['webp', 'avif', 'jpg', 'png'];
    
    // Prefer WebP for better compression
    if (supportedFormats.includes('webp')) {
      return 'webp';
    }
    
    return originalFormat;
  }
};
