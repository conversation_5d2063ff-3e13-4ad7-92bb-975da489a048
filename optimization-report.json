{"timestamp": "2025-07-14T07:40:24.230Z", "testResults": {"bundleAnalysis": {"metroConfig": true, "easConfig": true, "bundleAnalysisScript": true, "analysisResult": true}, "dependencyCheck": {}, "performanceTests": {"utils/performance.ts": true, "utils/memoryOptimization.ts": true, "utils/animationOptimization.ts": true, "utils/navigationOptimization.ts": true}, "memoryTests": {"utils/apiOptimization.ts": true, "hooks/useOptimizedAPI.ts": true, "utils/assetOptimization.ts": true, "redux": {"hasMemoryMiddleware": true, "hasBatchDispatcher": true}}, "buildTests": {"androidOptimized": true, "newArchEnabled": true, "appConfigExists": true, "scripts": [{"name": "analyze-bundle", "exists": true}, {"name": "build:android", "exists": true}, {"name": "build:ios", "exists": true}]}, "codeQuality": {"components/OptimizedImage.tsx": true, "components/OptimizedIcon.tsx": true, "components/OptimizedLoading.tsx": true, "components/OptimizedFlatList.tsx": true, "components/PerformanceWrapper.tsx": true, "typescript": {"strictMode": true, "pathMapping": true}}}, "summary": {"totalTests": 26, "passedTests": 26, "failedTests": 0, "successRate": "100.0"}}