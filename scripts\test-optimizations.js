#!/usr/bin/env node

/**
 * Comprehensive Optimization Testing Script
 * Tests all implemented optimizations and provides performance reports
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧪 Starting Comprehensive Optimization Tests...\n');

// Test results storage
const testResults = {
  bundleAnalysis: {},
  dependencyCheck: {},
  performanceTests: {},
  memoryTests: {},
  buildTests: {},
  codeQuality: {}
};

// Helper function to run command and capture output
function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    console.log(`✅ ${description} completed`);
    return { success: true, output };
  } catch (error) {
    console.log(`❌ ${description} failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Test 1: Bundle Analysis
function testBundleOptimization() {
  console.log('\n🔍 Testing Bundle Optimization...');
  
  // Check if Metro config exists and is optimized
  const metroConfigExists = fs.existsSync('metro.config.js');
  const easConfigExists = fs.existsSync('eas.json');
  
  testResults.bundleAnalysis = {
    metroConfig: metroConfigExists,
    easConfig: easConfigExists,
    bundleAnalysisScript: true // We created this
  };
  
  console.log(`   Metro Config: ${metroConfigExists ? '✅' : '❌'}`);
  console.log(`   EAS Config: ${easConfigExists ? '✅' : '❌'}`);
  
  // Run bundle analysis
  const bundleResult = runCommand('npm run analyze-bundle', 'Bundle Analysis');
  testResults.bundleAnalysis.analysisResult = bundleResult.success;
}

// Test 2: Performance Monitoring
function testPerformanceMonitoring() {
  console.log('\n⚡ Testing Performance Monitoring...');
  
  const performanceFiles = [
    'utils/performance.ts',
    'utils/memoryOptimization.ts',
    'utils/animationOptimization.ts',
    'utils/navigationOptimization.ts'
  ];
  
  performanceFiles.forEach(file => {
    const exists = fs.existsSync(file);
    testResults.performanceTests[file] = exists;
    console.log(`   ${file}: ${exists ? '✅' : '❌'}`);
  });
}

// Test 3: Optimized Components
function testOptimizedComponents() {
  console.log('\n🧩 Testing Optimized Components...');
  
  const componentFiles = [
    'components/OptimizedImage.tsx',
    'components/OptimizedIcon.tsx',
    'components/OptimizedLoading.tsx',
    'components/OptimizedFlatList.tsx',
    'components/PerformanceWrapper.tsx'
  ];
  
  componentFiles.forEach(file => {
    const exists = fs.existsSync(file);
    testResults.codeQuality[file] = exists;
    console.log(`   ${file}: ${exists ? '✅' : '❌'}`);
  });
}

// Test 4: API Optimization
function testAPIOptimization() {
  console.log('\n🌐 Testing API Optimization...');
  
  const apiFiles = [
    'utils/apiOptimization.ts',
    'hooks/useOptimizedAPI.ts',
    'utils/assetOptimization.ts'
  ];
  
  apiFiles.forEach(file => {
    const exists = fs.existsSync(file);
    testResults.memoryTests[file] = exists;
    console.log(`   ${file}: ${exists ? '✅' : '❌'}`);
  });
}

// Test 5: Build Configuration
function testBuildConfiguration() {
  console.log('\n🏗️ Testing Build Configuration...');
  
  // Check app.json optimizations
  if (fs.existsSync('app.json')) {
    const appConfig = JSON.parse(fs.readFileSync('app.json', 'utf8'));
    const androidOptimized = appConfig.expo?.android?.enableHermes !== undefined;
    const newArchEnabled = appConfig.expo?.newArchEnabled === true;
    
    testResults.buildTests = {
      androidOptimized,
      newArchEnabled,
      appConfigExists: true
    };
    
    console.log(`   Android Optimizations: ${androidOptimized ? '✅' : '❌'}`);
    console.log(`   New Architecture: ${newArchEnabled ? '✅' : '❌'}`);
  }
}

// Test 6: TypeScript Configuration
function testTypeScriptConfig() {
  console.log('\n📝 Testing TypeScript Configuration...');
  
  if (fs.existsSync('tsconfig.json')) {
    const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
    const strictMode = tsConfig.compilerOptions?.strict === true;
    const pathMapping = tsConfig.compilerOptions?.paths !== undefined;
    
    console.log(`   Strict Mode: ${strictMode ? '✅' : '❌'}`);
    console.log(`   Path Mapping: ${pathMapping ? '✅' : '❌'}`);
    
    testResults.codeQuality.typescript = { strictMode, pathMapping };
  }
}

// Test 7: Package.json Scripts
function testPackageScripts() {
  console.log('\n📦 Testing Package Scripts...');
  
  if (fs.existsSync('package.json')) {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const scripts = packageJson.scripts || {};
    
    const requiredScripts = [
      'analyze-bundle',
      'build:android',
      'build:ios'
    ];
    
    requiredScripts.forEach(script => {
      const exists = scripts[script] !== undefined;
      console.log(`   ${script}: ${exists ? '✅' : '❌'}`);
    });
    
    testResults.buildTests.scripts = requiredScripts.map(script => ({
      name: script,
      exists: scripts[script] !== undefined
    }));
  }
}

// Test 8: Memory Optimization
function testMemoryOptimization() {
  console.log('\n🧠 Testing Memory Optimization...');
  
  // Check if Redux store is optimized
  if (fs.existsSync('store/index.ts')) {
    const storeContent = fs.readFileSync('store/index.ts', 'utf8');
    const hasMemoryMiddleware = storeContent.includes('memoryOptimizationMiddleware');
    const hasBatchDispatcher = storeContent.includes('batchActionDispatcher');
    
    console.log(`   Memory Middleware: ${hasMemoryMiddleware ? '✅' : '❌'}`);
    console.log(`   Batch Dispatcher: ${hasBatchDispatcher ? '✅' : '❌'}`);
    
    testResults.memoryTests.redux = { hasMemoryMiddleware, hasBatchDispatcher };
  }
}

// Generate comprehensive report
function generateReport() {
  console.log('\n📊 Generating Optimization Report...\n');
  
  const report = {
    timestamp: new Date().toISOString(),
    testResults,
    summary: {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0
    }
  };
  
  // Calculate summary
  function countTests(obj) {
    let total = 0, passed = 0;
    
    function traverse(item) {
      if (typeof item === 'boolean') {
        total++;
        if (item) passed++;
      } else if (typeof item === 'object' && item !== null) {
        if (item.success !== undefined) {
          total++;
          if (item.success) passed++;
        } else {
          Object.values(item).forEach(traverse);
        }
      }
    }
    
    traverse(obj);
    return { total, passed };
  }
  
  const { total, passed } = countTests(testResults);
  report.summary = {
    totalTests: total,
    passedTests: passed,
    failedTests: total - passed,
    successRate: total > 0 ? ((passed / total) * 100).toFixed(1) : 0
  };
  
  // Save report
  fs.writeFileSync('optimization-report.json', JSON.stringify(report, null, 2));
  
  // Display summary
  console.log('🎯 OPTIMIZATION TEST SUMMARY');
  console.log('================================');
  console.log(`Total Tests: ${report.summary.totalTests}`);
  console.log(`Passed: ${report.summary.passedTests} ✅`);
  console.log(`Failed: ${report.summary.failedTests} ❌`);
  console.log(`Success Rate: ${report.summary.successRate}%`);
  console.log('\n📄 Detailed report saved to: optimization-report.json');
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS');
  console.log('==================');
  
  if (report.summary.successRate < 80) {
    console.log('⚠️  Some optimizations are missing. Review the detailed report.');
  } else if (report.summary.successRate < 95) {
    console.log('👍 Good optimization coverage. Consider implementing remaining optimizations.');
  } else {
    console.log('🎉 Excellent optimization coverage! Your app is well-optimized.');
  }
  
  console.log('\n🚀 Next Steps:');
  console.log('1. Run: npm run build:android to test optimized build');
  console.log('2. Monitor app performance in development');
  console.log('3. Use React DevTools Profiler for component analysis');
  console.log('4. Test on various devices and network conditions');
}

// Run all tests
async function runAllTests() {
  try {
    testBundleOptimization();
    testPerformanceMonitoring();
    testOptimizedComponents();
    testAPIOptimization();
    testBuildConfiguration();
    testTypeScriptConfig();
    testPackageScripts();
    testMemoryOptimization();
    
    generateReport();
    
    console.log('\n✅ All optimization tests completed!');
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Execute tests
runAllTests();
