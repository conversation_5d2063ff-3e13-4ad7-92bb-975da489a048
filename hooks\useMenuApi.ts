import { useApi, useApiMutation } from './useApi';
import { menuService, MenuCategory, MenuItem, CreateMenuItemData } from '@/services';

// Category hooks
export function useMenuCategories() {
  return useApi(() => menuService.getCategories());
}

export function useCreateCategory() {
  return useApiMutation((data: Omit<MenuCategory, 'id' | 'restaurantId' | 'createdAt' | 'updatedAt'>) => 
    menuService.createCategory(data)
  );
}

export function useUpdateCategory() {
  return useApiMutation(({ id, data }: { id: string; data: Partial<MenuCategory> }) => 
    menuService.updateCategory(id, data)
  );
}

export function useDeleteCategory() {
  return useApiMutation((id: string) => 
    menuService.deleteCategory(id)
  );
}

// Menu item hooks
export function useMenuItems(categoryId?: string) {
  return useApi(() => menuService.getMenuItems(categoryId), [categoryId]);
}

export function useMenuItem(id: string) {
  return useApi(() => menuService.getMenuItem(id), [id]);
}

export function useCreateMenuItem() {
  return useApiMutation((data: CreateMenuItemData) => 
    menuService.createMenuItem(data)
  );
}

export function useUpdateMenuItem() {
  return useApiMutation(({ id, data }: { id: string; data: Partial<CreateMenuItemData> }) => 
    menuService.updateMenuItem(id, data)
  );
}

export function useDeleteMenuItem() {
  return useApiMutation((id: string) => 
    menuService.deleteMenuItem(id)
  );
}

export function useToggleItemAvailability() {
  return useApiMutation(({ id, isAvailable }: { id: string; isAvailable: boolean }) => 
    menuService.toggleItemAvailability(id, isAvailable)
  );
}

// Image management hooks
export function useUploadItemImage() {
  return useApiMutation(({ itemId, imageUri }: { itemId: string; imageUri: string }) => 
    menuService.uploadItemImage(itemId, imageUri)
  );
}

export function useDeleteItemImage() {
  return useApiMutation(({ itemId, imageUrl }: { itemId: string; imageUrl: string }) => 
    menuService.deleteItemImage(itemId, imageUrl)
  );
}

// Bulk operations hooks
export function useUpdateItemsOrder() {
  return useApiMutation((items: Array<{ id: string; displayOrder: number }>) => 
    menuService.updateItemsOrder(items)
  );
}

export function useUpdateCategoriesOrder() {
  return useApiMutation((categories: Array<{ id: string; displayOrder: number }>) => 
    menuService.updateCategoriesOrder(categories)
  );
}

// Search hook
export function useSearchMenuItems() {
  return useApiMutation((query: string) => 
    menuService.searchMenuItems(query)
  );
}
