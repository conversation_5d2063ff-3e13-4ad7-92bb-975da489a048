import { Tabs } from 'expo-router';
import React from 'react';

import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { usePermissions } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const { canAccess } = usePermissions();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#F8F9FA', // White for selected tab text
        tabBarInactiveTintColor: '#F8F9FA', // Dark white for unselected tabs
        headerShown: false,
        tabBarButton: CustomTabButton,
        tabBarBackground: TabBarBackground,
        tabBarStyle: {
          backgroundColor: '#DC143C', // Red background
          borderTopColor: '#B91C1C',
          borderTopWidth: 1,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          height: 70, // Taller for pop-up effect
          paddingBottom: 5,
        },
        tabBarLabelStyle: {
          fontSize: 11,
          fontWeight: '700',
          marginBottom: 2,
        },
        tabBarIconStyle: {
          marginBottom: 2,
        },

      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Dashboard',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="chart-bar" color={color} />,
          href: canAccess('dashboard') ? '/' : null,
        }}
      />
      <Tabs.Screen
        name="orders"
        options={{
          title: 'Orders',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="clipboard-list" color={color} />,
          href: canAccess('orders') ? '/orders' : null,
        }}
      />
      <Tabs.Screen
        name="menu"
        options={{
          title: 'Menu',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="book-open" color={color} />,
          href: canAccess('menu') ? '/menu' : null,
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="account" color={color} />,
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="cog" color={color} />,
          href: canAccess('settings') ? '/settings' : null,
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          href: null, // Hide this tab
        }}
      />
      <Tabs.Screen
        name="add-menu-item"
        options={{
          href: null, // Hide this tab - accessed via navigation
          title: 'Add Menu Item',
        }}
      />
      <Tabs.Screen
        name="analytics"
        options={{
          href: null, // Hide this tab - accessed only through dashboard
          title: 'Analytics',
        }}
      />

    </Tabs>
  );
}

