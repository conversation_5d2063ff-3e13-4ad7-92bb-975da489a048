import { Tabs } from 'expo-router';
import React from 'react';

import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { usePermissions } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';

// Custom tab button with pop-up effect for selected state
function CustomTabButton({ children, onPress, accessibilityState, ...props }: any) {
  const focused = accessibilityState?.selected;

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.tabButton,
        focused && styles.tabButtonFocused
      ]}
      {...props}
    >
      <View style={[
        styles.tabContent,
        focused && styles.tabContentFocused
      ]}>
        {children}
      </View>
    </TouchableOpacity>
  );
}

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const { canAccess } = usePermissions();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#FF6B35', // Red-orange for selected tab
        tabBarInactiveTintColor: '#F8F9FA', // Dark white for unselected tabs
        headerShown: false,
        tabBarButton: CustomTabButton,
        tabBarBackground: TabBarBackground,
        tabBarStyle: {
          backgroundColor: '#DC143C', // Red background
          borderTopColor: '#B91C1C',
          borderTopWidth: 1,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          height: 65, // Slightly taller for pop-up effect
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginBottom: 4,
        },
        tabBarItemStyle: {
          paddingVertical: 4,
        },
        tabBarIconStyle: {
          marginTop: 2,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Dashboard',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="chart-bar" color={color} />,
          href: canAccess('dashboard') ? '/' : null,
        }}
      />
      <Tabs.Screen
        name="orders"
        options={{
          title: 'Orders',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="clipboard-list" color={color} />,
          href: canAccess('orders') ? '/orders' : null,
        }}
      />
      <Tabs.Screen
        name="menu"
        options={{
          title: 'Menu',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="book-open" color={color} />,
          href: canAccess('menu') ? '/menu' : null,
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="account" color={color} />,
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="cog" color={color} />,
          href: canAccess('settings') ? '/settings' : null,
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          href: null, // Hide this tab
        }}
      />
      <Tabs.Screen
        name="add-menu-item"
        options={{
          href: null, // Hide this tab - accessed via navigation
          title: 'Add Menu Item',
        }}
      />
      <Tabs.Screen
        name="analytics"
        options={{
          href: null, // Hide this tab - accessed only through dashboard
          title: 'Analytics',
        }}
      />

    </Tabs>
  );
}

const styles = StyleSheet.create({
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  tabButtonFocused: {
    transform: [{ translateY: -3 }], // Pop up effect
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 50,
  },
  tabContentFocused: {
    backgroundColor: 'rgba(255, 107, 53, 0.2)', // Light red-orange background
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
    transform: [{ scale: 1.1 }], // Slightly larger when selected
  },
});
