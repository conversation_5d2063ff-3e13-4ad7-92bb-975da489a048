import api, { ApiResponse, PaginatedResponse, handleApiError } from './api';

// Staff types
export interface StaffMember {
  id: string;
  username: string;
  email: string;
  name: string;
  role: 'admin' | 'staff';
  phone?: string;
  isActive: boolean;
  permissions: string[];
  restaurantId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
}

export interface CreateStaffData {
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'staff';
  permissions?: string[];
}

export interface StaffCredentials {
  username: string;
  password: string;
  temporaryPassword: boolean;
}

export interface StaffStats {
  totalStaff: number;
  activeStaff: number;
  inactiveStaff: number;
  adminCount: number;
  staffCount: number;
}

export interface StaffActivity {
  id: string;
  staffId: string;
  staffName: string;
  action: string;
  details: string;
  timestamp: string;
}

class StaffService {
  // Get all staff members
  async getStaffMembers(): Promise<ApiResponse<StaffMember[]>> {
    try {
      const response = await api.get<ApiResponse<StaffMember[]>>('/staff');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get single staff member
  async getStaffMember(id: string): Promise<ApiResponse<StaffMember>> {
    try {
      const response = await api.get<ApiResponse<StaffMember>>(`/staff/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Create new staff member
  async createStaffMember(data: CreateStaffData): Promise<ApiResponse<{
    staff: StaffMember;
    credentials: StaffCredentials;
  }>> {
    try {
      const response = await api.post<ApiResponse<{
        staff: StaffMember;
        credentials: StaffCredentials;
      }>>('/staff', data);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update staff member
  async updateStaffMember(id: string, data: Partial<CreateStaffData>): Promise<ApiResponse<StaffMember>> {
    try {
      const response = await api.put<ApiResponse<StaffMember>>(`/staff/${id}`, data);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Delete staff member
  async deleteStaffMember(id: string): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.delete<ApiResponse<{ message: string }>>(`/staff/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Toggle staff member active status
  async toggleStaffStatus(id: string, isActive: boolean): Promise<ApiResponse<StaffMember>> {
    try {
      const response = await api.patch<ApiResponse<StaffMember>>(`/staff/${id}/status`, { isActive });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Reset staff password
  async resetStaffPassword(id: string): Promise<ApiResponse<StaffCredentials>> {
    try {
      const response = await api.post<ApiResponse<StaffCredentials>>(`/staff/${id}/reset-password`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update staff permissions
  async updateStaffPermissions(id: string, permissions: string[]): Promise<ApiResponse<StaffMember>> {
    try {
      const response = await api.patch<ApiResponse<StaffMember>>(`/staff/${id}/permissions`, { permissions });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get staff statistics
  async getStaffStats(): Promise<ApiResponse<StaffStats>> {
    try {
      const response = await api.get<ApiResponse<StaffStats>>('/staff/stats');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get staff activity log
  async getStaffActivity(
    page: number = 1,
    limit: number = 20,
    staffId?: string
  ): Promise<PaginatedResponse<StaffActivity>> {
    try {
      const params = { page, limit };
      if (staffId) {
        (params as any).staffId = staffId;
      }
      
      const response = await api.get<PaginatedResponse<StaffActivity>>('/staff/activity', { params });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get available permissions
  async getAvailablePermissions(): Promise<ApiResponse<Array<{
    id: string;
    name: string;
    description: string;
    category: string;
  }>>> {
    try {
      const response = await api.get<ApiResponse<Array<{
        id: string;
        name: string;
        description: string;
        category: string;
      }>>>('/staff/permissions');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Bulk operations
  async bulkUpdateStaffStatus(staffIds: string[], isActive: boolean): Promise<ApiResponse<{ updated: number }>> {
    try {
      const response = await api.patch<ApiResponse<{ updated: number }>>('/staff/bulk-status', {
        staffIds,
        isActive
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async bulkDeleteStaff(staffIds: string[]): Promise<ApiResponse<{ deleted: number }>> {
    try {
      const response = await api.delete<ApiResponse<{ deleted: number }>>('/staff/bulk-delete', {
        data: { staffIds }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Search staff members
  async searchStaff(query: string): Promise<ApiResponse<StaffMember[]>> {
    try {
      const response = await api.get<ApiResponse<StaffMember[]>>('/staff/search', {
        params: { q: query }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get staff performance metrics
  async getStaffPerformance(
    staffId: string,
    period: 'week' | 'month' | 'quarter' = 'month'
  ): Promise<ApiResponse<{
    ordersProcessed: number;
    averageProcessingTime: number;
    customerRating: number;
    totalWorkingHours: number;
    efficiency: number;
  }>> {
    try {
      const response = await api.get<ApiResponse<{
        ordersProcessed: number;
        averageProcessingTime: number;
        customerRating: number;
        totalWorkingHours: number;
        efficiency: number;
      }>>(`/staff/${staffId}/performance`, {
        params: { period }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export default new StaffService();
