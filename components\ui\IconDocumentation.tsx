import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Clipboard } from 'react-native';
import { AppIcon, RestaurantIcons, IconSizes, IconColors } from './AppIcon';
import { PremiumColors } from '../../constants/PremiumTheme';

export const IconDocumentation: React.FC = () => {
  const [copiedCode, setCopiedCode] = useState<string>('');

  const copyToClipboard = (code: string) => {
    Clipboard.setString(code);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(''), 2000);
  };

  const CodeBlock: React.FC<{ code: string; title?: string }> = ({ code, title }) => (
    <View style={styles.codeBlock}>
      {title && <Text style={styles.codeTitle}>{title}</Text>}
      <TouchableOpacity onPress={() => copyToClipboard(code)} style={styles.codeContainer}>
        <Text style={styles.codeText}>{code}</Text>
        <View style={styles.copyIndicator}>
          <AppIcon 
            name={copiedCode === code ? "check" : "content-copy"} 
            size="sm" 
            color={copiedCode === code ? "success" : "gray"} 
          />
        </View>
      </TouchableOpacity>
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <AppIcon name="book-open-variant" size="xl" color="white" />
        <Text style={styles.title}>Icon System Documentation</Text>
        <Text style={styles.subtitle}>Complete guide for developers</Text>
      </View>

      {/* Quick Start */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Start</Text>
        
        <Text style={styles.description}>
          The AppIcon component provides a unified interface for all restaurant app icons with consistent sizing, theming, and performance optimization.
        </Text>

        <CodeBlock 
          title="Basic Import"
          code={`import { AppIcon, RestaurantIcons } from '@/components/ui/AppIcon';`}
        />

        <CodeBlock 
          title="Basic Usage"
          code={`<AppIcon name="store" size="md" color="primary" />`}
        />

        <CodeBlock 
          title="Using Restaurant Icons"
          code={`<AppIcon name={RestaurantIcons.auth.restaurant} size="lg" color="primary" />`}
        />
      </View>

      {/* Props Documentation */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Props</Text>
        
        <View style={styles.propTable}>
          <View style={styles.propRow}>
            <Text style={styles.propName}>name</Text>
            <Text style={styles.propType}>string</Text>
            <Text style={styles.propDescription}>Icon name from MaterialCommunityIcons</Text>
          </View>
          
          <View style={styles.propRow}>
            <Text style={styles.propName}>size</Text>
            <Text style={styles.propType}>IconSize | number</Text>
            <Text style={styles.propDescription}>Icon size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' or custom number</Text>
          </View>
          
          <View style={styles.propRow}>
            <Text style={styles.propName}>color</Text>
            <Text style={styles.propType}>IconColor | string</Text>
            <Text style={styles.propDescription}>Theme color or custom hex/rgb value</Text>
          </View>
          
          <View style={styles.propRow}>
            <Text style={styles.propName}>library</Text>
            <Text style={styles.propType}>IconLibrary</Text>
            <Text style={styles.propDescription}>Icon library: 'MaterialCommunityIcons' (default) | 'MaterialIcons' | 'FontAwesome' | 'Ionicons'</Text>
          </View>
          
          <View style={styles.propRow}>
            <Text style={styles.propName}>style</Text>
            <Text style={styles.propType}>ViewStyle</Text>
            <Text style={styles.propDescription}>Additional styling for the icon container</Text>
          </View>
        </View>
      </View>

      {/* Size System */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Size System</Text>
        
        <View style={styles.sizeDemo}>
          {Object.entries(IconSizes).map(([sizeName, sizeValue]) => (
            <View key={sizeName} style={styles.sizeDemoItem}>
              <AppIcon name="star" size={sizeName as keyof typeof IconSizes} color="primary" />
              <Text style={styles.sizeDemoText}>{sizeName}</Text>
              <Text style={styles.sizeDemoValue}>{sizeValue}px</Text>
            </View>
          ))}
        </View>

        <CodeBlock 
          title="Size Usage Examples"
          code={`// Predefined sizes
<AppIcon name="star" size="xs" />   // 12px
<AppIcon name="star" size="sm" />   // 16px
<AppIcon name="star" size="md" />   // 24px
<AppIcon name="star" size="lg" />   // 32px
<AppIcon name="star" size="xl" />   // 48px
<AppIcon name="star" size="xxl" />  // 64px

// Custom size
<AppIcon name="star" size={28} />`}
        />
      </View>

      {/* Color System */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Color System</Text>
        
        <View style={styles.colorDemo}>
          {Object.entries(IconColors).map(([colorName, colorValue]) => (
            <View key={colorName} style={styles.colorDemoItem}>
              <AppIcon name="circle" size="lg" color={colorName as keyof typeof IconColors} />
              <Text style={styles.colorDemoText}>{colorName}</Text>
              <Text style={styles.colorDemoValue}>{colorValue}</Text>
            </View>
          ))}
        </View>

        <CodeBlock 
          title="Color Usage Examples"
          code={`// Theme colors
<AppIcon name="heart" color="primary" />
<AppIcon name="heart" color="success" />
<AppIcon name="heart" color="warning" />
<AppIcon name="heart" color="danger" />

// Custom colors
<AppIcon name="heart" color="#FF6B35" />
<AppIcon name="heart" color="rgb(255, 107, 53)" />`}
        />
      </View>

      {/* Restaurant Icon Categories */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Restaurant Icon Categories</Text>
        
        {Object.entries(RestaurantIcons).map(([categoryName, categoryIcons]) => (
          <View key={categoryName} style={styles.categorySection}>
            <Text style={styles.categoryTitle}>
              {categoryName.charAt(0).toUpperCase() + categoryName.slice(1)}
            </Text>
            
            <View style={styles.categoryGrid}>
              {Object.entries(categoryIcons).slice(0, 8).map(([iconKey, iconName]) => (
                <View key={iconKey} style={styles.categoryIconItem}>
                  <AppIcon name={iconName} size="md" color="primary" />
                  <Text style={styles.categoryIconText}>{iconKey}</Text>
                </View>
              ))}
              {Object.keys(categoryIcons).length > 8 && (
                <Text style={styles.categoryMore}>
                  +{Object.keys(categoryIcons).length - 8} more
                </Text>
              )}
            </View>

            <CodeBlock 
              code={`// ${categoryName.charAt(0).toUpperCase() + categoryName.slice(1)} icons
<AppIcon name={RestaurantIcons.${categoryName}.${Object.keys(categoryIcons)[0]}} size="md" color="primary" />
<AppIcon name={RestaurantIcons.${categoryName}.${Object.keys(categoryIcons)[1] || Object.keys(categoryIcons)[0]}} size="md" color="primary" />`}
            />
          </View>
        ))}
      </View>

      {/* Best Practices */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Best Practices</Text>
        
        <View style={styles.practiceList}>
          <View style={styles.practiceItem}>
            <AppIcon name="check-circle" size="sm" color="success" />
            <View style={styles.practiceContent}>
              <Text style={styles.practiceTitle}>Use Semantic Names</Text>
              <Text style={styles.practiceDescription}>
                Use RestaurantIcons mappings for better code readability and maintenance
              </Text>
            </View>
          </View>

          <View style={styles.practiceItem}>
            <AppIcon name="check-circle" size="sm" color="success" />
            <View style={styles.practiceContent}>
              <Text style={styles.practiceTitle}>Consistent Sizing</Text>
              <Text style={styles.practiceDescription}>
                Use predefined size tokens (xs, sm, md, lg, xl, xxl) for consistency
              </Text>
            </View>
          </View>

          <View style={styles.practiceItem}>
            <AppIcon name="check-circle" size="sm" color="success" />
            <View style={styles.practiceContent}>
              <Text style={styles.practiceTitle}>Theme Colors</Text>
              <Text style={styles.practiceDescription}>
                Prefer theme colors over custom hex values for better maintainability
              </Text>
            </View>
          </View>

          <View style={styles.practiceItem}>
            <AppIcon name="check-circle" size="sm" color="success" />
            <View style={styles.practiceContent}>
              <Text style={styles.practiceTitle}>Performance</Text>
              <Text style={styles.practiceDescription}>
                Vector icons scale perfectly and perform better than bitmap images
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Migration Guide */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Migration from IconSymbol</Text>
        
        <Text style={styles.description}>
          The IconSymbol component has been updated to use MaterialCommunityIcons with automatic SF Symbol mapping for backward compatibility.
        </Text>

        <CodeBlock 
          title="Old IconSymbol Usage (Still Works)"
          code={`<IconSymbol name="star.fill" size={24} color="#FF6B35" />`}
        />

        <CodeBlock 
          title="New AppIcon Usage (Recommended)"
          code={`<AppIcon name="star" size="md" color="primary" />`}
        />
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Icon System v1.0 • {Object.values(RestaurantIcons).reduce((acc, category) => acc + Object.keys(category).length, 0)} Total Icons
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PremiumColors.white,
  },
  header: {
    padding: 24,
    backgroundColor: PremiumColors.primary,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: PremiumColors.white,
    marginTop: 8,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: PremiumColors.white,
    opacity: 0.9,
  },
  section: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    color: PremiumColors.grayDark,
    lineHeight: 20,
    marginBottom: 16,
  },
  codeBlock: {
    marginBottom: 16,
  },
  codeTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: PremiumColors.grayDark,
    marginBottom: 4,
  },
  codeContainer: {
    backgroundColor: PremiumColors.black,
    padding: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  codeText: {
    fontFamily: 'monospace',
    fontSize: 12,
    color: PremiumColors.white,
    flex: 1,
    lineHeight: 16,
  },
  copyIndicator: {
    marginLeft: 8,
  },
  propTable: {
    backgroundColor: PremiumColors.grayLight,
    borderRadius: 8,
    overflow: 'hidden',
  },
  propRow: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: PremiumColors.white,
  },
  propName: {
    fontSize: 14,
    fontWeight: '600',
    color: PremiumColors.black,
    marginBottom: 2,
  },
  propType: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: PremiumColors.primary,
    marginBottom: 4,
  },
  propDescription: {
    fontSize: 12,
    color: PremiumColors.grayDark,
    lineHeight: 16,
  },
  sizeDemo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 16,
  },
  sizeDemoItem: {
    alignItems: 'center',
    minWidth: 60,
  },
  sizeDemoText: {
    fontSize: 12,
    fontWeight: '600',
    color: PremiumColors.black,
    marginTop: 4,
  },
  sizeDemoValue: {
    fontSize: 10,
    color: PremiumColors.grayDark,
  },
  colorDemo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  colorDemoItem: {
    alignItems: 'center',
    minWidth: 80,
  },
  colorDemoText: {
    fontSize: 12,
    fontWeight: '600',
    color: PremiumColors.black,
    marginTop: 4,
  },
  colorDemoValue: {
    fontSize: 10,
    color: PremiumColors.grayDark,
    textAlign: 'center',
  },
  categorySection: {
    marginBottom: 24,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: PremiumColors.black,
    marginBottom: 8,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 12,
  },
  categoryIconItem: {
    alignItems: 'center',
    width: 60,
  },
  categoryIconText: {
    fontSize: 10,
    color: PremiumColors.grayDark,
    textAlign: 'center',
    marginTop: 4,
  },
  categoryMore: {
    fontSize: 12,
    color: PremiumColors.grayDark,
    fontStyle: 'italic',
    alignSelf: 'center',
  },
  practiceList: {
    gap: 16,
  },
  practiceItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  practiceContent: {
    flex: 1,
  },
  practiceTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: PremiumColors.black,
    marginBottom: 4,
  },
  practiceDescription: {
    fontSize: 12,
    color: PremiumColors.grayDark,
    lineHeight: 16,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: PremiumColors.grayLight,
    marginTop: 20,
  },
  footerText: {
    fontSize: 12,
    color: PremiumColors.grayDark,
  },
});

export default IconDocumentation;
