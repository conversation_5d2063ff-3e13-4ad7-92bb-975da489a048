import AsyncStorage from '@react-native-async-storage/async-storage';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Alert } from 'react-native';
import { FormActions, FormState, FormStep, FormValidationError, MenuItem } from '../types';
import { generateAIDescription, optimizeForSEO } from '../utils/aiHelpers';
import { saveMenuItemDraft, submitMenuItem } from '../utils/api';
import { validateField, validateMenuItem } from '../utils/validation';

const AUTOSAVE_INTERVAL = 30000; // 30 seconds
const DRAFT_STORAGE_KEY = 'menuItemDraft';

const initialSteps: FormStep[] = [
  {
    id: 'basic',
    title: 'Basic Information',
    description: 'Name, description, and category',
    icon: 'info.circle',
    isCompleted: false,
    isValid: false,
    errors: [],
    progress: 0,
  },
  {
    id: 'pricing',
    title: 'Pricing & Availability',
    description: 'Price tiers and schedules',
    icon: 'cash',
    isCompleted: false,
    isValid: false,
    errors: [],
    progress: 0,
  },
  {
    id: 'media',
    title: 'Media Gallery',
    description: 'Images and videos',
    icon: 'camera',
    isCompleted: false,
    isValid: false,
    errors: [],
    progress: 0,
  },

  {
    id: 'customizations',
    title: 'Extra Addons',
    description: 'Modifications and add-ons',
    icon: 'options',
    isCompleted: false,
    isValid: false,
    errors: [],
    progress: 0,
  },
  {
    id: 'marketing',
    title: 'Marketing',
    description: 'Tags, SEO, and promotions',
    icon: 'bullhorn',
    isCompleted: false,
    isValid: false,
    errors: [],
    progress: 0,
  },
  {
    id: 'review',
    title: 'Review & Publish',
    description: 'Preview and final checks',
    icon: 'checkmark-circle',
    isCompleted: false,
    isValid: false,
    errors: [],
    progress: 0,
  },
];

export function useMenuItemForm(initialData?: Partial<MenuItem>): [FormState, FormActions] {
  const [state, setState] = useState<FormState>({
    currentStep: 0,
    steps: initialSteps,
    data: initialData || {},
    isDirty: false,
    isSubmitting: false,
    lastSaved: null,
    validationErrors: [],
    analytics: null,
  });

  const autosaveTimer = useRef<NodeJS.Timeout>();

  // Auto-save functionality
  useEffect(() => {
    if (state.isDirty && !state.isSubmitting) {
      if (autosaveTimer.current) {
        clearTimeout(autosaveTimer.current);
      }
      
      autosaveTimer.current = setTimeout(() => {
        saveDraft();
      }, AUTOSAVE_INTERVAL);
    }

    return () => {
      if (autosaveTimer.current) {
        clearTimeout(autosaveTimer.current);
      }
    };
  }, [state.isDirty, state.data]);

  // Load draft on mount
  useEffect(() => {
    loadDraftFromStorage();
  }, []);

  const loadDraftFromStorage = async () => {
    try {
      const draftData = await AsyncStorage.getItem(DRAFT_STORAGE_KEY);
      if (draftData && !initialData) {
        const parsedData = JSON.parse(draftData);
        setState(prev => ({
          ...prev,
          data: parsedData.data,
          lastSaved: new Date(parsedData.timestamp),
        }));
      }
    } catch (error) {
      console.error('Failed to load draft:', error);
    }
  };

  const updateField = useCallback((field: string, value: any) => {
    setState(prev => ({
      ...prev,
      data: {
        ...prev.data,
        [field]: value,
      },
      isDirty: true,
    }));
  }, []);

  const validateFieldAsync = useCallback(async (field: string): Promise<FormValidationError[]> => {
    const errors = await validateField(field, state.data[field as keyof typeof state.data], state.data);

    setState(prev => ({
      ...prev,
      validationErrors: [
        ...prev.validationErrors.filter(error => error.field !== field),
        ...errors,
      ],
    }));

    return errors;
  }, [state.data]);

  const validateStep = useCallback(async (stepId: string): Promise<boolean> => {
    // console.log('Validating step:', stepId, 'with data:', state.data);
    const stepErrors = await validateMenuItem(state.data, stepId);
    // console.log('Step validation errors for', stepId, ':', stepErrors);

    setState(prev => {
      const updatedSteps = prev.steps.map(step => {
        if (step.id === stepId) {
          const isValid = stepErrors.length === 0;
          // console.log('Step', stepId, 'isValid:', isValid, 'errors:', stepErrors.length);
          return {
            ...step,
            isValid,
            errors: stepErrors,
            isCompleted: isValid,
            progress: isValid ? 100 : Math.max(0, 100 - (stepErrors.length * 20)),
          };
        }
        return step;
      });

      return {
        ...prev,
        steps: updatedSteps,
        validationErrors: [
          ...prev.validationErrors.filter(error =>
            !stepErrors.some(stepError => stepError.field === error.field)
          ),
          ...stepErrors,
        ],
      };
    });

    return stepErrors.length === 0;
  }, [state.data]);

  // Auto-validate current step when form data changes
  useEffect(() => {
    const currentStep = state.steps[state.currentStep];
    if (currentStep && state.data && Object.keys(state.data).length > 0) {
      const timeoutId = setTimeout(() => {
        validateStep(currentStep.id);
      }, 1500); // Increased debounce for mobile stability

      return () => clearTimeout(timeoutId);
    }
  }, [state.data, state.currentStep, state.steps, validateStep]);

  const nextStep = useCallback(() => {
    setState(prev => {
      if (prev.currentStep < prev.steps.length - 1) {
        const newStep = prev.currentStep + 1;
        console.log('Moving from step', prev.currentStep, 'to step', newStep);
        console.log('Current step title:', prev.steps[prev.currentStep]?.title);
        console.log('Next step title:', prev.steps[newStep]?.title);
        return {
          ...prev,
          currentStep: newStep,
        };
      }
      console.log('Already at last step, not advancing');
      return prev;
    });
  }, []);

  const previousStep = useCallback(() => {
    setState(prev => {
      if (prev.currentStep > 0) {
        return {
          ...prev,
          currentStep: prev.currentStep - 1,
        };
      }
      return prev;
    });
  }, []);

  const goToStep = useCallback((stepIndex: number) => {
    setState(prev => {
      const targetIndex = Math.max(0, Math.min(stepIndex, prev.steps.length - 1));

      // Safety check: only allow going to current step or previous completed steps
      if (targetIndex > prev.currentStep) {
        // console.warn('Attempted to jump to future step', targetIndex, 'from current step', prev.currentStep);
        return prev;
      }

      // Allow going to current step or any previous step
      if (targetIndex <= prev.currentStep) {
        // console.log('Navigating to step', targetIndex, 'from step', prev.currentStep);
        return {
          ...prev,
          currentStep: targetIndex,
        };
      }

      return prev;
    });
  }, []);

  const saveDraft = useCallback(async () => {
    try {
      const draftData = {
        data: state.data,
        timestamp: new Date().toISOString(),
      };
      
      await AsyncStorage.setItem(DRAFT_STORAGE_KEY, JSON.stringify(draftData));
      await saveMenuItemDraft(state.data);
      
      setState(prev => ({
        ...prev,
        lastSaved: new Date(),
        isDirty: false,
      }));
    } catch (error) {
      console.error('Failed to save draft:', error);
      Alert.alert('Error', 'Failed to save draft. Please try again.');
    }
  }, [state.data]);

  const submitForm = useCallback(async () => {
    setState(prev => ({ ...prev, isSubmitting: true }));
    
    try {
      // Validate all steps
      const allStepsValid = await Promise.all(
        state.steps.map(step => validateStep(step.id))
      );

      if (allStepsValid.every(Boolean)) {
        await submitMenuItem(state.data as MenuItem);
        await AsyncStorage.removeItem(DRAFT_STORAGE_KEY);
        
        setState(prev => ({
          ...prev,
          isSubmitting: false,
          isDirty: false,
          lastSaved: new Date(),
        }));

        Alert.alert('Success', 'Menu item has been created successfully!');
      } else {
        Alert.alert('Validation Error', 'Please fix all errors before submitting.');
      }
    } catch (error) {
      // console.error('Failed to submit form:', error);
      Alert.alert('Error', 'Failed to create menu item. Please try again.');
    } finally {
      setState(prev => ({ ...prev, isSubmitting: false }));
    }
  }, [state.data, state.steps, validateStep]);

  const resetForm = useCallback(() => {
    setState({
      currentStep: 0,
      steps: initialSteps,
      data: {},
      isDirty: false,
      isSubmitting: false,
      lastSaved: null,
      validationErrors: [],
      analytics: null,
    });
    AsyncStorage.removeItem(DRAFT_STORAGE_KEY);
  }, []);

  const loadTemplate = useCallback((templateId: string) => {
    // Implementation for loading template data
    // console.log('Loading template:', templateId);
  }, []);

  const generateAIDescriptionAsync = useCallback(async (): Promise<string> => {
    try {
      const description = await generateAIDescription(state.data);
      updateField('description', description);
      return description;
    } catch (error) {
      // console.error('Failed to generate AI description:', error);
      throw error;
    }
  }, [state.data, updateField]);

  const optimizeForSEOAsync = useCallback(async () => {
    try {
      const optimizedData = await optimizeForSEO(state.data);
      setState(prev => ({
        ...prev,
        data: { ...prev.data, ...optimizedData },
        isDirty: true,
      }));
    } catch (error) {
      console.error('Failed to optimize for SEO:', error);
      Alert.alert('Error', 'Failed to optimize for SEO. Please try again.');
    }
  }, [state.data]);

  const actions: FormActions = {
    updateField,
    validateField: validateFieldAsync,
    validateStep,
    nextStep,
    previousStep,
    goToStep,
    saveDraft,
    submitForm,
    resetForm,
    loadTemplate,
    generateAIDescription: generateAIDescriptionAsync,
    optimizeForSEO: optimizeForSEOAsync,
  };

  return [state, actions];
}
