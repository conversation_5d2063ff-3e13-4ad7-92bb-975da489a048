import { Colors } from '@/constants/Theme';
import React, { useEffect } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    StatusBar,
    StyleSheet
} from 'react-native';
import Animated, {
    useAnimatedStyle,
    useSharedValue,
    withSpring,
    withTiming,
} from 'react-native-reanimated';

import { FormHeader } from './components/FormHeader';
import { FormNavigation } from './components/FormNavigation';
import { ProgressIndicator } from './components/ProgressIndicator';
import { StepContent } from './components/StepContent';
import { useMenuItemForm } from './hooks/useMenuItemForm';
import { MenuItem } from './types';

interface MenuItemFormProps {
  initialData?: Partial<MenuItem>;
  onSubmit?: (data: MenuItem) => void;
  onCancel?: () => void;
  onSave?: (data: Partial<MenuItem>) => void;
  mode?: 'create' | 'edit';
  restaurantId?: string;
}

export function MenuItemForm({
  initialData,
  onSubmit,
  onCancel,
  onSave,
  mode = 'create',
  restaurantId,
}: MenuItemFormProps) {
  const [formState, formActions] = useMenuItemForm(initialData);
  
  // Animation values
  const slideAnimation = useSharedValue(0);
  const fadeAnimation = useSharedValue(0);
  const scaleAnimation = useSharedValue(0.95);

  // Initialize animations
  useEffect(() => {
    slideAnimation.value = withSpring(1, { damping: 20, stiffness: 100 });
    fadeAnimation.value = withTiming(1, { duration: 500 });
    scaleAnimation.value = withSpring(1, { damping: 15, stiffness: 150 });
  }, []);

  // Step change animation
  useEffect(() => {
    slideAnimation.value = withSpring(0, { damping: 20, stiffness: 100 });
    setTimeout(() => {
      slideAnimation.value = withSpring(1, { damping: 20, stiffness: 100 });
    }, 100);
  }, [formState.currentStep]);

  // Auto-save callback
  useEffect(() => {
    if (onSave && formState.isDirty) {
      onSave(formState.data);
    }
  }, [formState.lastSaved, onSave]);

  const animatedContainerStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
    transform: [
      { scale: scaleAnimation.value },
      { translateY: (1 - slideAnimation.value) * 50 },
    ],
  }));

  const animatedContentStyle = useAnimatedStyle(() => ({
    opacity: slideAnimation.value,
    transform: [{ translateX: (1 - slideAnimation.value) * 100 }],
  }));

  const handleNext = async () => {
    const currentStep = formState.steps[formState.currentStep];

    // Add extra validation check for mobile stability
    if (!currentStep) {
      // console.warn('No current step found, preventing navigation');
      return;
    }

    // console.log('Validating step:', currentStep.id, 'at index:', formState.currentStep);
    const isValid = await formActions.validateStep(currentStep.id);

    if (isValid) {
      // console.log('Step validation passed, moving to next step');
      formActions.nextStep();
    } else {
      // console.log('Step validation failed, showing error');
      Alert.alert(
        'Validation Error',
        'Please fix the errors in this step before continuing.',
        [{ text: 'OK' }]
      );
    }
  };

  const handlePrevious = () => {
    formActions.previousStep();
  };

  const handleSubmit = async () => {
    try {
      await formActions.submitForm();
      if (onSubmit) {
        onSubmit(formState.data as MenuItem);
      }
    } catch (error) {
      Alert.alert(
        'Submission Error',
        'Failed to submit the form. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleCancel = () => {
    if (formState.isDirty) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Are you sure you want to cancel?',
        [
          { text: 'Keep Editing', style: 'cancel' },
          {
            text: 'Discard Changes',
            style: 'destructive',
            onPress: () => {
              formActions.resetForm();
              if (onCancel) onCancel();
            },
          },
        ]
      );
    } else {
      if (onCancel) onCancel();
    }
  };

  const handleSaveDraft = async () => {
    try {
      await formActions.saveDraft();
      Alert.alert('Draft Saved', 'Your progress has been saved as a draft.', [
        { text: 'OK' },
      ]);
    } catch (error) {
      Alert.alert(
        'Save Error',
        'Failed to save draft. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const currentStep = formState.steps[formState.currentStep];
  const isFirstStep = formState.currentStep === 0;
  const isLastStep = formState.currentStep === formState.steps.length - 1;

  // More lenient proceed logic - check if basic required fields are filled
  const hasBasicRequiredFields = () => {
    if (currentStep?.id === 'basic') {
      return !!(formState.data.name && formState.data.description && formState.data.category);
    }
    return true; // For other steps, rely on validation
  };

  const canProceed = currentStep?.isValid || currentStep?.id === 'review' ||
    (currentStep?.id === 'basic' && hasBasicRequiredFields());

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.light.background} />
      
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <Animated.View style={[styles.formContainer, animatedContainerStyle]}>
          {/* Header */}
          <FormHeader
            title={mode === 'create' ? 'Create Menu Item' : 'Edit Menu Item'}
            subtitle={currentStep?.title}
            onCancel={handleCancel}
            onSaveDraft={handleSaveDraft}
            isDirty={formState.isDirty}
            isSubmitting={formState.isSubmitting}
          />

          {/* Progress Indicator */}
          <ProgressIndicator
            steps={formState.steps}
            currentStep={formState.currentStep}
            onStepPress={formActions.goToStep}
          />





          {/* Step Content */}
          <Animated.View style={[styles.contentContainer, animatedContentStyle]}>
            <StepContent
              step={currentStep}
              formData={formState.data}
              onUpdateField={formActions.updateField}
              onValidateField={formActions.validateField}
              onSubmitForm={formActions.submitForm}
              errors={formState.validationErrors}
              analytics={formState.analytics}
              onGenerateAIDescription={formActions.generateAIDescription}
              onOptimizeForSEO={formActions.optimizeForSEO}
              isSubmitting={formState.isSubmitting}
            />
          </Animated.View>

          {/* Navigation */}
          <FormNavigation
            isFirstStep={isFirstStep}
            isLastStep={isLastStep}
            canProceed={canProceed}
            isSubmitting={formState.isSubmitting}
            onPrevious={handlePrevious}
            onNext={handleNext}
            onSubmit={handleSubmit}
            currentStepTitle={currentStep?.title}
            nextStepTitle={
              !isLastStep ? formState.steps[formState.currentStep + 1]?.title : undefined
            }
          />
        </Animated.View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 8, // Reduced from 16 for mobile
  },
  contentContainer: {
    flex: 1,
    marginTop: 4, // Reduced from 8
  },
});

export default MenuItemForm;
