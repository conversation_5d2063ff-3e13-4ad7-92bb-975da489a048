#!/usr/bin/env node

/**
 * Bundle Analysis Script for Restaurant App
 * Analyzes bundle size, dependencies, and provides optimization recommendations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Starting Bundle Analysis...\n');

// Function to get file size in MB
function getFileSizeInMB(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return (stats.size / (1024 * 1024)).toFixed(2);
  } catch (error) {
    return 'N/A';
  }
}

// Function to analyze package.json dependencies
function analyzeDependencies() {
  console.log('📦 Analyzing Dependencies...');
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const dependencies = packageJson.dependencies || {};
  const devDependencies = packageJson.devDependencies || {};
  
  console.log(`   Production Dependencies: ${Object.keys(dependencies).length}`);
  console.log(`   Development Dependencies: ${Object.keys(devDependencies).length}`);
  
  // Large dependencies that might need optimization
  const largeDependencies = [
    'react-native-reanimated',
    'react-native-paper',
    'victory-native',
    'react-native-chart-kit',
    '@reduxjs/toolkit',
    'react-native-svg'
  ];
  
  console.log('\n   📊 Large Dependencies Found:');
  largeDependencies.forEach(dep => {
    if (dependencies[dep]) {
      console.log(`   ✓ ${dep}: ${dependencies[dep]}`);
    }
  });
}

// Function to check for potential optimizations
function checkOptimizations() {
  console.log('\n🚀 Optimization Recommendations:');
  
  const recommendations = [
    {
      check: () => !fs.existsSync('metro.config.js'),
      message: '❌ Metro config not optimized - Create metro.config.js with minification settings'
    },
    {
      check: () => !fs.existsSync('eas.json'),
      message: '❌ EAS Build not configured - Add eas.json for optimized builds'
    },
    {
      check: () => {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        return !packageJson.scripts['analyze-bundle'];
      },
      message: '❌ Bundle analysis script missing - Add bundle analysis to package.json'
    },
    {
      check: () => {
        const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
        return !appJson.expo.newArchEnabled;
      },
      message: '❌ New Architecture not enabled - Enable Fabric and TurboModules'
    }
  ];
  
  recommendations.forEach(rec => {
    if (rec.check()) {
      console.log(`   ${rec.message}`);
    } else {
      console.log(`   ✅ ${rec.message.replace('❌', '✅').replace('not ', '').replace('missing', 'configured')}`);
    }
  });
}

// Function to analyze asset sizes
function analyzeAssets() {
  console.log('\n🖼️  Analyzing Assets...');
  
  const assetDirs = ['assets/images', 'assets/fonts'];
  let totalAssetSize = 0;
  
  assetDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      const files = fs.readdirSync(dir, { recursive: true });
      let dirSize = 0;
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        if (fs.statSync(filePath).isFile()) {
          const size = fs.statSync(filePath).size;
          dirSize += size;
          totalAssetSize += size;
        }
      });
      
      console.log(`   ${dir}: ${(dirSize / (1024 * 1024)).toFixed(2)} MB`);
    }
  });
  
  console.log(`   Total Asset Size: ${(totalAssetSize / (1024 * 1024)).toFixed(2)} MB`);
  
  if (totalAssetSize > 10 * 1024 * 1024) { // 10MB
    console.log('   ⚠️  Large asset size detected. Consider:');
    console.log('      - Converting images to WebP format');
    console.log('      - Using vector icons instead of image icons');
    console.log('      - Implementing lazy loading for images');
  }
}

// Function to provide performance tips
function performanceTips() {
  console.log('\n💡 Performance Optimization Tips:');
  
  const tips = [
    '1. Enable Hermes engine for better JavaScript performance',
    '2. Use FlatList instead of ScrollView for large lists',
    '3. Implement lazy loading for screens and components',
    '4. Optimize images with WebP format and proper sizing',
    '5. Use React.memo() for expensive components',
    '6. Implement proper cleanup in useEffect hooks',
    '7. Use AsyncStorage efficiently with batching',
    '8. Enable ProGuard/R8 for Android builds',
    '9. Use bundle splitting for large applications',
    '10. Monitor memory usage and prevent leaks'
  ];
  
  tips.forEach(tip => console.log(`   ${tip}`));
}

// Main analysis function
function runAnalysis() {
  try {
    analyzeDependencies();
    checkOptimizations();
    analyzeAssets();
    performanceTips();
    
    console.log('\n✅ Bundle Analysis Complete!');
    console.log('\n📈 Next Steps:');
    console.log('   1. Run: npm run build:android to create optimized build');
    console.log('   2. Use: npx react-native-bundle-visualizer for visual analysis');
    console.log('   3. Monitor: App performance with React DevTools Profiler');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
  }
}

// Run the analysis
runAnalysis();
