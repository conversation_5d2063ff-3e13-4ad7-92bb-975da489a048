// Fallback for using MaterialCommunityIcons on Android and web.

import { MaterialCommunityIcons } from '@expo/vector-icons';
import { SymbolWeight } from 'expo-symbols';
import { OpaqueColorValue, type StyleProp, type TextStyle } from 'react-native';

type IconSymbolName = keyof typeof MAPPING;

/**
 * Comprehensive SF Symbols to Material Community Icons mappings for restaurant app.
 * - see Material Community Icons in the [Icons Directory](https://icons.expo.fyi).
 * - see SF Symbols in the [SF Symbols](https://developer.apple.com/sf-symbols/) app.
 */
const MAPPING = {
  // Original mappings
  'house.fill': 'home',
  'paperplane.fill': 'send',
  'chevron.left.forwardslash.chevron.right': 'code',
  'chevron.right': 'chevron-right',

  // Restaurant app specific mappings
  'star.fill': 'star',
  'star': 'star-outline',
  'clock': 'clock-outline',
  'chart.bar': 'chart-bar',
  'chart.line.uptrend.xyaxis': 'chart-line',
  'pencil': 'pencil',
  'leaf': 'leaf',
  'heart': 'heart',
  'checkmark.shield': 'shield-check',
  'flame': 'fire',
  'slider.horizontal.3': 'tune',
  'cube.box': 'cube-outline',
  'magnifyingglass': 'magnify',
  'xmark.circle.fill': 'close-circle',
  'line.3.horizontal.decrease.circle': 'filter-variant',
  'square.grid.2x2': 'view-grid',
  'list.bullet': 'format-list-bulleted',
  'plus': 'plus',
  'trash': 'delete',
  'xmark': 'close',
  'book': 'book-open-variant',
  'photo': 'image',
  'camera': 'camera',
  'square.and.arrow.up': 'share-variant',
  'resize': 'resize',
  'plus-circle': 'plus-circle',
  'cup': 'cup',

  // Navigation
  'chevron.left': 'chevron-left',
  'chevron.up': 'chevron-up',
  'chevron.down': 'chevron-down',
  'arrow.left': 'arrow-left',
  'arrow.right': 'arrow-right',
  'arrow.up': 'arrow-up',
  'arrow.down': 'arrow-down',

  // Common actions
  'checkmark': 'check',
  'multiply': 'close',
  'plus.circle': 'plus-circle',
  'minus.circle': 'minus-circle',
  'info.circle': 'information',
  'exclamationmark.triangle': 'alert-triangle',
  'questionmark.circle': 'help-circle',

  // Food & Restaurant
  'fork.knife': 'silverware-fork-knife',
  'cup.and.saucer': 'coffee',
  'wineglass': 'glass-wine',
  'birthday.cake': 'cake-variant',

  // Security
  'lock': 'lock',
  'lock.open': 'lock-open',
  'key': 'key',
  'shield': 'shield',
  'eye': 'eye',
  'eye.slash': 'eye-off',

  // Social
  'person': 'account',
  'person.2': 'account-group',
  'message': 'message',
  'bell': 'bell',
  'share': 'share',

  // Settings
  'gear': 'cog',
  'wrench': 'wrench',
  'hammer': 'hammer',
  'paintbrush': 'brush',

  // Default fallbacks
  'circle': 'circle-outline',
  'square': 'square-outline',
  'triangle': 'triangle-outline',
  'diamond': 'diamond-outline',

  // Missing icons from warnings - using valid MaterialCommunityIcons names
  'envelope': 'email',
  'moon.stars.fill': 'weather-night',
  'bell.fill': 'bell',
  'menucard.fill': 'food',
  'chart.bar.xaxis': 'chart-line',
  'person.3.fill': 'account-multiple',
  'dollarsign.circle.fill': 'currency-usd',
  'bag.fill': 'bag-personal',
  'clock.fill': 'clock',
  'flame.fill': 'fire',
  'checkmark.circle.fill': 'check-circle',
  'calendar.badge.clock': 'calendar-clock',
  'calendar.badge.plus': 'calendar-plus',
  'chart.bar.fill': 'chart-bar',
  'clock.arrow.circlepath': 'clock-outline',
  'person.2.fill': 'account-group',
  'exclamationmark.triangle.fill': 'alert',
  'exclamationmark.circle.fill': 'alert-circle',
  'list.clipboard.fill': 'clipboard-list',
  'book.fill': 'book',
  'person.fill': 'account',
  'gearshape.fill': 'cog',
  'camera.fill': 'camera',
  'ellipsis': 'dots-horizontal',
  'phone.fill': 'phone',
  'envelope.fill': 'email',
  'location.fill': 'map-marker',
  'test-tube': 'test-tube',
  'arrow.clockwise': 'refresh',
  'car.fill': 'car',
  'eye.fill': 'eye',
  'location': 'map-marker',

  // AI & Magic
  'auto-fix': 'auto-fix',
  'wand.and.stars': 'auto-fix',

  // Marketing & Communication
  'bullhorn': 'bullhorn',
  'megaphone': 'bullhorn',

  // Weather & Time Icons
  'sun.max.fill': 'weather-sunny',
  'sun.haze.fill': 'weather-hazy',
  'moon.fill': 'weather-night',

  // Navigation & Actions
  'arrow.right.square': 'arrow-right-box',
  'content-copy': 'content-copy',

  // Security & Authentication Icons (fill variants)
  'lock.fill': 'lock',
  'eye.slash.fill': 'eye-off',

  // Social & Web Icons
  'globe': 'earth',
} as const;

/**
 * An icon component that uses native SF Symbols on iOS, and Material Community Icons on Android and web.
 * This ensures a consistent look across platforms, and optimal resource usage.
 * Icon `name`s are based on SF Symbols and require manual mapping to Material Community Icons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName | string;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
  weight?: SymbolWeight;
}) {
  // Get the mapped icon name, fallback to original name if not found
  const iconName = MAPPING[name as IconSymbolName] || name;

  // Debug logging for unmapped icons
  if (!MAPPING[name as IconSymbolName] && name !== iconName) {
    // console.warn(`IconSymbol: No mapping found for "${name}"`);
  }

  return <MaterialCommunityIcons color={color} size={size} name={iconName as any} style={style} />;
}
