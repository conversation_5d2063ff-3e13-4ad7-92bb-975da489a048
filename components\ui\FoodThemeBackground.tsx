import React from 'react';
import { ImageBackground, StyleSheet, View } from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';

interface FoodThemeBackgroundProps {
  children: React.ReactNode;
  variant?: 'restaurant' | 'kitchen' | 'food' | 'dining' | 'chef' | 'ingredients';
  intensity?: number;
  overlay?: boolean;
}

// Food and restaurant themed background images (using placeholder URLs - replace with actual food images)
const FOOD_BACKGROUNDS = {
  restaurant: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800&q=80', // Restaurant interior
  kitchen: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&q=80', // Professional kitchen
  food: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&q=80', // Delicious food spread
  dining: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=800&q=80', // Elegant dining
  chef: 'https://images.unsplash.com/photo-1577219491135-ce391730fb2c?w=800&q=80', // Chef cooking
  ingredients: 'https://images.unsplash.com/photo-1506368249639-73a05d6f6488?w=800&q=80', // Fresh ingredients
};

export const FoodThemeBackground: React.FC<FoodThemeBackgroundProps> = ({
  children,
  variant = 'restaurant',
  intensity = 80,
  overlay = true,
}) => {
  const backgroundImage = FOOD_BACKGROUNDS[variant];

  return (
    <View style={styles.container}>
      <ImageBackground
        source={{ uri: backgroundImage }}
        style={styles.backgroundImage}
        resizeMode="cover"
      >
        <BlurView intensity={intensity} style={styles.blurContainer}>
          {overlay && (
            <LinearGradient
              colors={[
                'rgba(255, 255, 255, 0.95)',
                'rgba(255, 255, 255, 0.85)',
                'rgba(220, 20, 60, 0.05)', // Very subtle red tint
              ]}
              style={styles.overlay}
            />
          )}
          <View style={styles.contentContainer}>
            {children}
          </View>
        </BlurView>
      </ImageBackground>
    </View>
  );
};

// Alternative local food images component for better performance
export const LocalFoodBackground: React.FC<FoodThemeBackgroundProps> = ({
  children,
  variant = 'restaurant',
  intensity = 80,
  overlay = true,
}) => {
  // For now, we'll use a gradient background with food-inspired colors
  // You can replace this with actual local images later
  const getGradientColors = (variant: string) => {
    switch (variant) {
      case 'restaurant':
        return ['#FFF8F0', '#FFF5F5', '#FFEAA7']; // Warm restaurant ambiance
      case 'kitchen':
        return ['#F8F9FA', '#E9ECEF', '#DEE2E6']; // Clean kitchen vibes
      case 'food':
        return ['#FFF3E0', '#FFE0B2', '#FFCC80']; // Golden food colors
      case 'dining':
        return ['#F3E5F5', '#E1BEE7', '#CE93D8']; // Elegant dining
      case 'chef':
        return ['#E8F5E8', '#C8E6C9', '#A5D6A7']; // Fresh chef colors
      case 'ingredients':
        return ['#E0F2F1', '#B2DFDB', '#80CBC4']; // Fresh ingredients
      default:
        return ['#FFFFFF', '#FAFAFA', '#F5F5F5'];
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={getGradientColors(variant)}
        style={styles.backgroundImage}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {overlay && (
          <LinearGradient
            colors={[
              'rgba(255, 255, 255, 0.7)',
              'rgba(255, 255, 255, 0.5)',
              'rgba(220, 20, 60, 0.03)', // Very subtle red tint
            ]}
            style={styles.overlay}
          />
        )}
        <View style={styles.contentContainer}>
          {children}
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  blurContainer: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
  },
  contentContainer: {
    flex: 1,
    zIndex: 1,
  },
});

export default FoodThemeBackground;
