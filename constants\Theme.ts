import { StyleSheet } from 'react-native';

// Color Palette
export const Colors = {
  light: {
    // Primary Colors - Red Theme
    primary: '#DC143C',      // Crimson Red - main brand color
    primaryDark: '#B91C3C',  // Darker red
    primaryLight: '#EF4444', // Lighter red

    // Secondary Colors - White and Gray
    secondary: '#FFFFFF',    // Pure white
    secondaryDark: '#F8FAFC', // Very light gray
    secondaryLight: '#FFFFFF', // Pure white

    // Accent Color
    accent: '#DC2626',       // Red accent
    
    // Status Colors
    success: '#DC143C',      // Red for success (consistent with theme)
    successLight: '#FEE2E2', // Light red background
    warning: '#DC143C',      // Red for warning
    warningLight: '#FEE2E2', // Light red background
    error: '#B91C1C',        // Dark red for errors
    errorLight: '#FEE2E2',   // Light red background
    info: '#DC143C',         // Red for info
    infoLight: '#FEE2E2',    // Light red background
    
    // Background Colors - White Theme
    background: '#FFFFFF',
    backgroundSecondary: '#FAFAFA',
    backgroundTertiary: '#F5F5F5',

    // Surface Colors - White Theme
    surface: '#FFFFFF',
    surfaceSecondary: '#FAFAFA',
    
    // Text Colors
    text: '#1F2937',         // Dark gray for main text
    textSecondary: '#6B7280', // Medium gray for secondary text
    textTertiary: '#9CA3AF',  // Light gray for tertiary text
    textInverse: '#FFFFFF',   // White text for red backgrounds
    
    // Border Colors
    border: '#E5E7EB',       // Light gray borders
    borderLight: '#F3F4F6',  // Very light gray borders
    borderDark: '#D1D5DB',   // Medium gray borders
    
    // Shadow
    shadow: '#000000',
    
    // Order Status Colors - Red Theme
    pending: '#DC143C',      // Red
    confirmed: '#B91C1C',    // Dark red
    preparing: '#DC2626',    // Red
    ready: '#DC143C',        // Red
    delivered: '#DC143C',    // Red
    cancelled: '#7F1D1D',    // Very dark red
    rejected: '#7F1D1D',     // Very dark red
  },
  dark: {
    // Primary Colors - Red Theme
    primary: '#EF4444',      // Bright red for dark mode
    primaryDark: '#DC2626',  // Darker red
    primaryLight: '#F87171', // Lighter red

    // Secondary Colors - Dark grays
    secondary: '#1F2937',    // Dark gray
    secondaryDark: '#111827', // Very dark gray
    secondaryLight: '#374151', // Medium dark gray
    
    // Status Colors - Red Theme for Dark Mode
    success: '#EF4444',      // Red for success
    successLight: '#7F1D1D', // Dark red background
    warning: '#EF4444',      // Red for warning
    warningLight: '#7F1D1D', // Dark red background
    error: '#DC2626',        // Dark red for errors
    errorLight: '#7F1D1D',   // Dark red background
    info: '#EF4444',         // Red for info
    infoLight: '#7F1D1D',    // Dark red background
    
    // Background Colors
    background: '#121212',
    backgroundSecondary: '#1E1E1E',
    backgroundTertiary: '#2C2C2C',
    
    // Surface Colors
    surface: '#1E1E1E',
    surfaceSecondary: '#2C2C2C',
    
    // Text Colors
    text: '#FFFFFF',
    textSecondary: '#B3B3B3',
    textTertiary: '#808080',
    textInverse: '#000000',
    
    // Border Colors
    border: '#404040',
    borderLight: '#303030',
    borderDark: '#606060',
    
    // Shadow
    shadow: '#000000',
    
    // Order Status Colors - Red Theme for Dark Mode
    pending: '#EF4444',      // Red
    confirmed: '#DC2626',    // Dark red
    preparing: '#F87171',    // Light red
    ready: '#EF4444',        // Red
    delivered: '#EF4444',    // Red
    cancelled: '#B91C1C',    // Very dark red
    rejected: '#B91C1C',     // Very dark red
  }
};

// Typography
export const Typography = {
  // Font Sizes
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  // Font Weights
  fontWeight: {
    light: '300' as const,
    regular: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  
  // Line Heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  }
};

// Spacing
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// Border Radius
export const BorderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
};

// Shadows
export const Shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};

// Common Styles
export const CommonStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  
  column: {
    flexDirection: 'column',
  },
  
  card: {
    backgroundColor: Colors.light.surface,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    ...Shadows.small,
  },
  
  button: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  buttonPrimary: {
    backgroundColor: Colors.light.primary,
  },
  
  buttonSecondary: {
    backgroundColor: Colors.light.secondary,
  },
  
  buttonText: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.light.textInverse,
  },
  
  input: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: BorderRadius.sm,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    fontSize: Typography.fontSize.md,
    backgroundColor: Colors.light.surface,
  },
  
  textPrimary: {
    fontSize: Typography.fontSize.md,
    color: Colors.light.text,
    fontWeight: Typography.fontWeight.regular,
  },
  
  textSecondary: {
    fontSize: Typography.fontSize.sm,
    color: Colors.light.textSecondary,
    fontWeight: Typography.fontWeight.regular,
  },
  
  textBold: {
    fontWeight: Typography.fontWeight.bold,
  },
  
  textCenter: {
    textAlign: 'center',
  },
  
  divider: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginVertical: Spacing.md,
  },
  
  badge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  badgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.light.textInverse,
  },
});

// Order Status Styles
export const getOrderStatusColor = (status: string, isDark = false) => {
  const colors = isDark ? Colors.dark : Colors.light;
  
  switch (status) {
    case 'pending':
      return colors.pending;
    case 'confirmed':
      return colors.confirmed;
    case 'preparing':
      return colors.preparing;
    case 'ready':
      return colors.ready;
    case 'delivered':
      return colors.delivered;
    case 'cancelled':
    case 'rejected':
      return colors.cancelled;
    default:
      return colors.textSecondary;
  }
};

export const getOrderStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return 'Pending';
    case 'confirmed':
      return 'Confirmed';
    case 'preparing':
      return 'Preparing';
    case 'ready':
      return 'Ready';
    case 'picked_up':
      return 'Picked Up';
    case 'delivered':
      return 'Delivered';
    case 'cancelled':
      return 'Cancelled';
    case 'rejected':
      return 'Rejected';
    default:
      return status;
  }
};
