{"expo": {"name": "restaurant_app1", "slug": "restaurant_app1", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "restaurantapp1", "userInterfaceStyle": "automatic", "newArchEnabled": true, "platforms": ["android"], "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.restaurant.app", "versionCode": 1, "compileSdkVersion": 34, "targetSdkVersion": 34, "minSdkVersion": 21, "buildToolsVersion": "34.0.0", "proguardEnabled": true, "enableProguardInReleaseBuilds": true, "enableHermes": true, "enableSeparateBuildPerCPUArchitecture": true, "universalApk": false, "bundleInDebug": false, "bundleInRelease": true, "enableAapt2": true}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}