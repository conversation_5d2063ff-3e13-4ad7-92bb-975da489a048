{"expo": {"name": "restaurant_app1", "slug": "restaurant_app1", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "restaurantapp1", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.restaurant.app", "buildNumber": "1", "deploymentTarget": "13.0", "infoPlist": {"UIBackgroundModes": ["background-fetch", "background-processing"], "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": false, "NSExceptionDomains": {}}}, "config": {"usesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.restaurant.app", "versionCode": 1, "compileSdkVersion": 34, "targetSdkVersion": 34, "minSdkVersion": 21, "buildToolsVersion": "34.0.0", "proguardEnabled": true, "enableProguardInReleaseBuilds": true, "enableHermes": true, "enableSeparateBuildPerCPUArchitecture": true, "universalApk": false}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}