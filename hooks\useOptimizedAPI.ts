import { useCallback, useEffect, useRef, useState } from 'react';
import { apiOptimizer, RequestConfig } from '@/utils/apiOptimization';

interface UseOptimizedAPIOptions {
  immediate?: boolean;
  cache?: boolean;
  cacheTime?: number;
  retry?: number;
  timeout?: number;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

interface UseOptimizedAPIReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  execute: (config?: Partial<RequestConfig>) => Promise<T>;
  refresh: () => Promise<T>;
  cancel: () => void;
}

export function useOptimizedAPI<T = any>(
  config: RequestConfig,
  options: UseOptimizedAPIOptions = {}
): UseOptimizedAPIReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const abortControllerRef = useRef<AbortController | null>(null);
  const mountedRef = useRef(true);
  const configRef = useRef(config);
  
  // Update config ref when config changes
  useEffect(() => {
    configRef.current = config;
  }, [config]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const execute = useCallback(async (overrideConfig?: Partial<RequestConfig>): Promise<T> => {
    if (!mountedRef.current) return Promise.reject(new Error('Component unmounted'));

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    
    const finalConfig = {
      ...configRef.current,
      ...overrideConfig,
      cache: options.cache ?? configRef.current.cache,
      cacheTime: options.cacheTime ?? configRef.current.cacheTime,
      retry: options.retry ?? configRef.current.retry,
      timeout: options.timeout ?? configRef.current.timeout,
    };

    setLoading(true);
    setError(null);

    try {
      const result = await apiOptimizer.request(finalConfig);
      
      if (mountedRef.current) {
        setData(result);
        setError(null);
        options.onSuccess?.(result);
      }
      
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      
      if (mountedRef.current) {
        setError(error);
        options.onError?.(error);
      }
      
      throw error;
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
      abortControllerRef.current = null;
    }
  }, [options]);

  const refresh = useCallback(() => {
    return execute();
  }, [execute]);

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setLoading(false);
  }, []);

  // Execute immediately if requested
  useEffect(() => {
    if (options.immediate) {
      execute();
    }
  }, [execute, options.immediate]);

  return {
    data,
    loading,
    error,
    execute,
    refresh,
    cancel,
  };
}

// Hook for batch API requests
export function useBatchAPI() {
  const [requests, setRequests] = useState<Map<string, any>>(new Map());
  const [loading, setLoading] = useState(false);

  const addRequest = useCallback((id: string, config: RequestConfig) => {
    setRequests(prev => new Map(prev.set(id, { config, status: 'pending' })));
  }, []);

  const executeBatch = useCallback(async () => {
    setLoading(true);
    const results = new Map();

    try {
      const promises = Array.from(requests.entries()).map(async ([id, { config }]) => {
        try {
          const result = await apiOptimizer.batchRequest(config);
          results.set(id, { data: result, error: null });
        } catch (error) {
          results.set(id, { data: null, error });
        }
      });

      await Promise.all(promises);
      
      setRequests(prev => {
        const updated = new Map(prev);
        results.forEach((result, id) => {
          if (updated.has(id)) {
            updated.set(id, { ...updated.get(id), ...result, status: 'completed' });
          }
        });
        return updated;
      });

    } finally {
      setLoading(false);
    }

    return results;
  }, [requests]);

  const clearBatch = useCallback(() => {
    setRequests(new Map());
  }, []);

  return {
    requests,
    loading,
    addRequest,
    executeBatch,
    clearBatch,
  };
}

// Hook for infinite scroll with API optimization
export function useInfiniteAPI<T = any>(
  baseConfig: RequestConfig,
  options: {
    pageSize?: number;
    getNextPageParam?: (lastPage: any, allPages: any[]) => any;
    enabled?: boolean;
  } = {}
) {
  const [pages, setPages] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [hasNextPage, setHasNextPage] = useState(true);

  const { pageSize = 20, getNextPageParam, enabled = true } = options;

  const loadPage = useCallback(async (page: number = 0) => {
    const isFirstPage = page === 0;
    
    if (isFirstPage) {
      setLoading(true);
      setPages([]);
    } else {
      setLoadingMore(true);
    }

    setError(null);

    try {
      const config = {
        ...baseConfig,
        url: `${baseConfig.url}?page=${page}&limit=${pageSize}`,
      };

      const result = await apiOptimizer.request(config);
      
      setPages(prev => isFirstPage ? [result] : [...prev, result]);
      
      // Determine if there's a next page
      if (getNextPageParam) {
        const nextPageParam = getNextPageParam(result, [...pages, result]);
        setHasNextPage(!!nextPageParam);
      } else {
        // Default logic: check if returned items equal page size
        setHasNextPage(result?.data?.length === pageSize);
      }

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [baseConfig, pageSize, getNextPageParam, pages]);

  const loadMore = useCallback(() => {
    if (!loadingMore && hasNextPage) {
      loadPage(pages.length);
    }
  }, [loadPage, loadingMore, hasNextPage, pages.length]);

  const refresh = useCallback(() => {
    loadPage(0);
  }, [loadPage]);

  // Load first page when enabled
  useEffect(() => {
    if (enabled && pages.length === 0) {
      loadPage(0);
    }
  }, [enabled, loadPage, pages.length]);

  // Flatten all pages data
  const data = pages.flatMap(page => page?.data || page || []);

  return {
    data,
    pages,
    loading,
    loadingMore,
    error,
    hasNextPage,
    loadMore,
    refresh,
  };
}

// Hook for real-time data with polling
export function usePollingAPI<T = any>(
  config: RequestConfig,
  options: {
    interval?: number;
    enabled?: boolean;
    immediate?: boolean;
  } = {}
) {
  const { interval = 30000, enabled = true, immediate = true } = options;
  
  const { data, loading, error, execute } = useOptimizedAPI<T>(config, { immediate });
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (enabled && interval > 0) {
      intervalRef.current = setInterval(() => {
        execute();
      }, interval);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [enabled, interval, execute]);

  const startPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    intervalRef.current = setInterval(() => {
      execute();
    }, interval);
  }, [execute, interval]);

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  return {
    data,
    loading,
    error,
    startPolling,
    stopPolling,
    refresh: execute,
  };
}
