import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { PremiumColors, PremiumShadows } from '@/constants/PremiumTheme';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
    RefreshControl,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { AppIcon, RestaurantIcons } from '../../components/ui/AppIcon';

// const { width: screenWidth } = Dimensions.get('window'); // Removed unused variable

// Mock Analytics Data
const mockAnalyticsData = {
  revenue: {
    today: 2847,
    todayChange: 12,
    week: 18432,
    weekChange: 8,
    month: 67891,
    monthChange: -3,
    year: 789234,
    yearChange: 15,
  },
  orders: {
    today: 127,
    todayChange: 15,
    completionRate: 94,
    avgPrepTime: 18,
    targetPrepTime: 15,
    pending: 8,
    preparing: 15,
    ready: 4,
    delivered: 100,
  },
  customers: {
    newVsReturning: { new: 32, returning: 95 },
    satisfaction: 4.7,
    reviewCount: 234,
    avgLifetimeValue: 247,
    retentionRate: 68,
    retentionChange: 5,
  },
};

const revenueChartData = [
  { day: 'Mon', revenue: 2100 },
  { day: 'Tue', revenue: 2400 },
  { day: 'Wed', revenue: 2200 },
  { day: 'Thu', revenue: 2800 },
  { day: 'Fri', revenue: 3200 },
  { day: 'Sat', revenue: 3800 },
  { day: 'Sun', revenue: 2847 },
];

const topItems = [
  { id: 1, name: 'Classic Burger', emoji: '🍔', orders: 47, revenue: 564, change: 23 },
  { id: 2, name: 'Margherita Pizza', emoji: '🍕', orders: 34, revenue: 408, change: 12 },
  { id: 3, name: 'Loaded Fries', emoji: '🍟', orders: 29, revenue: 174, change: -5 },
  { id: 4, name: 'Combo Meal', emoji: '🥤', orders: 25, revenue: 375, change: 8 },
  { id: 5, name: 'Chocolate Cake', emoji: '🍰', orders: 18, revenue: 126, change: 15 },
];

export default function AnalyticsScreen() {
  return (
    <ProtectedRoute requiredPermission="analytics">
      <AnalyticsContent />
    </ProtectedRoute>
  );
}

function AnalyticsContent() {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState('today');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);

  const filterOptions = [
    { id: 'all', label: 'All Data', icon: 'apps' },
    { id: 'revenue', label: 'Revenue Only', icon: 'cash' },
    { id: 'orders', label: 'Orders Only', icon: 'receipt' },
    { id: 'customers', label: 'Customers Only', icon: 'people' },
    { id: 'performance', label: 'Performance Only', icon: 'speedometer' },
  ];

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  const formatCurrency = (amount: number) => {
    return `PKR ${amount.toLocaleString()}`;
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value}%`;
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? PremiumColors.success : PremiumColors.danger;
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? 'trending-up' : 'trending-down';
  };

  return (
    <View style={styles.container}>
      <StatusBar style="dark" backgroundColor={PremiumColors.white} />
      
      {/* Premium Header */}
      <LinearGradient
        colors={[PremiumColors.white, PremiumColors.gray]}
        style={styles.headerContainer}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Text style={styles.headerTitle}>📊 Analytics Dashboard</Text>
            <Text style={styles.headerSubtitle}>Real-time insights & performance</Text>
          </View>
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setShowFilters(!showFilters)}
          >
            <AppIcon name={RestaurantIcons.nav.filter} size={20} color="primary" />
            <Text style={styles.filterText}>Filter</Text>
            <AppIcon
              name={showFilters ? "chevron-up" : "chevron-down"}
              size={16}
              color="primary"
            />
          </TouchableOpacity>
        </View>
        
        {/* Time Range Selector */}
        <View style={styles.timeRangeContainer}>
          {['today', 'week', 'month', 'year'].map((range) => (
            <TouchableOpacity
              key={range}
              style={[
                styles.timeRangeButton,
                selectedTimeRange === range && styles.timeRangeButtonActive
              ]}
              onPress={() => setSelectedTimeRange(range)}
            >
              <Text style={[
                styles.timeRangeText,
                selectedTimeRange === range && styles.timeRangeTextActive
              ]}>
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Filter Dropdown */}
        {showFilters && (
          <View style={styles.filterDropdown}>
            <Text style={styles.filterDropdownTitle}>Filter Analytics Data</Text>
            <View style={styles.filterOptionsContainer}>
              {filterOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.filterOption,
                    selectedFilter === option.id && styles.filterOptionActive
                  ]}
                  onPress={() => {
                    setSelectedFilter(option.id);
                    setShowFilters(false); // Auto-close dropdown
                  }}
                >
                  <AppIcon
                    name={option.icon as any}
                    size={18}
                    color={selectedFilter === option.id ? PremiumColors.white : PremiumColors.primary}
                  />
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilter === option.id && styles.filterOptionTextActive
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      </LinearGradient>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Enhanced KPI Cards Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📊 Key Performance Indicators</Text>

          {/* Primary Metrics - Vertical Layout */}
          <View style={styles.kpiColumn}>
            {/* Revenue Card - Full Width */}
            <View style={[styles.kpiCard, styles.kpiCardFull]}>
              <View style={styles.kpiHeader}>
                <View style={[styles.iconContainer, { backgroundColor: `${PremiumColors.primary}15` }]}>
                  <AppIcon name={RestaurantIcons.dashboard.revenue} size={24} color="primary" />
                </View>
                <Text style={styles.kpiTitle}>Today&apos;s Revenue</Text>
              </View>
              <Text style={styles.kpiValue}>{formatCurrency(mockAnalyticsData.revenue.today)}</Text>
              <View style={styles.kpiChange}>
                <View style={[styles.changeIndicator, { backgroundColor: `${getChangeColor(mockAnalyticsData.revenue.todayChange)}15` }]}>
                  <AppIcon
                    name={getChangeIcon(mockAnalyticsData.revenue.todayChange)}
                    size={14}
                    color={getChangeColor(mockAnalyticsData.revenue.todayChange)}
                  />
                  <Text style={[styles.kpiChangeText, { color: getChangeColor(mockAnalyticsData.revenue.todayChange) }]}>
                    {formatPercentage(mockAnalyticsData.revenue.todayChange)}
                  </Text>
                </View>
              </View>
              <View style={styles.sparklineContainer}>
                <Text style={styles.sparklineText}>📈 Weekly: {formatCurrency(mockAnalyticsData.revenue.week)}</Text>
              </View>
            </View>

            {/* Orders Card - Full Width */}
            <View style={[styles.kpiCard, styles.kpiCardFull]}>
              <View style={styles.kpiHeader}>
                <View style={[styles.iconContainer, { backgroundColor: `${PremiumColors.success}15` }]}>
                  <AppIcon name={RestaurantIcons.dashboard.orders} size={24} color="success" />
                </View>
                <Text style={styles.kpiTitle}>Total Orders</Text>
              </View>
              <Text style={styles.kpiValue}>{mockAnalyticsData.orders.today}</Text>
              <View style={styles.kpiChange}>
                <View style={[styles.changeIndicator, { backgroundColor: `${getChangeColor(mockAnalyticsData.orders.todayChange)}15` }]}>
                  <AppIcon
                    name={getChangeIcon(mockAnalyticsData.orders.todayChange)}
                    size={14}
                    color={getChangeColor(mockAnalyticsData.orders.todayChange)}
                  />
                  <Text style={[styles.kpiChangeText, { color: getChangeColor(mockAnalyticsData.orders.todayChange) }]}>
                    {formatPercentage(mockAnalyticsData.orders.todayChange)}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Secondary Metrics - Two in Row, One Below */}
          <View style={styles.kpiRow}>
            <View style={styles.kpiCard}>
              <View style={styles.kpiHeader}>
                <View style={[styles.iconContainer, { backgroundColor: `${PremiumColors.warning}15` }]}>
                  <AppIcon name={RestaurantIcons.dashboard.time} size={18} color="warning" />
                </View>
                <Text style={styles.kpiTitleSmall}>Avg Prep Time</Text>
              </View>
              <Text style={styles.kpiValueSmall}>{mockAnalyticsData.orders.avgPrepTime}m</Text>
              <Text style={styles.kpiTarget}>Target: {mockAnalyticsData.orders.targetPrepTime}m</Text>
              <View style={styles.progressBarContainer}>
                <View style={styles.progressBarBackground}>
                  <View style={[
                    styles.progressBarFill,
                    {
                      width: `${Math.min((mockAnalyticsData.orders.targetPrepTime / mockAnalyticsData.orders.avgPrepTime) * 100, 100)}%`,
                      backgroundColor: mockAnalyticsData.orders.avgPrepTime <= mockAnalyticsData.orders.targetPrepTime ?
                        PremiumColors.success : PremiumColors.warning
                    }
                  ]} />
                </View>
              </View>
            </View>

            <View style={styles.kpiCard}>
              <View style={styles.kpiHeader}>
                <View style={[styles.iconContainer, { backgroundColor: `${PremiumColors.success}15` }]}>
                  <AppIcon name="check-circle" size={18} color="success" />
                </View>
                <Text style={styles.kpiTitleSmall}>Completion Rate</Text>
              </View>
              <Text style={styles.kpiValueSmall}>{mockAnalyticsData.orders.completionRate}%</Text>
              <Text style={styles.kpiTarget}>Excellent Performance</Text>
              <View style={styles.progressBarContainer}>
                <View style={styles.progressBarBackground}>
                  <View style={[
                    styles.progressBarFill,
                    {
                      width: `${mockAnalyticsData.orders.completionRate}%`,
                      backgroundColor: mockAnalyticsData.orders.completionRate >= 90 ?
                        PremiumColors.success :
                        mockAnalyticsData.orders.completionRate >= 70 ?
                        PremiumColors.warning : PremiumColors.danger
                    }
                  ]} />
                </View>
              </View>
            </View>
          </View>

          {/* Customer Rating - Single Row */}
          <View style={styles.kpiSingleRow}>
            <View style={[styles.kpiCard, styles.kpiCardCentered]}>
              <View style={styles.kpiHeader}>
                <View style={[styles.iconContainer, { backgroundColor: `${PremiumColors.primary}15` }]}>
                  <AppIcon name={RestaurantIcons.dashboard.rating} size={18} color="primary" />
                </View>
                <Text style={styles.kpiTitleSmall}>Customer Rating</Text>
              </View>
              <Text style={styles.kpiValueSmall}>{mockAnalyticsData.customers.satisfaction}/5</Text>
              <Text style={styles.kpiTarget}>{mockAnalyticsData.customers.reviewCount} reviews</Text>
              <View style={styles.starRating}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <AppIcon
                    key={star}
                    name={star <= mockAnalyticsData.customers.satisfaction ? "star" : "star-outline"}
                    size={12}
                    color={star <= mockAnalyticsData.customers.satisfaction ? PremiumColors.primary : PremiumColors.grayMedium}
                  />
                ))}
              </View>
            </View>
          </View>
        </View>

        {/* Interactive Charts Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>📈 Revenue Analytics</Text>
            <TouchableOpacity style={styles.expandButton}>
              <AppIcon name="arrow-expand" size={16} color="primary" />
            </TouchableOpacity>
          </View>

          {/* Revenue Line Chart */}
          <View style={styles.chartWrapper}>
            <View style={styles.chartContainer}>
              <Text style={styles.chartTitle}>Weekly Revenue Trend</Text>

              {/* Professional Line Chart */}
              <View style={styles.lineChartContainer}>
                {/* Y-Axis Labels */}
                <View style={styles.yAxisContainer}>
                  {[100, 75, 50, 25, 0].map((percentage) => {
                    const value = Math.round((Math.max(...revenueChartData.map(d => d.revenue)) * percentage) / 100);
                    return (
                      <Text key={percentage} style={styles.yAxisLabel}>
                        {value > 1000 ? `${(value/1000).toFixed(0)}K` : value}
                      </Text>
                    );
                  })}
                </View>

                {/* Chart Area */}
                <View style={styles.chartArea}>
                  {/* Grid Lines */}
                  <View style={styles.gridContainer}>
                    {[0, 25, 50, 75, 100].map((percentage) => (
                      <View key={percentage} style={[styles.gridLine, { bottom: `${percentage}%` }]} />
                    ))}
                  </View>

                  {/* Line Chart Path */}
                  <View style={styles.lineContainer}>
                    {revenueChartData.map((item, index) => {
                      const height = (item.revenue / Math.max(...revenueChartData.map(d => d.revenue))) * 100;
                      const left = (index / (revenueChartData.length - 1)) * 100;

                      return (
                        <View key={index}>
                          {/* Data Point */}
                          <View
                            style={[
                              styles.dataPoint,
                              {
                                bottom: `${height}%`,
                                left: `${left}%`,
                                backgroundColor: PremiumColors.primary,
                              }
                            ]}
                          />

                          {/* Connection Line */}
                          {index < revenueChartData.length - 1 && (
                            <View
                              style={[
                                styles.connectionLine,
                                {
                                  bottom: `${height}%`,
                                  left: `${left}%`,
                                  width: `${100 / (revenueChartData.length - 1)}%`,
                                  transform: [
                                    {
                                      rotate: `${Math.atan2(
                                        ((revenueChartData[index + 1].revenue / Math.max(...revenueChartData.map(d => d.revenue))) * 100) - height,
                                        100 / (revenueChartData.length - 1)
                                      ) * (180 / Math.PI)}deg`
                                    }
                                  ]
                                }
                              ]}
                            />
                          )}

                          {/* Value Label */}
                          <View
                            style={[
                              styles.valueLabel,
                              {
                                bottom: `${height + 8}%`,
                                left: `${left}%`,
                              }
                            ]}
                          >
                            <Text style={styles.valueLabelText}>
                              {item.revenue > 1000 ? `${(item.revenue/1000).toFixed(0)}K` : item.revenue}
                            </Text>
                          </View>
                        </View>
                      );
                    })}
                  </View>
                </View>
              </View>

              {/* X-Axis Labels */}
              <View style={styles.xAxisContainer}>
                {revenueChartData.map((item, index) => (
                  <Text key={index} style={styles.xAxisLabel}>{item.day}</Text>
                ))}
              </View>
            </View>
          </View>

          {/* Order Flow Analysis */}
          <View style={styles.chartWrapper}>
            <View style={styles.chartContainer}>
              <Text style={styles.chartTitle}>📋 Order Flow Analysis</Text>
              <View style={styles.flowChartContent}>
                {[
                  { stage: 'Orders Placed', value: 847, percentage: 100, color: PremiumColors.primary },
                  { stage: 'Orders Confirmed', value: 834, percentage: 98, color: PremiumColors.success },
                  { stage: 'In Preparation', value: 782, percentage: 92, color: PremiumColors.warning },
                  { stage: 'Ready for Pickup', value: 721, percentage: 85, color: PremiumColors.info },
                  { stage: 'Delivered', value: 698, percentage: 82, color: PremiumColors.success },
                ].map((stage, index) => (
                  <View key={index} style={styles.flowStage}>
                    <View style={styles.flowStageInfo}>
                      <Text style={styles.flowStageTitle}>{stage.stage}</Text>
                      <Text style={styles.flowStageValue}>
                        {stage.value} ({stage.percentage}%)
                      </Text>
                    </View>
                    <View style={styles.flowBarContainer}>
                      <View
                        style={[
                          styles.flowBar,
                          {
                            width: `${stage.percentage}%`,
                            backgroundColor: stage.color
                          }
                        ]}
                      />
                    </View>
                  </View>
                ))}
              </View>
            </View>
          </View>
        </View>

        {/* Top Performing Items */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🏆 Best Sellers Today</Text>
          
          {topItems.map((item, index) => (
            <View key={item.id} style={styles.topItemCard}>
              <View style={styles.topItemLeft}>
                <Text style={styles.topItemRank}>{index + 1}</Text>
                <Text style={styles.topItemEmoji}>{item.emoji}</Text>
                <View style={styles.topItemInfo}>
                  <Text style={styles.topItemName}>{item.name}</Text>
                  <Text style={styles.topItemOrders}>{item.orders} orders</Text>
                </View>
              </View>
              
              <View style={styles.topItemRight}>
                <Text style={styles.topItemRevenue}>{formatCurrency(item.revenue)}</Text>
                <View style={styles.topItemChange}>
                  <AppIcon
                    name={getChangeIcon(item.change)}
                    size={14}
                    color={getChangeColor(item.change)}
                  />
                  <Text style={[styles.topItemChangeText, { color: getChangeColor(item.change) }]}>
                    {formatPercentage(item.change)}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E0F2F1', // Fresh ingredients-themed background
  },

  // Header Styles
  headerContainer: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: PremiumColors.grayMedium,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: PremiumColors.grayDark,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: PremiumColors.white,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: PremiumColors.primary,
    gap: 6,
  },
  filterText: {
    fontSize: 14,
    color: PremiumColors.primary,
    fontWeight: '600',
  },

  // Filter Dropdown Styles
  filterDropdown: {
    backgroundColor: PremiumColors.white,
    marginTop: 16,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: PremiumColors.grayMedium,
    ...PremiumShadows.small,
  },
  filterDropdownTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 12,
  },
  filterOptionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: PremiumColors.primary,
    backgroundColor: PremiumColors.white,
    gap: 6,
  },
  filterOptionActive: {
    backgroundColor: PremiumColors.primary,
  },
  filterOptionText: {
    fontSize: 12,
    color: PremiumColors.primary,
    fontWeight: '500',
  },
  filterOptionTextActive: {
    color: PremiumColors.white,
  },

  // Time Range Selector
  timeRangeContainer: {
    flexDirection: 'row',
    backgroundColor: PremiumColors.gray,
    borderRadius: 12,
    padding: 4,
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  timeRangeButtonActive: {
    backgroundColor: PremiumColors.primary,
    ...PremiumShadows.small,
  },
  timeRangeText: {
    fontSize: 14,
    color: PremiumColors.grayDark,
    fontWeight: '500',
  },
  timeRangeTextActive: {
    color: PremiumColors.white,
    fontWeight: '600',
  },

  // Content Styles
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 16,
  },
  expandButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: PremiumColors.gray,
  },

  // KPI Cards
  kpiColumn: {
    gap: 12,
    marginBottom: 16,
  },
  kpiRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  kpiSingleRow: {
    alignItems: 'center',
    marginBottom: 12,
  },
  kpiCard: {
    flex: 1,
    backgroundColor: PremiumColors.white,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: PremiumColors.grayMedium,
    ...PremiumShadows.small,
  },
  kpiCardLarge: {
    flex: 2,
  },
  kpiCardFull: {
    width: '100%',
    marginBottom: 12,
  },
  kpiCardCentered: {
    width: '70%',
    alignSelf: 'center',
  },
  kpiHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  kpiTitle: {
    fontSize: 14,
    color: PremiumColors.grayDark,
    fontWeight: '500',
  },
  kpiTitleSmall: {
    fontSize: 12,
    color: PremiumColors.grayDark,
    fontWeight: '500',
  },
  kpiValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 4,
  },
  kpiValueSmall: {
    fontSize: 18,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 4,
  },
  kpiChange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  kpiChangeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  kpiChangeTextSmall: {
    fontSize: 12,
    fontWeight: '600',
  },
  kpiTarget: {
    fontSize: 11,
    color: PremiumColors.grayDark,
  },

  // Enhanced KPI Styles
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  sparklineContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: PremiumColors.grayMedium,
  },
  sparklineText: {
    fontSize: 11,
    color: PremiumColors.grayDark,
    fontWeight: '500',
  },

  // Progress Bar Styles
  progressBarContainer: {
    marginTop: 8,
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: PremiumColors.grayMedium,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 2,
  },

  // Star Rating Styles
  starRating: {
    flexDirection: 'row',
    gap: 2,
    marginTop: 4,
  },

  // Enhanced Chart Styles
  chartWrapper: {
    marginVertical: 8,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 16,
    textAlign: 'center',
  },
  chartContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 120,
    paddingHorizontal: 8,
  },
  chartBar: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 2,
  },
  chartBarInfo: {
    alignItems: 'center',
    marginBottom: 8,
  },
  chartBarDay: {
    fontSize: 12,
    color: PremiumColors.grayDark,
    fontWeight: '500',
  },
  chartBarValue: {
    fontSize: 10,
    color: PremiumColors.black,
    fontWeight: '600',
  },
  chartBarContainer: {
    width: 24,
    height: 80,
    backgroundColor: PremiumColors.grayMedium,
    borderRadius: 4,
    justifyContent: 'flex-end',
    overflow: 'hidden',
  },
  chartBarFill: {
    width: '100%',
    borderRadius: 4,
    minHeight: 4,
  },

  // Horizontal Chart Styles (Rotated)
  chartContentHorizontal: {
    gap: 8,
    paddingVertical: 16,
  },
  chartBarHorizontal: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  chartBarInfoHorizontal: {
    width: 80,
    marginRight: 12,
  },
  chartBarContainerHorizontal: {
    flex: 1,
    height: 20,
    backgroundColor: PremiumColors.grayMedium,
    borderRadius: 10,
    justifyContent: 'center',
    overflow: 'hidden',
  },
  chartBarFillHorizontal: {
    height: '100%',
    borderRadius: 10,
    minWidth: 8,
  },

  // Professional Line Chart Styles
  lineChartContainer: {
    flexDirection: 'row',
    height: 200,
    marginVertical: 16,
  },
  yAxisContainer: {
    width: 40,
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingRight: 8,
    paddingVertical: 10,
  },
  yAxisLabel: {
    fontSize: 10,
    color: PremiumColors.grayDark,
    fontWeight: '500',
  },
  chartArea: {
    flex: 1,
    position: 'relative',
    marginLeft: 8,
  },
  gridContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: PremiumColors.grayMedium,
    opacity: 0.3,
  },
  lineContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  dataPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: -4,
    marginBottom: -4,
    borderWidth: 2,
    borderColor: PremiumColors.white,
    shadowColor: PremiumColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  connectionLine: {
    position: 'absolute',
    height: 2,
    backgroundColor: PremiumColors.primary,
    marginBottom: -1,
    transformOrigin: 'left center',
  },
  valueLabel: {
    position: 'absolute',
    marginLeft: -15,
    marginBottom: 5,
  },
  valueLabelText: {
    fontSize: 9,
    color: PremiumColors.black,
    fontWeight: '600',
    textAlign: 'center',
    backgroundColor: PremiumColors.white,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: PremiumColors.grayMedium,
  },
  xAxisContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 48,
    marginTop: 8,
  },
  xAxisLabel: {
    fontSize: 11,
    color: PremiumColors.grayDark,
    fontWeight: '500',
    textAlign: 'center',
  },

  // Flow Chart Styles
  flowChartContent: {
    gap: 12,
  },
  flowStage: {
    marginBottom: 8,
  },
  flowStageInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  flowStageTitle: {
    fontSize: 14,
    color: PremiumColors.black,
    fontWeight: '500',
  },
  flowStageValue: {
    fontSize: 12,
    color: PremiumColors.grayDark,
    fontWeight: '600',
  },
  flowBarContainer: {
    height: 6,
    backgroundColor: PremiumColors.grayMedium,
    borderRadius: 3,
    overflow: 'hidden',
  },
  flowBar: {
    height: '100%',
    borderRadius: 3,
  },

  // Chart Styles
  chartContainer: {
    backgroundColor: PremiumColors.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: PremiumColors.grayMedium,
    overflow: 'hidden',
    ...PremiumShadows.small,
  },
  chartPlaceholder: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: PremiumColors.gray,
  },
  chartPlaceholderText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 8,
  },
  chartPlaceholderSubtext: {
    fontSize: 12,
    color: PremiumColors.grayDark,
    textAlign: 'center',
    paddingHorizontal: 40,
  },

  // Top Items Styles
  topItemCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: PremiumColors.white,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: PremiumColors.grayMedium,
    marginBottom: 8,
    ...PremiumShadows.small,
  },
  topItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  topItemRank: {
    fontSize: 16,
    fontWeight: 'bold',
    color: PremiumColors.primary,
    width: 24,
    textAlign: 'center',
  },
  topItemEmoji: {
    fontSize: 24,
    marginHorizontal: 12,
  },
  topItemInfo: {
    flex: 1,
  },
  topItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: PremiumColors.black,
    marginBottom: 2,
  },
  topItemOrders: {
    fontSize: 12,
    color: PremiumColors.grayDark,
  },
  topItemRight: {
    alignItems: 'flex-end',
  },
  topItemRevenue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: PremiumColors.black,
    marginBottom: 2,
  },
  topItemChange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  topItemChangeText: {
    fontSize: 12,
    fontWeight: '600',
  },

  // Utility Styles
  bottomSpacing: {
    height: 100,
  },
});
