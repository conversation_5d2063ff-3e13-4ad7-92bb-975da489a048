import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

export default function LoginScreen() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState<'owner' | 'admin' | 'staff' | null>(null);
  const { validateCredentials, login, user } = useAuth();

  const handleLogin = async () => {
    if (!selectedRole) {
      Alert.alert('Error', 'Please select a role first');
      return;
    }

    if (!username.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter both username and password');
      return;
    }

    setLoading(true);
    try {
      // First check demo accounts
      const foundUser = await validateCredentials(username.trim(), password);

      if (foundUser) {
        // Demo account login
        if (foundUser.role !== selectedRole) {
          Alert.alert('Login Failed', `These credentials are not valid for ${selectedRole} role`);
          setLoading(false);
          return;
        }

        await login(username.trim(), password);

        // Redirect based on role
        if (selectedRole === 'staff') {
          router.replace('/(tabs)/orders');
        } else {
          router.replace('/(tabs)');
        }
        return;
      }

      // Try API login for real accounts
      try {
        const response = await authService.login({
          username: username.trim(),
          password: password,
          role: selectedRole
        });

        if (response.success && response.data.user) {
          // Check if user role matches selected role
          if (response.data.user.role !== selectedRole) {
            Alert.alert('Login Failed', `These credentials are not valid for ${selectedRole} role`);
            return;
          }

          // Login successful - user data is already stored by authService
          // Navigation will be handled by AuthWrapper and VerificationGuard
          router.replace('/(tabs)');
        }
      } catch (apiError) {
        // If API login fails, show error
        Alert.alert('Login Failed', apiError instanceof Error ? apiError.message : 'Invalid username or password');
      }
    } catch (error) {
      Alert.alert('Error', 'An error occurred during login');
    } finally {
      setLoading(false);
    }
  };

  const fillDemoCredentials = (role: 'owner' | 'admin' | 'staff') => {
    setUsername(role);
    setPassword(`${role}123`);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: '#DC143C' }]}>
      <StatusBar style="light" backgroundColor="#DC143C" translucent={false} />

      <LinearGradient
        colors={['#DC143C', '#B91C3C']}
        style={styles.gradient}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
          <View style={styles.content}>
            {/* Header */}
            <View style={styles.header}>
              <IconSymbol name="store" size={64} color="#FFFFFF" />
              <Text style={styles.title}>Restaurant Dashboard</Text>
              <Text style={styles.subtitle}>Sign in to manage your restaurant</Text>
            </View>

            {/* Role Selection */}
            <View style={styles.roleSelection}>
              <Text style={styles.roleTitle}>Select Your Role</Text>
              <View style={styles.roleButtons}>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    selectedRole === 'owner' && styles.roleButtonSelected
                  ]}
                  onPress={() => {
                    setSelectedRole('owner');
                    fillDemoCredentials('owner');
                  }}
                >
                  <IconSymbol
                    name="crown"
                    size={24}
                    color="#FFFFFF"
                  />
                  <Text style={[
                    styles.roleButtonText,
                    selectedRole === 'owner' && styles.roleButtonTextSelected
                  ]}>
                    Owner
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    selectedRole === 'admin' && styles.roleButtonSelected
                  ]}
                  onPress={() => {
                    setSelectedRole('admin');
                    fillDemoCredentials('admin');
                  }}
                >
                  <IconSymbol
                    name="account-key"
                    size={24}
                    color="#FFFFFF"
                  />
                  <Text style={[
                    styles.roleButtonText,
                    selectedRole === 'admin' && styles.roleButtonTextSelected
                  ]}>
                    Admin
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    selectedRole === 'staff' && styles.roleButtonSelected
                  ]}
                  onPress={() => {
                    setSelectedRole('staff');
                    fillDemoCredentials('staff');
                  }}
                >
                  <IconSymbol
                    name="person.fill"
                    size={24}
                    color="#FFFFFF"
                  />
                  <Text style={[
                    styles.roleButtonText,
                    selectedRole === 'staff' && styles.roleButtonTextSelected
                  ]}>
                    Staff
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Login Form */}
            <View style={styles.form}>
              <View style={styles.inputContainer}>
                <IconSymbol name="person.fill" size={20} color="#6B7280" />
                <TextInput
                  style={styles.input}
                  placeholder="Username"
                  placeholderTextColor="#9CA3AF"
                  value={username}
                  onChangeText={setUsername}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>

              <View style={styles.inputContainer}>
                <IconSymbol name="lock" size={20} color="#6B7280" />
                <TextInput
                  style={styles.input}
                  placeholder="Password"
                  placeholderTextColor="#9CA3AF"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <IconSymbol
                    name={showPassword ? 'eye.slash.fill' : 'eye.fill'}
                    size={20}
                    color="#DC143C"
                  />
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={[styles.loginButton, loading && styles.loginButtonDisabled]}
                onPress={handleLogin}
                disabled={loading}
              >
                <Text style={styles.loginButtonText}>
                  {loading ? 'Signing In...' : 'Sign In'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Demo Credentials */}
            <View style={styles.demoSection}>
              <Text style={styles.demoTitle}>Demo Accounts</Text>
              <View style={styles.demoButtons}>
                <TouchableOpacity
                  style={styles.demoButton}
                  onPress={() => fillDemoCredentials('owner')}
                >
                  <IconSymbol name="crown" size={16} color="#DC143C" />
                  <Text style={styles.demoButtonText}>Owner</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.demoButton}
                  onPress={() => fillDemoCredentials('admin')}
                >
                  <IconSymbol name="account-key" size={16} color="#DC143C" />
                  <Text style={styles.demoButtonText}>Admin</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.demoButton}
                  onPress={() => fillDemoCredentials('staff')}
                >
                  <IconSymbol name="person.fill" size={16} color="#DC143C" />
                  <Text style={styles.demoButtonText}>Staff</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Continue with Google */}
            <View style={styles.socialSection}>
              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>or</Text>
                <View style={styles.dividerLine} />
              </View>

              <TouchableOpacity style={styles.googleButton} onPress={() => Alert.alert('Coming Soon', 'Google Sign-In will be available soon!')}>
                <IconSymbol name="globe" size={20} color="#DC143C" />
                <Text style={styles.googleButtonText}>Continue with Google</Text>
              </TouchableOpacity>
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={styles.footerText}>Don't have an account? </Text>
              <TouchableOpacity onPress={() => router.replace('/(auth)/register')}>
                <Text style={styles.signUpText}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#DC143C', // Red background to prevent black showing through
  },
  gradient: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.9,
  },
  form: {
    marginBottom: 32,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Semi-transparent white for red theme
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)', // Light border for red background
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937', // Dark gray for better readability on white background
    paddingVertical: 12,
    paddingHorizontal: 12,
    fontWeight: '500',
  },
  eyeButton: {
    padding: 8,
  },
  loginButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Semi-transparent white for contrast
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 8,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)', // White border for red background
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loginButtonDisabled: {
    opacity: 0.6,
  },
  loginButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#DC143C', // Red text on white button
    letterSpacing: 0.5,
  },
  roleSelection: {
    marginBottom: 24,
    alignItems: 'center',
  },
  roleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
    opacity: 0.9,
  },
  roleButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  roleButton: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    minWidth: 80,
  },
  roleButtonSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  roleButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 4,
    opacity: 0.8,
  },
  roleButtonTextSelected: {
    opacity: 1,
  },
  demoSection: {
    alignItems: 'center',
  },
  demoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
    opacity: 0.9,
  },
  demoButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  demoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  demoButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#DC143C',
  },
  socialSection: {
    marginTop: 20,
    marginBottom: 20,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  googleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Semi-transparent white for red theme
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)', // White border for red background
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  googleButtonText: {
    fontSize: 16,
    color: '#1F2937', // Dark gray for better readability on white background
    fontWeight: '600',
    marginLeft: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  footerText: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  signUpText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
