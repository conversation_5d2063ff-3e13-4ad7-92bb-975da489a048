import { usePerformanceMonitor } from '@/utils/performance';
import React, { memo, useCallback, useEffect, useMemo } from 'react';
import { InteractionManager } from 'react-native';

interface PerformanceWrapperProps {
  children: React.ReactNode;
  screenName: string;
  enableMemoryMonitoring?: boolean;
  enableRenderTracking?: boolean;
}

// Higher-order component for performance monitoring
export const withPerformanceMonitoring = <P extends object>(
  Component: React.ComponentType<P>,
  screenName: string
) => {
  const PerformanceEnhancedComponent = memo((props: P) => {
    const { startScreenLoad, endScreenLoad, measureRender } = usePerformanceMonitor(screenName);

    useEffect(() => {
      startScreenLoad();
      
      // Measure when the component is fully interactive
      InteractionManager.runAfterInteractions(() => {
        endScreenLoad();
      });

      // Measure render performance
      measureRender();
    }, [startScreenLoad, endScreenLoad, measureRender]);

    return <Component {...props} />;
  });

  PerformanceEnhancedComponent.displayName = `withPerformanceMonitoring(${Component.displayName || Component.name})`;
  
  return PerformanceEnhancedComponent;
};

// Performance wrapper component
const PerformanceWrapper: React.FC<PerformanceWrapperProps> = memo(({
  children,
  screenName,
  enableMemoryMonitoring = false,
  enableRenderTracking = true
}) => {
  const { startScreenLoad, endScreenLoad, measureRender } = usePerformanceMonitor(screenName);

  useEffect(() => {
    if (enableRenderTracking) {
      startScreenLoad();
      
      const timeoutId = setTimeout(() => {
        endScreenLoad();
      }, 100); // Small delay to ensure rendering is complete

      return () => clearTimeout(timeoutId);
    }
  }, [enableRenderTracking, startScreenLoad, endScreenLoad]);

  useEffect(() => {
    if (enableRenderTracking) {
      measureRender();
    }
  }, [enableRenderTracking, measureRender]);

  // Memory monitoring
  useEffect(() => {
    if (enableMemoryMonitoring && __DEV__) {
      const interval = setInterval(() => {
        const memoryUsage = global.performance?.memory?.usedJSHeapSize;
        if (memoryUsage && memoryUsage > 50 * 1024 * 1024) { // 50MB threshold
          console.warn(`High memory usage in ${screenName}: ${(memoryUsage / 1024 / 1024).toFixed(2)}MB`);
        }
      }, 10000); // Check every 10 seconds

      return () => clearInterval(interval);
    }
  }, [enableMemoryMonitoring, screenName]);

  return <>{children}</>;
});

PerformanceWrapper.displayName = 'PerformanceWrapper';

export default PerformanceWrapper;

// Hook for optimized callbacks
export const useOptimizedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T => {
  return useCallback(callback, deps);
};

// Hook for optimized memoization
export function useOptimizedMemo<T>(
  factory: () => T,
  deps: React.DependencyList
): T {
  return useMemo(factory, deps);
}

// Performance-optimized list item component
interface OptimizedListItemProps {
  children: React.ReactNode;
  itemId: string;
}

export const OptimizedListItem = memo<OptimizedListItemProps>(({ children, itemId }) => {
  // Only re-render if itemId changes
  return <>{children}</>;
}, (prevProps, nextProps) => {
  return prevProps.itemId === nextProps.itemId;
});

OptimizedListItem.displayName = 'OptimizedListItem';
