import { FormValidationError, MenuItem } from '../types';

// Validation rules for different fields
const VALIDATION_RULES = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z0-9\s\-'&.()]+$/,
  },
  description: {
    required: true,
    minLength: 10,
    maxLength: 500,
    seoOptimalLength: { min: 150, max: 300 },
  },
  price: {
    required: true,
    min: 0.01,
    max: 10000,
    decimalPlaces: 2,
  },
  preparationTime: {
    required: true,
    min: 1,
    max: 480, // 8 hours max
  },
  category: {
    required: true,
    minLength: 2,
    maxLength: 50,
  },
  images: {
    required: false, // Made optional for testing
    minCount: 0,
    maxCount: 10,
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedFormats: ['jpg', 'jpeg', 'png', 'webp'],
    minDimensions: { width: 400, height: 300 },
    maxDimensions: { width: 4000, height: 3000 },
    aspectRatio: { min: 0.75, max: 1.5 },
  },
  ingredients: {
    required: false, // Made optional since ingredients step was removed
    minCount: 0,
    maxCount: 50,
  },
  allergens: {
    maxCount: 20,
  },
  customizations: {
    maxCount: 10,
    maxOptionsPerGroup: 20,
  },
  tags: {
    maxCount: 15,
    maxLength: 30,
  },
};

export async function validateField(
  fieldName: string,
  value: any,
  formData: Partial<MenuItem>
): Promise<FormValidationError[]> {
  const errors: FormValidationError[] = [];
  const rule = VALIDATION_RULES[fieldName as keyof typeof VALIDATION_RULES];

  if (!rule) return errors;

  // Required field validation
  if (rule.required && (!value || (Array.isArray(value) && value.length === 0))) {
    errors.push({
      field: fieldName,
      message: `${fieldName} is required`,
      severity: 'error',
    });
    return errors;
  }

  // Skip further validation if field is empty and not required
  if (!value && !rule.required) return errors;

  switch (fieldName) {
    case 'name':
      errors.push(...validateName(value, rule));
      break;
    case 'description':
      errors.push(...validateDescription(value, rule));
      break;
    case 'price':
      errors.push(...validatePrice(value, rule, formData));
      break;
    case 'preparationTime':
      errors.push(...validatePreparationTime(value, rule));
      break;
    case 'category':
      errors.push(...validateCategory(value, rule));
      break;
    case 'images':
      errors.push(...await validateImages(value, rule));
      break;
    case 'ingredients':
      errors.push(...validateIngredients(value, rule));
      break;
    case 'allergens':
      errors.push(...validateAllergens(value, rule));
      break;
    case 'customizations':
      errors.push(...validateCustomizations(value, rule));
      break;
    case 'tags':
      errors.push(...validateTags(value, rule));
      break;
  }

  return errors;
}

function validateName(value: string, rule: any): FormValidationError[] {
  const errors: FormValidationError[] = [];

  if (value.length < rule.minLength) {
    errors.push({
      field: 'name',
      message: `Name must be at least ${rule.minLength} characters`,
      severity: 'error',
    });
  }

  if (value.length > rule.maxLength) {
    errors.push({
      field: 'name',
      message: `Name must not exceed ${rule.maxLength} characters`,
      severity: 'error',
    });
  }

  if (!rule.pattern.test(value)) {
    errors.push({
      field: 'name',
      message: 'Name contains invalid characters',
      severity: 'error',
    });
  }

  // Check for uniqueness (would typically involve API call)
  // This is a placeholder for demonstration
  if (value.toLowerCase().includes('test')) {
    errors.push({
      field: 'name',
      message: 'A menu item with this name already exists',
      severity: 'warning',
    });
  }

  return errors;
}

function validateDescription(value: string, rule: any): FormValidationError[] {
  const errors: FormValidationError[] = [];

  if (value.length < rule.minLength) {
    errors.push({
      field: 'description',
      message: `Description must be at least ${rule.minLength} characters`,
      severity: 'error',
    });
  }

  if (value.length > rule.maxLength) {
    errors.push({
      field: 'description',
      message: `Description must not exceed ${rule.maxLength} characters`,
      severity: 'error',
    });
  }

  // SEO optimization suggestions
  if (value.length < rule.seoOptimalLength.min) {
    errors.push({
      field: 'description',
      message: `For better SEO, consider expanding description to at least ${rule.seoOptimalLength.min} characters`,
      severity: 'info',
    });
  }

  if (value.length > rule.seoOptimalLength.max) {
    errors.push({
      field: 'description',
      message: `For better SEO, consider shortening description to under ${rule.seoOptimalLength.max} characters`,
      severity: 'info',
    });
  }

  // Check for keyword density and readability
  const words = value.split(/\s+/);
  const uniqueWords = new Set(words.map(w => w.toLowerCase()));
  const keywordDensity = (words.length - uniqueWords.size) / words.length;

  if (keywordDensity > 0.3) {
    errors.push({
      field: 'description',
      message: 'Description may be too repetitive. Consider varying your language.',
      severity: 'warning',
    });
  }

  return errors;
}

function validatePrice(value: any, rule: any, formData: Partial<MenuItem>): FormValidationError[] {
  const errors: FormValidationError[] = [];
  const price = Array.isArray(value) ? value[0]?.price : value;

  if (typeof price !== 'number' || isNaN(price)) {
    errors.push({
      field: 'price',
      message: 'Price must be a valid number',
      severity: 'error',
    });
    return errors;
  }

  if (price < rule.min) {
    errors.push({
      field: 'price',
      message: `Price must be at least $${rule.min}`,
      severity: 'error',
    });
  }

  if (price > rule.max) {
    errors.push({
      field: 'price',
      message: `Price must not exceed $${rule.max}`,
      severity: 'error',
    });
  }

  // Check decimal places
  const decimalPlaces = (price.toString().split('.')[1] || '').length;
  if (decimalPlaces > rule.decimalPlaces) {
    errors.push({
      field: 'price',
      message: `Price should have at most ${rule.decimalPlaces} decimal places`,
      severity: 'warning',
    });
  }

  // Category-based price validation (PKR prices)
  if (formData.category) {
    const categoryPriceRanges: Record<string, { min: number; max: number }> = {
      'appetizer': { min: 100, max: 500 },
      'main course': { min: 300, max: 1200 },
      'dessert': { min: 100, max: 400 },
      'beverage': { min: 50, max: 300 },
    };

    const range = categoryPriceRanges[formData.category.toLowerCase()];
    if (range) {
      if (price < range.min || price > range.max) {
        errors.push({
          field: 'price',
          message: `Price seems unusual for ${formData.category}. Typical range: PKR ${range.min}-${range.max}`,
          severity: 'warning',
        });
      }
    }
  }

  return errors;
}

function validatePreparationTime(value: number, rule: any): FormValidationError[] {
  const errors: FormValidationError[] = [];

  if (value < rule.min) {
    errors.push({
      field: 'preparationTime',
      message: `Preparation time must be at least ${rule.min} minute(s)`,
      severity: 'error',
    });
  }

  if (value > rule.max) {
    errors.push({
      field: 'preparationTime',
      message: `Preparation time must not exceed ${rule.max} minutes`,
      severity: 'error',
    });
  }

  // Reasonability checks
  if (value > 120) {
    errors.push({
      field: 'preparationTime',
      message: 'Preparation time over 2 hours may impact customer satisfaction',
      severity: 'warning',
    });
  }

  return errors;
}

function validateCategory(value: string, rule: any): FormValidationError[] {
  const errors: FormValidationError[] = [];

  if (value.length < rule.minLength) {
    errors.push({
      field: 'category',
      message: `Category must be at least ${rule.minLength} characters`,
      severity: 'error',
    });
  }

  if (value.length > rule.maxLength) {
    errors.push({
      field: 'category',
      message: `Category must not exceed ${rule.maxLength} characters`,
      severity: 'error',
    });
  }

  return errors;
}

async function validateImages(value: any[], rule: any): Promise<FormValidationError[]> {
  const errors: FormValidationError[] = [];

  if (!Array.isArray(value)) {
    errors.push({
      field: 'images',
      message: 'Images must be an array',
      severity: 'error',
    });
    return errors;
  }

  if (value.length < rule.minCount) {
    errors.push({
      field: 'images',
      message: `At least ${rule.minCount} image is required`,
      severity: 'error',
    });
  }

  if (value.length > rule.maxCount) {
    errors.push({
      field: 'images',
      message: `Maximum ${rule.maxCount} images allowed`,
      severity: 'error',
    });
  }

  // Validate each image
  for (let i = 0; i < value.length; i++) {
    const image = value[i];
    
    if (image.metadata?.size > rule.maxFileSize) {
      errors.push({
        field: 'images',
        message: `Image ${i + 1} exceeds maximum file size of ${rule.maxFileSize / (1024 * 1024)}MB`,
        severity: 'error',
      });
    }

    if (image.metadata?.width < rule.minDimensions.width || 
        image.metadata?.height < rule.minDimensions.height) {
      errors.push({
        field: 'images',
        message: `Image ${i + 1} is too small. Minimum dimensions: ${rule.minDimensions.width}x${rule.minDimensions.height}`,
        severity: 'warning',
      });
    }

    const aspectRatio = image.metadata?.width / image.metadata?.height;
    if (aspectRatio < rule.aspectRatio.min || aspectRatio > rule.aspectRatio.max) {
      errors.push({
        field: 'images',
        message: `Image ${i + 1} has unusual aspect ratio. Consider cropping for better presentation.`,
        severity: 'info',
      });
    }
  }

  return errors;
}

function validateIngredients(value: any[], rule: any): FormValidationError[] {
  const errors: FormValidationError[] = [];

  if (!Array.isArray(value)) {
    errors.push({
      field: 'ingredients',
      message: 'Ingredients must be an array',
      severity: 'error',
    });
    return errors;
  }

  if (value.length < rule.minCount) {
    errors.push({
      field: 'ingredients',
      message: `At least ${rule.minCount} ingredient is required`,
      severity: 'error',
    });
  }

  if (value.length > rule.maxCount) {
    errors.push({
      field: 'ingredients',
      message: `Maximum ${rule.maxCount} ingredients allowed`,
      severity: 'warning',
    });
  }

  return errors;
}

function validateAllergens(value: any[], rule: any): FormValidationError[] {
  const errors: FormValidationError[] = [];

  if (value && value.length > rule.maxCount) {
    errors.push({
      field: 'allergens',
      message: `Maximum ${rule.maxCount} allergens allowed`,
      severity: 'warning',
    });
  }

  return errors;
}

function validateCustomizations(value: any[], rule: any): FormValidationError[] {
  const errors: FormValidationError[] = [];

  if (value && value.length > rule.maxCount) {
    errors.push({
      field: 'customizations',
      message: `Maximum ${rule.maxCount} customization groups allowed`,
      severity: 'warning',
    });
  }

  // Validate each customization group
  if (Array.isArray(value)) {
    value.forEach((group, index) => {
      // Require at least 1 option when a customization group is added
      if (!group.options || group.options.length === 0) {
        errors.push({
          field: 'customizations',
          message: `Customization group "${group.name || `Group ${index + 1}`}" must have at least 1 option`,
          severity: 'error',
        });
      }

      if (group.options && group.options.length > rule.maxOptionsPerGroup) {
        errors.push({
          field: 'customizations',
          message: `Customization group ${index + 1} has too many options (max: ${rule.maxOptionsPerGroup})`,
          severity: 'warning',
        });
      }
    });
  }

  return errors;
}

function validateTags(value: string[], rule: any): FormValidationError[] {
  const errors: FormValidationError[] = [];

  if (value && value.length > rule.maxCount) {
    errors.push({
      field: 'tags',
      message: `Maximum ${rule.maxCount} tags allowed`,
      severity: 'warning',
    });
  }

  if (Array.isArray(value)) {
    value.forEach((tag, index) => {
      if (tag.length > rule.maxLength) {
        errors.push({
          field: 'tags',
          message: `Tag ${index + 1} is too long (max: ${rule.maxLength} characters)`,
          severity: 'warning',
        });
      }
    });
  }

  return errors;
}

export async function validateMenuItem(
  data: Partial<MenuItem>,
  stepId?: string
): Promise<FormValidationError[]> {
  const errors: FormValidationError[] = [];

  // Define which fields belong to which step
  const stepFields: Record<string, string[]> = {
    basic: ['name', 'description', 'category'],
    pricing: ['price', 'preparationTime'],
    media: ['images'],
    ingredients: ['ingredients', 'allergens'],
    customizations: ['customizations'],
    marketing: [], // All marketing fields are optional
    review: [], // All fields
  };

  const fieldsToValidate = stepId ? stepFields[stepId] || [] : Object.keys(data);

  for (const field of fieldsToValidate) {
    const fieldErrors = await validateField(field, data[field as keyof typeof data], data);
    errors.push(...fieldErrors);
  }

  // Cross-field validations
  if (!stepId || stepId === 'review') {
    // Validate price vs ingredients cost
    if (data.price && data.ingredients) {
      const totalIngredientCost = data.ingredients.reduce((sum, ing) => sum + (ing.cost || 0), 0);
      const price = Array.isArray(data.price) ? data.price[0]?.price : data.price;

      if (typeof price === 'number' && totalIngredientCost > 0) {
        const profitMargin = ((price - totalIngredientCost) / price) * 100;

        if (profitMargin < 20) {
          errors.push({
            field: 'price',
            message: `Low profit margin (${profitMargin.toFixed(1)}%). Consider adjusting price or ingredients.`,
            severity: 'warning',
          });
        }
      }
    }

    // Validate allergens vs ingredients
    if (data.allergens && data.ingredients) {
      const ingredientAllergens = new Set(
        data.ingredients.flatMap(ing => ing.allergens || [])
      );
      const declaredAllergens = new Set(data.allergens.map(a => a.name));

      const missingAllergens = [...ingredientAllergens].filter(a => !declaredAllergens.has(a));
      if (missingAllergens.length > 0) {
        errors.push({
          field: 'allergens',
          message: `Missing allergen declarations: ${missingAllergens.join(', ')}`,
          severity: 'error',
        });
      }
    }

    // Validate dietary claims
    if (data.tags && data.ingredients) {
      const dietaryTags = data.tags.filter(tag =>
        ['vegan', 'vegetarian', 'gluten-free', 'dairy-free'].includes(tag.toLowerCase())
      );

      dietaryTags.forEach(tag => {
        const conflictingIngredients = data.ingredients?.filter(ing => {
          switch (tag.toLowerCase()) {
            case 'vegan':
              return ing.category?.toLowerCase().includes('meat') ||
                     ing.category?.toLowerCase().includes('dairy') ||
                     ing.category?.toLowerCase().includes('egg');
            case 'vegetarian':
              return ing.category?.toLowerCase().includes('meat');
            case 'gluten-free':
              return ing.allergens?.includes('gluten');
            case 'dairy-free':
              return ing.allergens?.includes('dairy');
            default:
              return false;
          }
        });

        if (conflictingIngredients && conflictingIngredients.length > 0) {
          errors.push({
            field: 'tags',
            message: `${tag} claim conflicts with ingredients: ${conflictingIngredients.map(i => i.name).join(', ')}`,
            severity: 'error',
          });
        }
      });
    }
  }

  return errors;
}
