import { useCallback, useEffect, useRef } from 'react';

// Memory monitoring utilities
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private memoryUsageHistory: number[] = [];
  private maxHistorySize = 100;
  private warningThreshold = 150; // MB
  private criticalThreshold = 200; // MB

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  // Get current memory usage
  getCurrentMemoryUsage(): number {
    if (global.performance && global.performance.memory) {
      return global.performance.memory.usedJSHeapSize / (1024 * 1024); // Convert to MB
    }
    return 0;
  }

  // Record memory usage
  recordMemoryUsage(): void {
    const usage = this.getCurrentMemoryUsage();
    this.memoryUsageHistory.push(usage);

    // Keep history size manageable
    if (this.memoryUsageHistory.length > this.maxHistorySize) {
      this.memoryUsageHistory.shift();
    }

    // Check for memory warnings
    this.checkMemoryThresholds(usage);
  }

  // Check memory thresholds and warn if necessary
  private checkMemoryThresholds(usage: number): void {
    if (__DEV__) {
      if (usage > this.criticalThreshold) {
        console.error(`🚨 Critical memory usage: ${usage.toFixed(2)}MB`);
      } else if (usage > this.warningThreshold) {
        console.warn(`⚠️ High memory usage: ${usage.toFixed(2)}MB`);
      }
    }
  }

  // Get memory statistics
  getMemoryStats(): {
    current: number;
    average: number;
    peak: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  } {
    const current = this.getCurrentMemoryUsage();
    const average = this.memoryUsageHistory.reduce((sum, val) => sum + val, 0) / this.memoryUsageHistory.length || 0;
    const peak = Math.max(...this.memoryUsageHistory, current);

    // Calculate trend
    const recentUsage = this.memoryUsageHistory.slice(-10);
    const trend = this.calculateTrend(recentUsage);

    return { current, average, peak, trend };
  }

  // Calculate memory usage trend
  private calculateTrend(values: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (values.length < 2) return 'stable';

    const first = values[0];
    const last = values[values.length - 1];
    const difference = last - first;

    if (Math.abs(difference) < 5) return 'stable'; // Less than 5MB change
    return difference > 0 ? 'increasing' : 'decreasing';
  }

  // Force garbage collection (development only)
  forceGarbageCollection(): void {
    if (__DEV__ && global.gc) {
      global.gc();
      console.log('🗑️ Forced garbage collection');
    }
  }
}

// Hook for memory-efficient component cleanup
export const useMemoryCleanup = (cleanupFn?: () => void) => {
  const cleanupRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    cleanupRef.current = cleanupFn || null;

    return () => {
      // Execute cleanup function
      if (cleanupRef.current) {
        cleanupRef.current();
      }
      
      // Clear the reference
      cleanupRef.current = null;
    };
  }, [cleanupFn]);

  return cleanupRef;
};

// Hook for optimized event listeners
export const useOptimizedEventListener = (
  eventName: string,
  handler: (event: any) => void,
  element?: any,
  options?: any
) => {
  const savedHandler = useRef<(event: any) => void>();

  useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);

  useEffect(() => {
    const eventListener = (event: any) => savedHandler.current?.(event);
    const targetElement = element || global;

    if (targetElement?.addEventListener) {
      targetElement.addEventListener(eventName, eventListener, options);
      
      return () => {
        targetElement.removeEventListener(eventName, eventListener, options);
      };
    }
  }, [eventName, element, options]);
};

// Hook for memory-efficient timers
export const useOptimizedTimer = (
  callback: () => void,
  delay: number | null,
  immediate = false
) => {
  const savedCallback = useRef<() => void>();
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    const tick = () => savedCallback.current?.();

    if (delay !== null) {
      if (immediate) {
        tick();
      }
      
      timerRef.current = setInterval(tick, delay);
      
      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
      };
    }
  }, [delay, immediate]);

  // Manual cleanup function
  const cleanup = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  return cleanup;
};

// Hook for memory-efficient async operations
export const useOptimizedAsync = <T>(
  asyncFn: () => Promise<T>,
  deps: React.DependencyList
) => {
  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);
  const mountedRef = useRef(true);

  useEffect(() => {
    let cancelled = false;

    const executeAsync = async () => {
      if (!mountedRef.current) return;
      
      setLoading(true);
      setError(null);

      try {
        const result = await asyncFn();
        
        if (!cancelled && mountedRef.current) {
          setData(result);
        }
      } catch (err) {
        if (!cancelled && mountedRef.current) {
          setError(err instanceof Error ? err : new Error('Unknown error'));
        }
      } finally {
        if (!cancelled && mountedRef.current) {
          setLoading(false);
        }
      }
    };

    executeAsync();

    return () => {
      cancelled = true;
    };
  }, deps);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return { data, loading, error };
};

// Memory-efficient list rendering utilities
export const ListOptimization = {
  // Calculate optimal item height for FlatList
  getOptimalItemHeight: (screenHeight: number, itemCount: number): number => {
    const maxVisibleItems = 10;
    const minItemHeight = 50;
    const maxItemHeight = 150;
    
    const calculatedHeight = screenHeight / maxVisibleItems;
    return Math.max(minItemHeight, Math.min(maxItemHeight, calculatedHeight));
  },

  // Get optimal window size for FlatList
  getOptimalWindowSize: (itemCount: number): number => {
    if (itemCount < 50) return 10;
    if (itemCount < 200) return 15;
    return 20;
  },

  // Memory-efficient keyExtractor
  keyExtractor: (item: any, index: number): string => {
    return item.id?.toString() || item.key?.toString() || index.toString();
  },

  // Optimized getItemLayout for known item heights
  getItemLayout: (itemHeight: number) => (data: any, index: number) => ({
    length: itemHeight,
    offset: itemHeight * index,
    index,
  }),
};

// Redux store optimization utilities
export const ReduxOptimization = {
  // Create memory-efficient selector
  createMemoizedSelector: <T, R>(
    selector: (state: T) => R,
    equalityFn?: (a: R, b: R) => boolean
  ) => {
    let lastResult: R;
    let lastArgs: T;

    return (state: T): R => {
      if (state !== lastArgs) {
        const result = selector(state);
        
        if (equalityFn ? !equalityFn(lastResult, result) : lastResult !== result) {
          lastResult = result;
        }
        
        lastArgs = state;
      }
      
      return lastResult;
    };
  },

  // Optimize Redux payload size
  optimizePayload: (payload: any): any => {
    // Remove undefined values and empty objects
    if (typeof payload === 'object' && payload !== null) {
      const optimized: any = {};
      
      Object.keys(payload).forEach(key => {
        const value = payload[key];
        if (value !== undefined && value !== null) {
          if (typeof value === 'object' && Object.keys(value).length > 0) {
            optimized[key] = ReduxOptimization.optimizePayload(value);
          } else if (typeof value !== 'object') {
            optimized[key] = value;
          }
        }
      });
      
      return optimized;
    }
    
    return payload;
  },
};

// Initialize memory monitor
export const memoryMonitor = MemoryMonitor.getInstance();

// Start memory monitoring in development
if (__DEV__) {
  setInterval(() => {
    memoryMonitor.recordMemoryUsage();
  }, 5000); // Record every 5 seconds
}
