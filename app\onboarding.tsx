import { IconSymbol } from '@/components/ui/IconSymbol';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import {
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

export default function OnboardingScreen() {

  const handleOptionSelect = (option: 'register' | 'login') => {
    if (option === 'register') {
      router.push('/(auth)/register');
    } else if (option === 'login') {
      router.push('/login');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      <LinearGradient
        colors={['#DC143C', '#B91C1C', '#EF4444']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <IconSymbol name="fork.knife" size={48} color="#FFFFFF" />
          </View>
          <Text style={styles.appName}>FoodWay</Text>
          <Text style={styles.tagline}>Restaurant Management Made Easy</Text>
        </View>

        {/* Welcome Content */}
        <View style={styles.content}>
          <View style={styles.welcomeCard}>
            <Text style={styles.welcomeTitle}>Welcome to FoodWay</Text>
            <Text style={styles.welcomeSubtitle}>
              Manage your restaurant with powerful tools for orders, menu, analytics, and staff management.
            </Text>

            {/* Option Selection */}
            <View style={styles.optionsContainer}>
              <Text style={styles.optionsTitle}>Get Started</Text>
              
              {/* Register Option */}
              <TouchableOpacity
                style={styles.optionCard}
                onPress={() => handleOptionSelect('register')}
                activeOpacity={0.8}
              >
                <View style={styles.optionIcon}>
                  <IconSymbol name="plus.circle" size={32} color="#DC143C" />
                </View>
                <View style={styles.optionContent}>
                  <Text style={styles.optionTitle}>
                    Register New Restaurant
                  </Text>
                  <Text style={styles.optionDescription}>
                    Set up your restaurant profile and start managing your business
                  </Text>
                </View>
                <View style={styles.optionCheck}>
                  <IconSymbol name="arrow-right-circle" size={24} color="#DC143C" />
                </View>
              </TouchableOpacity>

              {/* Login Option */}
              <TouchableOpacity
                style={styles.optionCard}
                onPress={() => handleOptionSelect('login')}
                activeOpacity={0.8}
              >
                <View style={styles.optionIcon}>
                  <IconSymbol name="person.fill" size={32} color="#DC143C" />
                </View>
                <View style={styles.optionContent}>
                  <Text style={styles.optionTitle}>
                    Login to Existing Account
                  </Text>
                  <Text style={styles.optionDescription}>
                    Access your restaurant dashboard as Owner, Admin, or Staff
                  </Text>
                </View>
                <View style={styles.optionCheck}>
                  <IconSymbol name="arrow-right-circle" size={24} color="#DC143C" />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            By continuing, you agree to our Terms of Service and Privacy Policy
          </Text>
        </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 40,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  appName: {
    fontSize: 32,
    fontWeight: '800',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  welcomeCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  optionsContainer: {
    marginBottom: 32,
  },
  optionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  optionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E5E5',
    marginBottom: 12,
    backgroundColor: '#FAFAFA',
  },
  optionCardSelected: {
    borderColor: '#DC143C',
    backgroundColor: '#FEE2E2',
  },
  optionIcon: {
    marginRight: 16,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  optionTitleSelected: {
    color: '#DC143C',
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  optionCheck: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  continueButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  continueButtonDisabled: {
    opacity: 0.5,
  },
  continueButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginRight: 8,
  },
  footer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  footerText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 18,
  },
});
