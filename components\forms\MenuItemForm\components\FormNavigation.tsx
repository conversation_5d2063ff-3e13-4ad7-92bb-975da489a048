import { Colors } from '@/constants/Theme';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

interface FormNavigationProps {
  isFirstStep: boolean;
  isLastStep: boolean;
  canProceed: boolean;
  isSubmitting: boolean;
  onPrevious: () => void;
  onNext: () => void;
  onSubmit: () => void;
  currentStepTitle?: string;
  nextStepTitle?: string;
}

export function FormNavigation({
  isFirstStep,
  isLastStep,
  canProceed,
  isSubmitting,
  onPrevious,
  onNext,
  onSubmit,
  currentStepTitle: _currentStepTitle,
  nextStepTitle: _nextStepTitle,
}: FormNavigationProps) {
  const slideAnimation = useSharedValue(0);
  const buttonScaleAnimation = useSharedValue(1);

  React.useEffect(() => {
    slideAnimation.value = withTiming(1, { duration: 300 });
  }, []);

  const animatedContainerStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: (1 - slideAnimation.value) * 100 }],
    opacity: slideAnimation.value,
  }));

  const animatedButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScaleAnimation.value }],
  }));

  const handleButtonPress = (action: () => void) => {
    buttonScaleAnimation.value = withSpring(0.95, { damping: 15, stiffness: 300 });
    setTimeout(() => {
      buttonScaleAnimation.value = withSpring(1, { damping: 15, stiffness: 300 });
    }, 100);
    action();
  };

  const getPrimaryButtonText = () => {
    if (isSubmitting) return 'Creating...';
    if (isLastStep) return 'Create Menu Item';
    return 'Continue';
  };

  const getPrimaryButtonIcon = () => {
    if (isSubmitting) return null;
    if (isLastStep) return 'checkmark';
    return 'chevron-forward-outline';
  };

  return (
    <Animated.View style={[styles.container, animatedContainerStyle]}>
      {/* Step Info */}
      {/* Navigation Buttons */}
      <View style={styles.buttonsContainer}>
        {/* Previous Button */}
        <TouchableOpacity
          style={[
            styles.secondaryButton,
            isFirstStep && styles.disabledButton,
          ]}
          onPress={() => handleButtonPress(onPrevious)}
          disabled={isFirstStep || isSubmitting}
          activeOpacity={0.7}
        >
          <Ionicons
            name="chevron-back"
            size={16}
            color={
              isFirstStep || isSubmitting
                ? Colors.light.textTertiary
                : Colors.light.text
            }
          />
          <Text
            style={[
              styles.secondaryButtonText,
              (isFirstStep || isSubmitting) && styles.disabledButtonText,
            ]}
          >
            Previous
          </Text>
        </TouchableOpacity>

        {/* Primary Button */}
        <Animated.View style={[styles.primaryButtonContainer, animatedButtonStyle]}>
          <TouchableOpacity
            style={[
              styles.primaryButton,
              !canProceed && styles.disabledPrimaryButton,
              isSubmitting && styles.submittingButton,
            ]}
            onPress={() => {
              if (isLastStep) {
                handleButtonPress(onSubmit);
              } else {
                handleButtonPress(onNext);
              }
            }}
            disabled={!canProceed || isSubmitting}
            activeOpacity={0.8}
          >
            {isSubmitting ? (
              <ActivityIndicator
                size="small"
                color={Colors.light.textInverse}
                style={styles.loadingIndicator}
              />
            ) : (
              getPrimaryButtonIcon() && (
                <Ionicons
                  name={getPrimaryButtonIcon()!}
                  size={16}
                  color={Colors.light.textInverse}
                  style={styles.buttonIcon}
                />
              )
            )}
            <Text style={styles.primaryButtonText}>
              {getPrimaryButtonText()}
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Progress Hint */}
      {!canProceed && !isSubmitting && (
        <View style={styles.hintContainer}>
          <Ionicons
            name="information-circle-outline"
            size={14}
            color={Colors.light.warning}
          />
          <Text style={styles.hintText}>
            Complete all required fields to continue
          </Text>
        </View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 12, // Reduced from 16
    paddingBottom: 6, // Reduced from 8
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  stepInfo: {
    alignItems: 'center',
    marginBottom: 12, // Reduced from 16
  },
  currentStep: {
    fontSize: 14, // Reduced from 16
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'center',
  },
  nextStep: {
    fontSize: 12, // Reduced from 14
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginTop: 3, // Reduced from 4
  },
  buttonsContainer: {
    flexDirection: 'row',
    gap: 10, // Reduced from 12
    paddingHorizontal: 2, // Reduced from 4
  },
  secondaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12, // Reduced from 14
    paddingHorizontal: 16, // Reduced from 20
    borderRadius: 8, // Reduced from 12
    backgroundColor: Colors.light.backgroundSecondary,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  disabledButton: {
    opacity: 0.5,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 8,
  },
  disabledButtonText: {
    color: Colors.light.textTertiary,
  },
  primaryButtonContainer: {
    flex: 2,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: Colors.light.primary,
    elevation: 2,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  disabledPrimaryButton: {
    backgroundColor: Colors.light.textTertiary,
    elevation: 0,
    shadowOpacity: 0,
  },
  submittingButton: {
    backgroundColor: Colors.light.primaryDark,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
  },
  buttonIcon: {
    marginRight: 8,
  },
  loadingIndicator: {
    marginRight: 8,
  },
  hintContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    paddingHorizontal: 16,
  },
  hintText: {
    fontSize: 13,
    color: Colors.light.warning,
    marginLeft: 6,
    textAlign: 'center',
  },
});

export default FormNavigation;
