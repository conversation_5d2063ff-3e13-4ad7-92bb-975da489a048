import { useState, useEffect, useCallback } from 'react';

// Generic API hook for handling loading states and errors
export function useApi<T>(
  apiFunction: () => Promise<{ success: boolean; data: T; message?: string }>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiFunction();
      
      if (response.success) {
        setData(response.data);
      } else {
        setError(response.message || 'An error occurred');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch
  };
}

// Hook for API mutations (create, update, delete)
export function useApiMutation<TData, TVariables = void>(
  apiFunction: (variables: TVariables) => Promise<{ success: boolean; data: TData; message?: string }>
) {
  const [data, setData] = useState<TData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mutate = useCallback(async (variables: TVariables) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiFunction(variables);
      
      if (response.success) {
        setData(response.data);
        return { success: true, data: response.data };
      } else {
        const errorMessage = response.message || 'An error occurred';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [apiFunction]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return {
    data,
    loading,
    error,
    mutate,
    reset
  };
}

// Hook for paginated API calls
export function usePaginatedApi<T>(
  apiFunction: (page: number, limit: number) => Promise<{
    success: boolean;
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    message?: string;
  }>,
  initialLimit: number = 20
) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchData = useCallback(async (pageNum: number, isRefresh: boolean = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const response = await apiFunction(pageNum, initialLimit);
      
      if (response.success) {
        if (pageNum === 1 || isRefresh) {
          setData(response.data);
        } else {
          setData(prev => [...prev, ...response.data]);
        }
        
        setHasMore(pageNum < response.pagination.totalPages);
        setPage(pageNum);
      } else {
        setError(response.message || 'An error occurred');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [apiFunction, initialLimit]);

  useEffect(() => {
    fetchData(1);
  }, [fetchData]);

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchData(page + 1);
    }
  }, [fetchData, loading, hasMore, page]);

  const refresh = useCallback(() => {
    fetchData(1, true);
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    hasMore,
    refreshing,
    loadMore,
    refresh
  };
}

// Hook for real-time data with polling
export function usePollingApi<T>(
  apiFunction: () => Promise<{ success: boolean; data: T; message?: string }>,
  interval: number = 30000, // 30 seconds default
  enabled: boolean = true
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setError(null);
      const response = await apiFunction();
      
      if (response.success) {
        setData(response.data);
      } else {
        setError(response.message || 'An error occurred');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [apiFunction]);

  useEffect(() => {
    if (!enabled) return;

    fetchData();
    const intervalId = setInterval(fetchData, interval);

    return () => clearInterval(intervalId);
  }, [fetchData, interval, enabled]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}

// Hook for optimistic updates
export function useOptimisticApi<T>(
  initialData: T[],
  apiFunction: (item: T) => Promise<{ success: boolean; data: T; message?: string }>
) {
  const [data, setData] = useState<T[]>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const optimisticUpdate = useCallback(async (
    item: T,
    optimisticUpdateFn: (items: T[]) => T[]
  ) => {
    // Apply optimistic update immediately
    const previousData = data;
    setData(optimisticUpdateFn(data));
    setLoading(true);
    setError(null);

    try {
      const response = await apiFunction(item);
      
      if (response.success) {
        // Update with real data from server
        setData(prev => prev.map(prevItem => 
          (prevItem as any).id === (response.data as any).id ? response.data : prevItem
        ));
      } else {
        // Revert optimistic update on failure
        setData(previousData);
        setError(response.message || 'An error occurred');
      }
    } catch (err) {
      // Revert optimistic update on error
      setData(previousData);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [data, apiFunction]);

  return {
    data,
    loading,
    error,
    optimisticUpdate,
    setData
  };
}
