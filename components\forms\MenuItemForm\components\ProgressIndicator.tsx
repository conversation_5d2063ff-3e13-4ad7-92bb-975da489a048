import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import React from 'react';
import {
  Dimensions,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { FormStep } from '../types';

const { width: screenWidth } = Dimensions.get('window');

interface ProgressIndicatorProps {
  steps: FormStep[];
  currentStep: number;
  onStepPress: (stepIndex: number) => void;
}

export function ProgressIndicator({
  steps,
  currentStep,
  onStepPress,
}: ProgressIndicatorProps) {
  const progressAnimation = useSharedValue(0);
  const scrollViewRef = React.useRef<ScrollView>(null);

  React.useEffect(() => {
    progressAnimation.value = withTiming(currentStep, { duration: 500 });
    
    // Auto-scroll to current step
    if (scrollViewRef.current) {
      const stepWidth = 120;
      const scrollPosition = Math.max(0, currentStep * stepWidth - screenWidth / 2 + stepWidth / 2);
      scrollViewRef.current.scrollTo({ x: scrollPosition, animated: true });
    }
  }, [currentStep, progressAnimation]);

  const animatedProgressStyle = useAnimatedStyle(() => {
    const progressWidth = interpolate(
      progressAnimation.value,
      [0, steps.length - 1],
      [0, 100],
      'clamp'
    );
    
    return {
      width: `${progressWidth}%`,
    };
  });

  const getStepStatus = (step: FormStep, index: number) => {
    if (index < currentStep) {
      return step.isCompleted ? 'completed' : 'visited';
    } else if (index === currentStep) {
      return 'current';
    } else {
      return 'upcoming';
    }
  };

  const getStepIcon = (step: FormStep, status: string) => {
    switch (status) {
      case 'completed':
        return 'checkmark';
      case 'visited':
        return step.errors.length > 0 ? 'exclamationmark.triangle' : 'circle';
      case 'current':
        return step.icon;
      case 'upcoming':
        return 'circle';
      default:
        return 'circle';
    }
  };

  const getStepColor = (status: string, hasErrors: boolean) => {
    switch (status) {
      case 'completed':
        return Colors.light.success;
      case 'visited':
        return hasErrors ? Colors.light.error : Colors.light.warning;
      case 'current':
        return Colors.light.primary;
      case 'upcoming':
        return Colors.light.textTertiary;
      default:
        return Colors.light.textTertiary;
    }
  };

  return (
    <View style={styles.container}>
      {/* Overall Progress Bar */}
      <View style={styles.overallProgressContainer}>
        <View style={styles.overallProgressTrack}>
          <Animated.View style={[styles.overallProgressFill, animatedProgressStyle]} />
        </View>
        <Text style={styles.overallProgressText}>
          {currentStep + 1} of {steps.length}
        </Text>
      </View>

      {/* Step Indicators */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.stepsContainer}
        style={styles.stepsScrollView}
      >
        {steps.map((step, index) => {
          const status = getStepStatus(step, index);
          const hasErrors = step.errors.length > 0;
          const stepColor = getStepColor(status, hasErrors);
          // More restrictive clickable logic - only allow clicking on current step or previous completed steps
          const isClickable = index <= currentStep && (index === currentStep || step.isCompleted);

          return (
            <StepIndicator
              key={step.id}
              step={step}
              index={index}
              status={status}
              color={stepColor}
              icon={getStepIcon(step, status)}
              isClickable={isClickable}
              onPress={() => isClickable && onStepPress(index)}
            />
          );
        })}
      </ScrollView>
    </View>
  );
}

interface StepIndicatorProps {
  step: FormStep;
  index: number;
  status: string;
  color: string;
  icon: string;
  isClickable: boolean;
  onPress: () => void;
}

function StepIndicator({
  step,
  index: _index, // Renamed to indicate it's intentionally unused
  status,
  color,
  icon,
  isClickable,
  onPress,
}: StepIndicatorProps) {
  const scaleAnimation = useSharedValue(1);
  const pulseAnimation = useSharedValue(1);

  React.useEffect(() => {
    if (status === 'current') {
      // Pulse animation for current step
      const pulse = () => {
        pulseAnimation.value = withSpring(1.1, { damping: 15, stiffness: 200 });
        setTimeout(() => {
          pulseAnimation.value = withSpring(1, { damping: 15, stiffness: 200 });
        }, 1000);
      };
      
      pulse();
      const interval = setInterval(pulse, 2000);
      return () => clearInterval(interval);
    }
  }, [status, pulseAnimation]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scaleAnimation.value * pulseAnimation.value },
    ],
  }));

  const handlePress = () => {
    if (!isClickable) return;
    
    scaleAnimation.value = withSpring(0.9, { damping: 15, stiffness: 300 });
    setTimeout(() => {
      scaleAnimation.value = withSpring(1, { damping: 15, stiffness: 300 });
    }, 100);
    
    onPress();
  };

  return (
    <TouchableOpacity
      style={styles.stepContainer}
      onPress={handlePress}
      disabled={!isClickable}
      activeOpacity={isClickable ? 0.7 : 1}
    >
      <Animated.View style={[styles.stepIndicator, animatedStyle]}>
        <View
          style={[
            styles.stepCircle,
            { backgroundColor: color },
            status === 'current' && styles.currentStepCircle,
            !isClickable && styles.disabledStepCircle,
          ]}
        >
          <IconSymbol
            name={icon}
            size={status === 'current' ? 20 : 16}
            color={
              status === 'upcoming' && !isClickable
                ? Colors.light.textTertiary
                : Colors.light.textInverse
            }
          />
        </View>
        
        {/* Progress Ring for Current Step */}
        {status === 'current' && (
          <View style={styles.progressRing}>
            <View
              style={[
                styles.progressRingFill,
                { 
                  transform: [{ 
                    rotate: `${(step.progress / 100) * 360}deg` 
                  }] 
                },
              ]}
            />
          </View>
        )}
      </Animated.View>

      <Text
        style={[
          styles.stepTitle,
          { color },
          status === 'current' && styles.currentStepTitle,
          !isClickable && styles.disabledStepTitle,
        ]}
        numberOfLines={2}
      >
        {step.title}
      </Text>

      {/* Error Indicator */}
      {step.errors.length > 0 && (
        <View style={styles.errorBadge}>
          <Text style={styles.errorBadgeText}>{step.errors.length}</Text>
        </View>
      )}

      {/* Completion Indicator */}
      {status === 'completed' && (
        <View style={styles.completionBadge}>
          <IconSymbol
            name="checkmark"
            size={12}
            color={Colors.light.textInverse}
          />
        </View>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12, // Reduced from 16
  },
  overallProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12, // Reduced from 16
    paddingHorizontal: 2, // Reduced from 4
  },
  overallProgressTrack: {
    flex: 1,
    height: 3, // Reduced from 4
    backgroundColor: Colors.light.border,
    borderRadius: 2,
    marginRight: 8, // Reduced from 12
  },
  overallProgressFill: {
    height: '100%',
    backgroundColor: Colors.light.primary,
    borderRadius: 2,
  },
  overallProgressText: {
    fontSize: 11, // Reduced from 12
    fontWeight: '600',
    color: Colors.light.textSecondary,
    minWidth: 45, // Reduced from 50
    textAlign: 'right',
  },
  stepsScrollView: {
    flexGrow: 0,
  },
  stepsContainer: {
    paddingHorizontal: 6, // Reduced from 8
    alignItems: 'center',
  },
  stepContainer: {
    alignItems: 'center',
    marginHorizontal: 6, // Reduced from 8
    width: 70, // Reduced from 80
    position: 'relative',
  },
  stepIndicator: {
    position: 'relative',
    marginBottom: 8,
  },
  stepCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  currentStepCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    elevation: 4,
    shadowOpacity: 0.2,
  },
  disabledStepCircle: {
    elevation: 0,
    shadowOpacity: 0,
  },
  progressRing: {
    position: 'absolute',
    top: -2,
    left: -2,
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  progressRingFill: {
    position: 'absolute',
    top: -2,
    left: -2,
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  stepTitle: {
    fontSize: 11,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 14,
  },
  currentStepTitle: {
    fontWeight: 'bold',
  },
  disabledStepTitle: {
    opacity: 0.5,
  },
  errorBadge: {
    position: 'absolute',
    top: -4,
    right: 8,
    backgroundColor: Colors.light.error,
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.light.background,
  },
  errorBadgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
  },
  completionBadge: {
    position: 'absolute',
    top: -4,
    right: 8,
    backgroundColor: Colors.light.success,
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.light.background,
  },
});

export default ProgressIndicator;
