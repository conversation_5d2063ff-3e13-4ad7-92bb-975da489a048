import AsyncStorage from '@react-native-async-storage/async-storage';
import React from 'react';
import { InteractionManager } from 'react-native';
import { assetOptimizer } from './assetOptimization';
import { performanceMonitor } from './performance';

// Startup optimization configuration
interface StartupConfig {
  preloadAssets?: string[];
  preloadScreens?: string[];
  enableCaching?: boolean;
  enablePerformanceMonitoring?: boolean;
  criticalDataKeys?: string[];
}

export class StartupOptimizer {
  private static instance: StartupOptimizer;
  private isInitialized = false;
  private startupTime = 0;
  private criticalDataCache = new Map<string, any>();

  static getInstance(): StartupOptimizer {
    if (!StartupOptimizer.instance) {
      StartupOptimizer.instance = new StartupOptimizer();
    }
    return StartupOptimizer.instance;
  }

  // Initialize app with optimizations
  async initialize(config: StartupConfig = {}): Promise<void> {
    if (this.isInitialized) return;

    const startTime = Date.now();
    this.startupTime = startTime;

    try {
      // Start performance monitoring
      if (config.enablePerformanceMonitoring) {
        performanceMonitor.startTiming('app_initialization');
      }

      // Run critical initializations in parallel
      await Promise.all([
        this.initializeAssetOptimizer(config),
        this.preloadCriticalData(config.criticalDataKeys || []),
        this.initializeCache(config.enableCaching),
      ]);

      // Run non-critical initializations after interactions
      InteractionManager.runAfterInteractions(() => {
        this.initializeNonCritical(config);
      });

      this.isInitialized = true;

      if (config.enablePerformanceMonitoring) {
        performanceMonitor.endTiming('app_initialization', 'screenLoadTime');
        performanceMonitor.logMetrics('app_initialization');
      }

      const endTime = Date.now();
      console.log(`App initialized in ${endTime - startTime}ms`);

    } catch (error) {
      console.error('Startup initialization failed:', error);
    }
  }

  // Initialize asset optimizer
  private async initializeAssetOptimizer(config: StartupConfig): Promise<void> {
    await assetOptimizer.initialize();

    if (config.preloadAssets && config.preloadAssets.length > 0) {
      // Preload critical assets
      await assetOptimizer.preloadCriticalAssets(config.preloadAssets);
    }
  }

  // Preload critical data from AsyncStorage
  private async preloadCriticalData(keys: string[]): Promise<void> {
    if (keys.length === 0) return;

    try {
      const values = await AsyncStorage.multiGet(keys);
      
      values.forEach(([key, value]) => {
        if (value) {
          try {
            this.criticalDataCache.set(key, JSON.parse(value));
          } catch {
            this.criticalDataCache.set(key, value);
          }
        }
      });
    } catch (error) {
      console.warn('Failed to preload critical data:', error);
    }
  }

  // Initialize caching systems
  private async initializeCache(enableCaching?: boolean): Promise<void> {
    if (!enableCaching) return;

    try {
      // Initialize various cache systems
      await Promise.all([
        this.initializeAPICache(),
        this.initializeImageCache(),
      ]);
    } catch (error) {
      console.warn('Cache initialization failed:', error);
    }
  }

  // Initialize API cache
  private async initializeAPICache(): Promise<void> {
    // API cache is initialized in apiOptimization.ts
    console.log('API cache initialized');
  }

  // Initialize image cache
  private async initializeImageCache(): Promise<void> {
    // Image cache is initialized in assetOptimization.ts
    console.log('Image cache initialized');
  }

  // Initialize non-critical features
  private async initializeNonCritical(config: StartupConfig): Promise<void> {
    try {
      // Preload screens
      if (config.preloadScreens && config.preloadScreens.length > 0) {
        // Screen preloading would be implemented here
        console.log('Preloading screens:', config.preloadScreens);
      }

      // Initialize analytics
      this.initializeAnalytics();

      // Clean up old cache data
      this.cleanupOldData();

    } catch (error) {
      console.warn('Non-critical initialization failed:', error);
    }
  }

  // Initialize analytics
  private initializeAnalytics(): void {
    // Analytics initialization would go here
    console.log('Analytics initialized');
  }

  // Clean up old data
  private async cleanupOldData(): Promise<void> {
    try {
      // Clean up expired cache entries
      const keys = await AsyncStorage.getAllKeys();
      const expiredKeys: string[] = [];

      for (const key of keys) {
        if (key.includes('_cache_') || key.includes('_temp_')) {
          const data = await AsyncStorage.getItem(key);
          if (data) {
            try {
              const parsed = JSON.parse(data);
              if (parsed.expiryTime && Date.now() > parsed.expiryTime) {
                expiredKeys.push(key);
              }
            } catch {
              // If parsing fails, consider it expired
              expiredKeys.push(key);
            }
          }
        }
      }

      if (expiredKeys.length > 0) {
        await AsyncStorage.multiRemove(expiredKeys);
        console.log(`Cleaned up ${expiredKeys.length} expired cache entries`);
      }
    } catch (error) {
      console.warn('Cache cleanup failed:', error);
    }
  }

  // Get cached critical data
  getCriticalData(key: string): any {
    return this.criticalDataCache.get(key);
  }

  // Get startup statistics
  getStartupStats(): {
    isInitialized: boolean;
    startupTime: number;
    criticalDataCount: number;
  } {
    return {
      isInitialized: this.isInitialized,
      startupTime: this.startupTime,
      criticalDataCount: this.criticalDataCache.size,
    };
  }

  // Force re-initialization
  async reinitialize(config: StartupConfig = {}): Promise<void> {
    this.isInitialized = false;
    this.criticalDataCache.clear();
    await this.initialize(config);
  }
}

// App startup configuration
export const DEFAULT_STARTUP_CONFIG: StartupConfig = {
  preloadAssets: [
    // Add critical image assets here
  ],
  preloadScreens: [
    'login',
    'dashboard',
    'orders',
  ],
  enableCaching: true,
  enablePerformanceMonitoring: __DEV__,
  criticalDataKeys: [
    'user_preferences',
    'auth_token',
    'restaurant_settings',
    'app_config',
  ],
};

// Startup performance utilities
export const StartupUtils = {
  // Measure app launch time
  measureLaunchTime: () => {
    const launchTime = Date.now();
    return {
      end: () => {
        const endTime = Date.now();
        const duration = endTime - launchTime;
        console.log(`App launch completed in ${duration}ms`);
        return duration;
      },
    };
  },

  // Optimize font loading
  optimizeFontLoading: (fonts: Record<string, any>) => {
    // Only load essential fonts during startup
    const essentialFonts = Object.entries(fonts).reduce((acc, [key, value]) => {
      if (key.includes('Regular') || key.includes('Medium')) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);

    return essentialFonts;
  },

  // Defer non-critical operations
  deferOperation: (operation: () => void, delay = 1000) => {
    setTimeout(() => {
      InteractionManager.runAfterInteractions(operation);
    }, delay);
  },

  // Batch AsyncStorage operations
  batchStorageOperations: async (operations: Array<() => Promise<any>>) => {
    const batchSize = 5;
    const results = [];

    for (let i = 0; i < operations.length; i += batchSize) {
      const batch = operations.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch.map(op => op()));
      results.push(...batchResults);
    }

    return results;
  },
};

// Export singleton
export const startupOptimizer = StartupOptimizer.getInstance();

// Hook for startup optimization
export const useStartupOptimization = (config: StartupConfig = DEFAULT_STARTUP_CONFIG) => {
  const [isReady, setIsReady] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    const initializeApp = async () => {
      try {
        await startupOptimizer.initialize(config);
        setIsReady(true);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Startup failed'));
      }
    };

    initializeApp();
  }, []);

  return { isReady, error };
};
