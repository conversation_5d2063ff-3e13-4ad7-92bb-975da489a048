import api, { ApiResponse, PaginatedResponse, handleApiError } from './api';

// Order types
export interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  deliveryFee: number;
  discount: number;
  total: number;
  status: OrderStatus;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: 'cash' | 'card' | 'online';
  orderType: 'delivery' | 'pickup' | 'dine-in';
  deliveryAddress?: DeliveryAddress;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  specialInstructions?: string;
  restaurantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  price: number;
  quantity: number;
  customizations: Array<{
    name: string;
    options: string[];
    additionalPrice: number;
  }>;
  specialInstructions?: string;
  total: number;
}

export interface DeliveryAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'preparing'
  | 'ready'
  | 'out-for-delivery'
  | 'delivered'
  | 'cancelled'
  | 'refunded';

export interface OrderFilters {
  status?: OrderStatus[];
  orderType?: ('delivery' | 'pickup' | 'dine-in')[];
  paymentStatus?: ('pending' | 'paid' | 'failed' | 'refunded')[];
  dateFrom?: string;
  dateTo?: string;
  customerId?: string;
}

export interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  averagePreparationTime: number;
}

class OrdersService {
  // Get orders with pagination and filters
  async getOrders(
    page: number = 1,
    limit: number = 20,
    filters?: OrderFilters
  ): Promise<PaginatedResponse<Order>> {
    try {
      const params = {
        page,
        limit,
        ...filters
      };
      
      const response = await api.get<PaginatedResponse<Order>>('/orders', { params });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get single order
  async getOrder(id: string): Promise<ApiResponse<Order>> {
    try {
      const response = await api.get<ApiResponse<Order>>(`/orders/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update order status
  async updateOrderStatus(id: string, status: OrderStatus): Promise<ApiResponse<Order>> {
    try {
      const response = await api.patch<ApiResponse<Order>>(`/orders/${id}/status`, { status });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Update estimated delivery time
  async updateEstimatedDeliveryTime(id: string, estimatedTime: string): Promise<ApiResponse<Order>> {
    try {
      const response = await api.patch<ApiResponse<Order>>(`/orders/${id}/delivery-time`, { 
        estimatedDeliveryTime: estimatedTime 
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Cancel order
  async cancelOrder(id: string, reason: string): Promise<ApiResponse<Order>> {
    try {
      const response = await api.patch<ApiResponse<Order>>(`/orders/${id}/cancel`, { reason });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Refund order
  async refundOrder(id: string, amount?: number, reason?: string): Promise<ApiResponse<Order>> {
    try {
      const response = await api.patch<ApiResponse<Order>>(`/orders/${id}/refund`, { 
        amount, 
        reason 
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get order statistics
  async getOrderStats(period?: 'today' | 'week' | 'month' | 'year'): Promise<ApiResponse<OrderStats>> {
    try {
      const params = period ? { period } : {};
      const response = await api.get<ApiResponse<OrderStats>>('/orders/stats', { params });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get real-time orders (pending/preparing)
  async getActiveOrders(): Promise<ApiResponse<Order[]>> {
    try {
      const response = await api.get<ApiResponse<Order[]>>('/orders/active');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Search orders
  async searchOrders(query: string): Promise<ApiResponse<Order[]>> {
    try {
      const response = await api.get<ApiResponse<Order[]>>('/orders/search', {
        params: { q: query }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get orders by customer
  async getOrdersByCustomer(customerId: string): Promise<ApiResponse<Order[]>> {
    try {
      const response = await api.get<ApiResponse<Order[]>>(`/orders/customer/${customerId}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Bulk update orders
  async bulkUpdateOrders(
    orderIds: string[], 
    updates: { status?: OrderStatus; estimatedDeliveryTime?: string }
  ): Promise<ApiResponse<{ updated: number }>> {
    try {
      const response = await api.patch<ApiResponse<{ updated: number }>>('/orders/bulk-update', {
        orderIds,
        updates
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Get order analytics
  async getOrderAnalytics(
    startDate: string,
    endDate: string,
    groupBy: 'hour' | 'day' | 'week' | 'month' = 'day'
  ): Promise<ApiResponse<Array<{
    date: string;
    orders: number;
    revenue: number;
    averageOrderValue: number;
  }>>> {
    try {
      const response = await api.get<ApiResponse<Array<{
        date: string;
        orders: number;
        revenue: number;
        averageOrderValue: number;
      }>>>('/orders/analytics', {
        params: { startDate, endDate, groupBy }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Print order receipt
  async printOrder(id: string): Promise<ApiResponse<{ receiptUrl: string }>> {
    try {
      const response = await api.post<ApiResponse<{ receiptUrl: string }>>(`/orders/${id}/print`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export default new OrdersService();
