import { GradientCard } from '@/components/profile/AnimatedCounter';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { PremiumColors } from '@/constants/PremiumTheme';
import { useAuth } from '@/contexts/AuthContext';
import { mockRestaurantProfile } from '@/data/mockProfileData';
import { GeneratedUser, mockUsers } from '@/types/auth';
import { RestaurantProfile } from '@/types/profile';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { useState } from 'react';
import {
    Alert,
    Image,
    RefreshControl,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import Animated, {
    interpolate,
    useAnimatedStyle,
    useSharedValue
} from 'react-native-reanimated';



const HEADER_HEIGHT = 300;

export default function ProfileScreen() {
  // Use mock data for demonstration
  const profileData: RestaurantProfile = mockRestaurantProfile;

  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedProfile, setEditedProfile] = useState<RestaurantProfile>(profileData);
  const [generatedUsers, setGeneratedUsers] = useState<GeneratedUser[]>([]);
  const scrollY = useSharedValue(0);
  const headerOpacity = useSharedValue(1);
  const profileImageScale = useSharedValue(1);

  // Auth context for logout functionality
  const { logout, user } = useAuth();
  
  const tabs = [
    { id: 'overview', title: 'Overview', icon: 'house.fill' as const },
    { id: 'analytics', title: 'Analytics', icon: 'chart.bar.fill' as const },
    { id: 'team', title: 'Team', icon: 'person.2.fill' as const },
  ];

  const handleEditProfile = () => {
    setIsEditing(true);
  };



  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            logout();
          },
        },
      ]
    );
  };

  const handleProfileImageUpload = () => {
    Alert.alert(
      'Update Profile Picture',
      'Choose an option to update your restaurant profile picture',
      [
        {
          text: 'Camera',
          onPress: () => {
            // Here you would implement camera functionality
            Alert.alert('Camera', 'Camera functionality will be implemented soon!');
          }
        },
        {
          text: 'Gallery',
          onPress: () => {
            // Here you would implement gallery selection
            Alert.alert('Gallery', 'Gallery selection will be implemented soon!');
          }
        },
        {
          text: 'Cancel',
          style: 'cancel'
        }
      ]
    );
  };



  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: headerOpacity.value,
      transform: [
        {
          translateY: interpolate(
            scrollY.value,
            [0, HEADER_HEIGHT],
            [0, -HEADER_HEIGHT / 2]
          ),
        },
      ],
    };
  });

  const profileImageAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: profileImageScale.value },
        {
          translateY: interpolate(
            scrollY.value,
            [0, HEADER_HEIGHT / 2],
            [0, -20]
          ),
        },
      ],
    };
  });



  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderHeroSection = () => (
    <Animated.View style={[styles.heroContainer, headerAnimatedStyle]}>
      {/* Cover Photo with Parallax */}
      <View style={styles.coverPhotoContainer}>
        <Image
          source={{ uri: profileData.coverPhoto }}
          style={styles.coverPhoto}
          resizeMode="cover"
        />
        <LinearGradient
          colors={['transparent', '#DC143C80', '#DC143C']}
          style={styles.coverGradient}
        />
        {/* Orange decorative bar */}
        <View style={styles.decorativeBar} />
      </View>



      {/* Restaurant Info - New Layout */}
      <View style={styles.heroContent}>
        <View style={styles.profileInfoContainer}>
          {/* Profile Image on Left */}
          <TouchableOpacity style={styles.profileImageSection} onPress={handleProfileImageUpload}>
            <View style={styles.uploadableProfileRing}>
              <Image
                source={{ uri: profileData.profilePicture }}
                style={styles.uploadableProfileImage}
              />
              <View style={styles.uploadOverlay}>
                <IconSymbol name="camera" size={20} color="#FFFFFF" />
              </View>
            </View>
          </TouchableOpacity>

          {/* Restaurant Name and Tagline on Right */}
          <View style={styles.restaurantInfoSection}>
            <Text style={styles.restaurantName}>{profileData.name}</Text>
            <Text style={styles.restaurantTagline}>{profileData.tagline}</Text>
          </View>
        </View>
      </View>


    </Animated.View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="transparent" translucent={false} />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderHeroSection()}

        {/* Tabbed Interface */}
        <View style={styles.tabsContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabsScrollView}>
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[
                  styles.tab,
                  activeTab === tab.id && styles.activeTab,
                ]}
                onPress={() => setActiveTab(tab.id)}
              >
                <IconSymbol
                  name={tab.icon}
                  size={20}
                  color={activeTab === tab.id ? '#FFFFFF' : '#6B7280'}
                />
                <Text
                  style={[
                    styles.tabText,
                    activeTab === tab.id && styles.activeTabText,
                  ]}
                >
                  {tab.title}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Tab Content */}
        <View style={styles.tabContent}>
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'analytics' && renderAnalyticsTab()}
          {activeTab === 'team' && renderTeamTab()}
        </View>

        {/* Logout Button - Only for Staff */}
        {user?.role === 'staff' && (
          <View style={styles.logoutContainer}>
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <LinearGradient
                colors={['#DC143C', '#B91C1C']}
                style={styles.logoutGradient}
              >
                <IconSymbol name="arrow.right.square" size={20} color="#FFFFFF" />
                <Text style={styles.logoutButtonText}>Logout</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );

  // Tab Content Renderers
  function renderOverviewTab() {
    return (
      <View style={styles.overviewContainer}>
        {/* Restaurant Description */}
        <GradientCard style={styles.descriptionCard}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>About Us</Text>
            {!isEditing && (
              <TouchableOpacity style={styles.editButton} onPress={handleEditProfile}>
                <IconSymbol name="pencil" size={16} color="#DC143C" />
              </TouchableOpacity>
            )}
          </View>

          {isEditing ? (
            <View style={styles.editForm}>
              <Text style={styles.editLabel}>Restaurant Name</Text>
              <TextInput
                style={styles.editInput}
                value={editedProfile.name}
                onChangeText={(text) => setEditedProfile({...editedProfile, name: text})}
                placeholder="Enter restaurant name"
              />

              <Text style={styles.editLabel}>Tagline</Text>
              <TextInput
                style={styles.editInput}
                value={editedProfile.tagline}
                onChangeText={(text) => setEditedProfile({...editedProfile, tagline: text})}
                placeholder="Enter restaurant tagline"
              />

              <Text style={styles.editLabel}>Description</Text>
              <TextInput
                style={[styles.editInput, styles.editTextArea]}
                value={editedProfile.description}
                onChangeText={(text) => setEditedProfile({...editedProfile, description: text})}
                placeholder="Enter restaurant description"
                multiline
                numberOfLines={4}
              />
            </View>
          ) : (
            <Text style={styles.description}>{profileData.description}</Text>
          )}

          {/* Cuisine Types */}
          <View style={styles.cuisineContainer}>
            {profileData.cuisineTypes.map((cuisine) => (
              <View key={cuisine.id} style={[styles.cuisineTag, { backgroundColor: cuisine.color }]}>
                <Text style={styles.cuisineEmoji}>{cuisine.icon}</Text>
                <Text style={styles.cuisineText}>{cuisine.name}</Text>
              </View>
            ))}
          </View>
        </GradientCard>

        {/* Contact Information */}
        <GradientCard style={styles.contactCard}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Contact Information</Text>
            {!isEditing && (
              <TouchableOpacity style={styles.editButton} onPress={handleEditProfile}>
                <IconSymbol name="pencil" size={16} color="#DC143C" />
              </TouchableOpacity>
            )}
          </View>

          {isEditing ? (
            <View style={styles.editForm}>
              <Text style={styles.editLabel}>Primary Phone</Text>
              <TextInput
                style={styles.editInput}
                value={editedProfile.contact.primaryPhone}
                onChangeText={(text) => setEditedProfile({
                  ...editedProfile,
                  contact: {...editedProfile.contact, primaryPhone: text}
                })}
                placeholder="Enter phone number"
                keyboardType="phone-pad"
              />

              <Text style={styles.editLabel}>Email Address</Text>
              <TextInput
                style={styles.editInput}
                value={editedProfile.contact.email}
                onChangeText={(text) => setEditedProfile({
                  ...editedProfile,
                  contact: {...editedProfile.contact, email: text}
                })}
                placeholder="Enter email address"
                keyboardType="email-address"
                autoCapitalize="none"
              />

              <Text style={styles.editLabel}>Address</Text>
              <TextInput
                style={[styles.editInput, styles.editTextArea]}
                value={editedProfile.location.address}
                onChangeText={(text) => setEditedProfile({
                  ...editedProfile,
                  location: {...editedProfile.location, address: text}
                })}
                placeholder="Enter restaurant address"
                multiline
                numberOfLines={3}
              />
            </View>
          ) : (
            <>
              <TouchableOpacity style={styles.contactItem}>
                <IconSymbol name="phone.fill" size={20} color="#DC143C" />
                <Text style={styles.contactText}>{profileData.contact.primaryPhone}</Text>
                <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
              </TouchableOpacity>

              <TouchableOpacity style={styles.contactItem}>
                <IconSymbol name="envelope.fill" size={20} color="#DC143C" />
                <Text style={styles.contactText}>{profileData.contact.email}</Text>
                <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
              </TouchableOpacity>

              <TouchableOpacity style={styles.contactItem}>
                <IconSymbol name="location.fill" size={20} color="#DC143C" />
                <Text style={styles.contactText}>{profileData.location.address}</Text>
                <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
              </TouchableOpacity>
            </>
          )}
        </GradientCard>
      </View>
    );
  }



  function renderAnalyticsTab() {
    return (
      <View style={styles.analyticsContainer}>
        {/* Revenue Overview */}
        <GradientCard style={styles.analyticsCard}>
          <Text style={styles.cardTitle}>Revenue Overview</Text>
          <View style={styles.revenueGrid}>
            <View style={styles.revenueItem}>
              <Text style={styles.revenueValue}>PKR 45,230</Text>
              <Text style={styles.revenueLabel}>Today</Text>
              <View style={styles.changeIndicator}>
                <IconSymbol name="trending-up" size={12} color="#DC143C" />
                <Text style={styles.changeText}>+12%</Text>
              </View>
            </View>
            <View style={styles.revenueItem}>
              <Text style={styles.revenueValue}>PKR 312,450</Text>
              <Text style={styles.revenueLabel}>This Week</Text>
              <View style={styles.changeIndicator}>
                <IconSymbol name="trending-up" size={12} color="#DC143C" />
                <Text style={styles.changeText}>+8%</Text>
              </View>
            </View>
            <View style={styles.revenueItem}>
              <Text style={styles.revenueValue}>PKR 1,245,680</Text>
              <Text style={styles.revenueLabel}>This Month</Text>
              <View style={styles.changeIndicator}>
                <IconSymbol name="trending-down" size={12} color="#B91C1C" />
                <Text style={styles.changeTextNegative}>-3%</Text>
              </View>
            </View>
            <View style={styles.revenueItem}>
              <Text style={styles.revenueValue}>PKR 14,567,890</Text>
              <Text style={styles.revenueLabel}>This Year</Text>
              <View style={styles.changeIndicator}>
                <IconSymbol name="trending-up" size={12} color="#DC143C" />
                <Text style={styles.changeText}>+15%</Text>
              </View>
            </View>
          </View>
        </GradientCard>

        {/* Order Statistics */}
        <GradientCard style={styles.analyticsCard}>
          <Text style={styles.cardTitle}>Order Statistics</Text>
          <View style={styles.statsGrid}>
            <View style={styles.analyticsStatItem}>
              <IconSymbol name="receipt" size={24} color={PremiumColors.primary} />
              <Text style={styles.analyticsStatValue}>1,234</Text>
              <Text style={styles.analyticsStatLabel}>Total Orders</Text>
            </View>
            <View style={styles.analyticsStatItem}>
              <IconSymbol name="clock" size={24} color={PremiumColors.primaryLight} />
              <Text style={styles.analyticsStatValue}>18 min</Text>
              <Text style={styles.analyticsStatLabel}>Avg Prep Time</Text>
            </View>
            <View style={styles.analyticsStatItem}>
              <IconSymbol name="star" size={24} color="#DC143C" />
              <Text style={styles.analyticsStatValue}>4.8</Text>
              <Text style={styles.analyticsStatLabel}>Avg Rating</Text>
            </View>
            <View style={styles.analyticsStatItem}>
              <IconSymbol name="account-group" size={24} color="#DC143C" />
              <Text style={styles.analyticsStatValue}>892</Text>
              <Text style={styles.analyticsStatLabel}>Customers</Text>
            </View>
          </View>
        </GradientCard>

        {/* Popular Items */}
        <GradientCard style={styles.analyticsCard}>
          <Text style={styles.cardTitle}>Top Selling Items</Text>
          <View style={styles.popularItemsList}>
            {[
              { name: 'Chicken Biryani', orders: 156, revenue: 'PKR 23,400' },
              { name: 'Beef Karahi', orders: 134, revenue: 'PKR 20,100' },
              { name: 'Mutton Pulao', orders: 98, revenue: 'PKR 14,700' },
              { name: 'Fish Curry', orders: 87, revenue: 'PKR 13,050' },
              { name: 'Chicken Tikka', orders: 76, revenue: 'PKR 11,400' }
            ].map((item, index) => (
              <View key={index} style={styles.popularItem}>
                <View style={styles.popularItemRank}>
                  <Text style={styles.rankNumber}>{index + 1}</Text>
                </View>
                <View style={styles.popularItemInfo}>
                  <Text style={styles.popularItemName}>{item.name}</Text>
                  <Text style={styles.popularItemStats}>{item.orders} orders • {item.revenue}</Text>
                </View>
                <View style={styles.popularItemChart}>
                  <View style={[styles.chartBar, { width: `${(item.orders / 156) * 100}%` }]} />
                </View>
              </View>
            ))}
          </View>
        </GradientCard>

        {/* Performance Metrics */}
        <GradientCard style={styles.analyticsCard}>
          <Text style={styles.cardTitle}>Performance Metrics</Text>
          <View style={styles.metricsGrid}>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>94%</Text>
              <Text style={styles.metricLabel}>Order Completion</Text>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: '94%', backgroundColor: '#DC143C' }]} />
              </View>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>87%</Text>
              <Text style={styles.metricLabel}>Customer Satisfaction</Text>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: '87%', backgroundColor: '#EF4444' }]} />
              </View>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>76%</Text>
              <Text style={styles.metricLabel}>On-Time Delivery</Text>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: '76%', backgroundColor: '#B91C1C' }]} />
              </View>
            </View>
          </View>
        </GradientCard>
      </View>
    );
  }

  function renderTeamTab() {
    // Combine mock users and generated users for display
    const allTeamMembers: (typeof mockUsers[0] | GeneratedUser)[] = [...mockUsers, ...generatedUsers];

    return (
      <View style={styles.teamContainer}>
        {/* Team Overview */}
        <GradientCard style={styles.teamCard}>
          <Text style={styles.cardTitle}>Team Overview</Text>
          <View style={styles.teamStats}>
            <View style={styles.teamStatItem}>
              <IconSymbol name="account-group" size={24} color={PremiumColors.primary} />
              <Text style={styles.teamStatValue}>{allTeamMembers.length}</Text>
              <Text style={styles.teamStatLabel}>Total Members</Text>
            </View>
            <View style={styles.teamStatItem}>
              <IconSymbol name="crown" size={24} color="#DC143C" />
              <Text style={styles.teamStatValue}>{allTeamMembers.filter(u => u.role === 'owner').length}</Text>
              <Text style={styles.teamStatLabel}>Owners</Text>
            </View>
            <View style={styles.teamStatItem}>
              <IconSymbol name="shield-account" size={24} color="#EF4444" />
              <Text style={styles.teamStatValue}>{allTeamMembers.filter(u => u.role === 'admin').length}</Text>
              <Text style={styles.teamStatLabel}>Admins</Text>
            </View>
            <View style={styles.teamStatItem}>
              <IconSymbol name="account" size={24} color="#B91C1C" />
              <Text style={styles.teamStatValue}>{allTeamMembers.filter(u => u.role === 'staff').length}</Text>
              <Text style={styles.teamStatLabel}>Staff</Text>
            </View>
          </View>
        </GradientCard>

        {/* Team Members List */}
        <GradientCard style={styles.teamCard}>
          <Text style={styles.cardTitle}>Team Members</Text>
          {allTeamMembers.length === 0 ? (
            <View style={styles.emptyTeamState}>
              <IconSymbol name="account-plus" size={48} color={Colors.light.textSecondary} />
              <Text style={styles.emptyStateText}>No team members found</Text>
              <Text style={styles.emptyStateSubtext}>Add team members through Staff Management</Text>
            </View>
          ) : (
            <View style={styles.teamMembersList}>
              {allTeamMembers.map((member) => (
                <View key={member.id} style={styles.teamMemberCard}>
                  <View style={styles.memberAvatar}>
                    <IconSymbol
                      name={member.role === 'owner' ? 'crown' : member.role === 'admin' ? 'shield-account' : 'account'}
                      size={24}
                      color={member.role === 'owner' ? '#DC143C' : member.role === 'admin' ? '#EF4444' : '#B91C1C'}
                    />
                  </View>
                  <View style={styles.memberInfo}>
                    <Text style={styles.memberName}>{'name' in member && member.name ? member.name : member.username}</Text>
                    <Text style={styles.memberEmail}>{'email' in member && member.email ? member.email : `${member.username}@restaurant.com`}</Text>
                    <View style={styles.memberRoleContainer}>
                      <View style={[styles.roleTag, {
                        backgroundColor: member.role === 'owner' ? '#DC143C' :
                                       member.role === 'admin' ? '#EF4444' : '#B91C1C'
                      }]}>
                        <Text style={styles.roleText}>{member.role.toUpperCase()}</Text>
                      </View>
                      <Text style={styles.memberJoinDate}>Joined {member.createdAt}</Text>
                    </View>
                  </View>
                  <View style={styles.memberActions}>
                    <TouchableOpacity style={styles.memberActionButton}>
                      <IconSymbol name="dots-vertical" size={20} color={Colors.light.textSecondary} />
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          )}
        </GradientCard>

        {/* Role Permissions */}
        <GradientCard style={styles.teamCard}>
          <Text style={styles.cardTitle}>Role Permissions</Text>
          <View style={styles.permissionsGrid}>
            <View style={styles.permissionRole}>
              <Text style={styles.permissionRoleTitle}>Owner</Text>
              <Text style={styles.permissionsList}>Full Access • Staff Management • Analytics • Settings</Text>
            </View>
            <View style={styles.permissionRole}>
              <Text style={styles.permissionRoleTitle}>Admin</Text>
              <Text style={styles.permissionsList}>Dashboard • Orders • Menu • Analytics • Settings</Text>
            </View>
            <View style={styles.permissionRole}>
              <Text style={styles.permissionRoleTitle}>Staff</Text>
              <Text style={styles.permissionsList}>Orders Only</Text>
            </View>
          </View>
        </GradientCard>
      </View>
    );
  }


}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F3E5F5', // Elegant dining-themed background
  },
  scrollView: {
    flex: 1,
  },

  // Enhanced Sticky Header Styles
  stickyHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    height: 110,
  },
  stickyHeaderBlur: {
    flex: 1,
    paddingTop: 50,
  },
  stickyHeaderGradient: {
    flex: 1,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    overflow: 'hidden',
  },
  stickyHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  stickyHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  stickyProfileImageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  stickyProfileImage: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 2,
    borderColor: Colors.light.textInverse,
  },
  stickyStatusDot: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: Colors.light.textInverse,
  },
  stickyHeaderInfo: {
    flex: 1,
  },
  stickyRestaurantName: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 2,
  },
  stickyRatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stickyRatingText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    marginLeft: 4,
  },
  stickyHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stickyActionButton: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Hero Section Styles
  heroContainer: {
    height: HEADER_HEIGHT,
    position: 'relative',
  },
  coverPhotoContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: HEADER_HEIGHT,
  },
  coverPhoto: {
    width: '100%',
    height: '100%',
  },
  coverGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
  },
  profileImageContainer: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 10,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: Colors.light.textInverse,
  },
  editProfileImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.light.primary,
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  heroContent: {
    position: 'absolute',
    bottom: 24,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 20,
    padding: 20,
    backdropFilter: 'blur(10px)',
  },
  profileInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  profileImageSection: {
    position: 'relative',
  },
  uploadableProfileRing: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#FFFFFF',
    padding: 3,
    position: 'relative',
  },
  uploadableProfileImage: {
    width: '100%',
    height: '100%',
    borderRadius: 37,
  },
  uploadOverlay: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#DC143C',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  restaurantInfoSection: {
    flex: 1,
    justifyContent: 'center',
  },
  restaurantName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  restaurantTagline: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    lineHeight: 18,
  },

  quickActions: {
    position: 'absolute',
    top: 60,
    right: 20,
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    backgroundColor: 'rgba(255,255,255,0.25)',
    borderRadius: 18,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },

  // Enhanced Tabs Styles
  tabsContainer: {
    backgroundColor: PremiumColors.white,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -24,
    paddingTop: 24,
    borderTopWidth: 4,
    borderTopColor: PremiumColors.primary,
    shadowColor: PremiumColors.black,
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  tabsScrollView: {
    paddingHorizontal: 20,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 14,
    marginRight: 12,
    borderRadius: 22,
    backgroundColor: Colors.light.background,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  activeTab: {
    backgroundColor: PremiumColors.primary,
    shadowColor: PremiumColors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: PremiumColors.grayDark,
    marginLeft: 8,
  },
  activeTabText: {
    color: PremiumColors.white,
    fontWeight: '700',
  },

  // Tab Content Styles
  tabContent: {
    backgroundColor: PremiumColors.white,
    paddingHorizontal: 20,
    paddingBottom: 100,
  },

  // Overview Tab Styles
  overviewContainer: {
    gap: 16,
  },
  descriptionCard: {
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
    marginBottom: 16,
  },
  cuisineContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  cuisineTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  cuisineEmoji: {
    fontSize: 16,
  },
  cuisineText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.light.textInverse,
  },
  contactCard: {
    marginBottom: 16,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  contactText: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 12,
  },

  // Media Tab Styles
  mediaContainer: {
    gap: 16,
  },

  // Analytics Tab Styles
  analyticsContainer: {
    gap: 16,
  },
  analyticsCard: {
    marginBottom: 16,
  },

  // Team Tab Styles
  teamContainer: {
    gap: 16,
  },
  teamCard: {
    marginBottom: 16,
  },

  // Settings Tab Styles
  settingsContainer: {
    gap: 16,
  },
  settingsCard: {
    marginBottom: 16,
  },

  // Premium Orange & Black Design System Styles
  decorativeBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: PremiumColors.primary,
  },
  profileRing: {
    borderWidth: 3,
    borderColor: PremiumColors.primary,
    borderRadius: 50,
    padding: 3,
  },
  premiumActionButton: {
    backgroundColor: PremiumColors.primary,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 4,
    shadowColor: PremiumColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Logout Button Styles (Staff Only)
  logoutContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 30,
  },
  logoutButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#DC143C',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  logoutGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 12,
  },
  logoutButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
  },

  // Analytics Styles
  revenueGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 16,
  },
  revenueItem: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  revenueValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 4,
  },
  revenueLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginBottom: 8,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  changeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#DC143C',
  },
  changeTextNegative: {
    fontSize: 12,
    fontWeight: '600',
    color: '#B91C1C',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginTop: 16,
  },
  analyticsStatItem: {
    flex: 1,
    minWidth: '40%',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
  },
  analyticsStatValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginTop: 8,
    marginBottom: 4,
  },
  analyticsStatLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  popularItemsList: {
    marginTop: 16,
    gap: 12,
  },
  popularItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    gap: 12,
  },
  popularItemRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: PremiumColors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rankNumber: {
    fontSize: 14,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  popularItemInfo: {
    flex: 1,
  },
  popularItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  popularItemStats: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  popularItemChart: {
    width: 60,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  chartBar: {
    height: '100%',
    backgroundColor: PremiumColors.primary,
    borderRadius: 2,
  },
  metricsGrid: {
    marginTop: 16,
    gap: 16,
  },
  metricItem: {
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 12,
  },
  progressBar: {
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },

  // Team Styles
  teamStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 16,
  },
  teamStatItem: {
    flex: 1,
    minWidth: '40%',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
  },
  teamStatValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginTop: 8,
    marginBottom: 4,
  },
  teamStatLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  emptyTeamState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  teamMembersList: {
    marginTop: 16,
    gap: 12,
  },
  teamMemberCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    gap: 12,
  },
  memberAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  memberEmail: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 8,
  },
  memberRoleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  roleTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  roleText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  memberJoinDate: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  memberActions: {
    padding: 8,
  },
  memberActionButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  permissionsGrid: {
    marginTop: 16,
    gap: 12,
  },
  permissionRole: {
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
  },
  permissionRoleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  permissionsList: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  // Edit Form Styles
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  editButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: `${PremiumColors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
  },
  editForm: {
    gap: 16,
  },
  editLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  editInput: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
    backgroundColor: Colors.light.background,
  },
  editTextArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
});
