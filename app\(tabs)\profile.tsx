import { GradientCard } from '@/components/profile/AnimatedCounter';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { PremiumColors } from '@/constants/PremiumTheme';
import { useAuth } from '@/contexts/AuthContext';
import { mockRestaurantProfile } from '@/data/mockProfileData';
import { mockUsers } from '@/types/auth';
import { RestaurantProfile } from '@/types/profile';
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import { useState } from 'react';
import {
  Alert,
  Image,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue
} from 'react-native-reanimated';



const HEADER_HEIGHT = 300;

export default function ProfileScreen() {
  // Use mock data for demonstration
  const profileData: RestaurantProfile = mockRestaurantProfile;

  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [profileImage, setProfileImage] = useState(profileData.profilePicture);
  const [teamMembers, setTeamMembers] = useState(mockUsers);
  const [editedData, setEditedData] = useState({
    name: profileData.name,
    tagline: profileData.tagline || '',
    phone: profileData.contact.primaryPhone,
    email: profileData.contact.email,
    address: profileData.location.address,
  });

  const scrollY = useSharedValue(0);
  const headerOpacity = useSharedValue(1);

  // Auth context for logout functionality
  const { logout, user } = useAuth();

  const tabs = [
    { id: 'overview', title: 'Overview', icon: 'house.fill' as const },
    { id: 'analytics', title: 'Analytics', icon: 'chart.bar.fill' as const },
    { id: 'team', title: 'Team', icon: 'person.2.fill' as const },
  ];





  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            logout();
          },
        },
      ]
    );
  };

  const handleProfileImageUpload = async () => {
    try {
      // Request permission to access media library
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera roll is required!');
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newImageUri = result.assets[0].uri;
        setProfileImage(newImageUri);

        Alert.alert(
          'Profile Picture Updated!',
          'Your restaurant profile picture has been updated successfully.',
          [
            {
              text: 'OK',
              onPress: () => {
                console.log('New profile image selected:', newImageUri);
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image from gallery. Please try again.');
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    // Here you would save the edited data to your backend
    Alert.alert(
      'Profile Updated!',
      'Your restaurant profile has been updated successfully.',
      [
        {
          text: 'OK',
          onPress: () => {
            setIsEditing(false);
            console.log('Profile data saved:', editedData);
          }
        }
      ]
    );
  };

  const handleCancel = () => {
    // Reset edited data to original values
    setEditedData({
      name: profileData.name,
      tagline: profileData.tagline || '',
      phone: profileData.contact.primaryPhone,
      email: profileData.contact.email,
      address: profileData.location.address,
    });
    setIsEditing(false);
  };

  const handleDisableTeamMember = (memberId: string) => {
    const member = teamMembers.find(m => m.id === memberId);
    if (!member) return;

    // Role-based permission checks
    if (member.role === 'owner') {
      Alert.alert('Cannot Disable', 'Owner accounts cannot be disabled as they are the main restaurant account.');
      return;
    }

    if (user?.role === 'admin' && member.role === 'admin') {
      Alert.alert('Permission Denied', 'Admin users cannot disable other Admin accounts. Only the Owner can manage Admin accounts.');
      return;
    }

    if (user?.role === 'admin' && member.role === 'owner') {
      Alert.alert('Permission Denied', 'Admin users cannot disable the Owner account.');
      return;
    }

    if (user?.role === 'staff') {
      Alert.alert('Permission Denied', 'Staff members do not have permission to manage team members.');
      return;
    }

    Alert.alert(
      'Disable Team Member',
      `Are you sure you want to disable ${member.username}? They will no longer be able to access the system.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Disable',
          style: 'destructive',
          onPress: () => {
            setTeamMembers(prev => prev.map(m =>
              m.id === memberId ? { ...m, isActive: false } : m
            ));
            Alert.alert('Success', `${member.username} has been disabled.`);
          }
        }
      ]
    );
  };

  const handleRemoveTeamMember = (memberId: string) => {
    const member = teamMembers.find(m => m.id === memberId);
    if (!member) return;

    // Role-based permission checks
    if (member.role === 'owner') {
      Alert.alert('Cannot Remove', 'Owner accounts cannot be removed as they are the main restaurant account.');
      return;
    }

    if (user?.role === 'admin' && member.role === 'admin') {
      Alert.alert('Permission Denied', 'Admin users cannot remove other Admin accounts. Only the Owner can manage Admin accounts.');
      return;
    }

    if (user?.role === 'staff') {
      Alert.alert('Permission Denied', 'Staff members do not have permission to manage team members.');
      return;
    }

    // Additional check: Only Owner can remove Admin accounts
    if (member.role === 'admin' && user?.role !== 'owner') {
      Alert.alert('Permission Denied', 'Only the Owner can remove Admin accounts.');
      return;
    }

    Alert.alert(
      'Remove Team Member',
      `Are you sure you want to permanently remove ${member.username}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setTeamMembers(prev => prev.filter(m => m.id !== memberId));
            Alert.alert('Success', `${member.username} has been removed from the team.`);
          }
        }
      ]
    );
  };

  const handleEnableTeamMember = (memberId: string) => {
    const member = teamMembers.find(m => m.id === memberId);
    if (!member) return;

    // Role-based permission checks
    if (user?.role === 'admin' && member.role === 'admin') {
      Alert.alert('Permission Denied', 'Admin users cannot enable other Admin accounts. Only the Owner can manage Admin accounts.');
      return;
    }

    if (user?.role === 'staff') {
      Alert.alert('Permission Denied', 'Staff members do not have permission to manage team members.');
      return;
    }

    // Additional check: Only Owner can enable Admin accounts
    if (member.role === 'admin' && user?.role !== 'owner') {
      Alert.alert('Permission Denied', 'Only the Owner can enable Admin accounts.');
      return;
    }

    setTeamMembers(prev => prev.map(m =>
      m.id === memberId ? { ...m, isActive: true } : m
    ));
    Alert.alert('Success', `${member.username} has been enabled.`);
  };

  // Helper function to check if current user can manage a specific member
  const canManageMember = (member: typeof teamMembers[0]) => {
    // Staff cannot manage anyone
    if (user?.role === 'staff') return false;

    // Owner cannot be managed by anyone (including other owners)
    if (member.role === 'owner') return false;

    // Only Owner can manage Admin accounts
    if (member.role === 'admin' && user?.role !== 'owner') return false;

    // Admin can manage Staff accounts
    if (user?.role === 'admin' && member.role === 'staff') return true;

    // Owner can manage everyone (except other owners)
    if (user?.role === 'owner' && member.role !== 'owner') return true;

    return false;
  };



  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: headerOpacity.value,
      transform: [
        {
          translateY: interpolate(
            scrollY.value,
            [0, HEADER_HEIGHT],
            [0, -HEADER_HEIGHT / 2]
          ),
        },
      ],
    };
  });





  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderHeroSection = () => (
    <Animated.View style={[styles.heroContainer, headerAnimatedStyle]}>
      {/* Cover Photo with Parallax */}
      <View style={styles.coverPhotoContainer}>
        <Image
          source={{ uri: profileData.coverPhoto }}
          style={styles.coverPhoto}
          resizeMode="cover"
        />
        <LinearGradient
          colors={['transparent', '#DC143C80', '#DC143C']}
          style={styles.coverGradient}
        />
        {/* Orange decorative bar */}
        <View style={styles.decorativeBar} />
      </View>



      {/* Restaurant Info - New Layout */}
      <View style={styles.heroContent}>
        <View style={styles.profileInfoContainer}>
          {/* Profile Image on Left */}
          <TouchableOpacity style={styles.profileImageSection} onPress={handleProfileImageUpload}>
            <View style={styles.uploadableProfileRing}>
              <Image
                source={{ uri: profileImage }}
                style={styles.uploadableProfileImage}
              />
              <View style={styles.uploadOverlay}>
                <IconSymbol name="camera" size={20} color="#FFFFFF" />
              </View>
            </View>
          </TouchableOpacity>

          {/* Restaurant Name and Tagline on Right */}
          <View style={styles.restaurantInfoSection}>
            {isEditing ? (
              <>
                <TextInput
                  style={styles.editInput}
                  value={editedData.name}
                  onChangeText={(text) => setEditedData(prev => ({ ...prev, name: text }))}
                  placeholder="Restaurant Name"
                  placeholderTextColor="#FFFFFF80"
                />
                <TextInput
                  style={styles.editTaglineInput}
                  value={editedData.tagline}
                  onChangeText={(text) => setEditedData(prev => ({ ...prev, tagline: text }))}
                  placeholder="Restaurant Tagline"
                  placeholderTextColor="#FFFFFF80"
                />
              </>
            ) : (
              <>
                <Text style={styles.restaurantName}>{editedData.name}</Text>
                <Text style={styles.restaurantTagline}>{editedData.tagline}</Text>
              </>
            )}
          </View>
        </View>

        {/* Edit/Save/Cancel Buttons */}
        <View style={styles.actionButtonsContainer}>
          {isEditing ? (
            <>
              <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                <IconSymbol name="check" size={16} color="#FFFFFF" />
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                <IconSymbol name="close" size={16} color="#FFFFFF" />
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            </>
          ) : (
            <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
              <IconSymbol name="pencil" size={16} color="#FFFFFF" />
              <Text style={styles.editButtonText}>Edit</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

    </Animated.View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="transparent" translucent={false} />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderHeroSection()}

        {/* Tabbed Interface */}
        <View style={styles.tabsContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabsScrollView}>
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[
                  styles.tab,
                  activeTab === tab.id && styles.activeTab,
                ]}
                onPress={() => setActiveTab(tab.id)}
              >
                <IconSymbol
                  name={tab.icon}
                  size={20}
                  color={activeTab === tab.id ? '#FFFFFF' : '#6B7280'}
                />
                <Text
                  style={[
                    styles.tabText,
                    activeTab === tab.id && styles.activeTabText,
                  ]}
                >
                  {tab.title}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Tab Content */}
        <View style={styles.tabContent}>
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'analytics' && renderAnalyticsTab()}
          {activeTab === 'team' && renderTeamTab()}
        </View>

        {/* Logout Button - Only for Staff */}
        {user?.role === 'staff' && (
          <View style={styles.logoutContainer}>
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <LinearGradient
                colors={['#DC143C', '#B91C1C']}
                style={styles.logoutGradient}
              >
                <IconSymbol name="arrow.right.square" size={20} color="#FFFFFF" />
                <Text style={styles.logoutButtonText}>Logout</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );

  // Tab Content Renderers
  function renderOverviewTab() {
    return (
      <View style={styles.overviewContainer}>
        {/* Restaurant Description */}
        <GradientCard style={styles.descriptionCard}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>About Us</Text>
          </View>
          <Text style={styles.description}>{profileData.description}</Text>

          {/* Cuisine Types */}
          <View style={styles.cuisineContainer}>
            {profileData.cuisineTypes.map((cuisine) => (
              <View key={cuisine.id} style={[styles.cuisineTag, { backgroundColor: cuisine.color }]}>
                <Text style={styles.cuisineEmoji}>{cuisine.icon}</Text>
                <Text style={styles.cuisineText}>{cuisine.name}</Text>
              </View>
            ))}
          </View>
        </GradientCard>

        {/* Contact Information */}
        <GradientCard style={styles.contactCard}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Contact Information</Text>
          </View>

          {isEditing ? (
            <View style={styles.editContactForm}>
              <View style={styles.editContactItem}>
                <IconSymbol name="phone.fill" size={20} color="#DC143C" />
                <TextInput
                  style={styles.editContactInput}
                  value={editedData.phone}
                  onChangeText={(text) => setEditedData(prev => ({ ...prev, phone: text }))}
                  placeholder="Phone number"
                  keyboardType="phone-pad"
                />
              </View>

              <View style={styles.editContactItem}>
                <IconSymbol name="envelope.fill" size={20} color="#DC143C" />
                <TextInput
                  style={styles.editContactInput}
                  value={editedData.email}
                  onChangeText={(text) => setEditedData(prev => ({ ...prev, email: text }))}
                  placeholder="Email address"
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>

              <View style={styles.editContactItem}>
                <IconSymbol name="location.fill" size={20} color="#DC143C" />
                <TextInput
                  style={[styles.editContactInput, styles.editContactTextArea]}
                  value={editedData.address}
                  onChangeText={(text) => setEditedData(prev => ({ ...prev, address: text }))}
                  placeholder="Restaurant address"
                  multiline
                  numberOfLines={2}
                />
              </View>
            </View>
          ) : (
            <>
              <TouchableOpacity style={styles.contactItem}>
                <IconSymbol name="phone.fill" size={20} color="#DC143C" />
                <Text style={styles.contactText}>{editedData.phone}</Text>
                <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
              </TouchableOpacity>

              <TouchableOpacity style={styles.contactItem}>
                <IconSymbol name="envelope.fill" size={20} color="#DC143C" />
                <Text style={styles.contactText}>{editedData.email}</Text>
                <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
              </TouchableOpacity>

              <TouchableOpacity style={styles.contactItem}>
                <IconSymbol name="location.fill" size={20} color="#DC143C" />
                <Text style={styles.contactText}>{editedData.address}</Text>
                <IconSymbol name="chevron.right" size={16} color="#9CA3AF" />
              </TouchableOpacity>
            </>
          )}
        </GradientCard>
      </View>
    );
  }



  function renderAnalyticsTab() {
    return (
      <View style={styles.analyticsContainer}>
        {/* Revenue Overview */}
        <GradientCard style={styles.analyticsCard}>
          <Text style={styles.cardTitle}>Revenue Overview</Text>
          <View style={styles.revenueGrid}>
            <View style={styles.revenueItem}>
              <Text style={styles.revenueValue}>PKR 45,230</Text>
              <Text style={styles.revenueLabel}>Today</Text>
              <View style={styles.changeIndicator}>
                <IconSymbol name="trending-up" size={12} color="#DC143C" />
                <Text style={styles.changeText}>+12%</Text>
              </View>
            </View>
            <View style={styles.revenueItem}>
              <Text style={styles.revenueValue}>PKR 312,450</Text>
              <Text style={styles.revenueLabel}>This Week</Text>
              <View style={styles.changeIndicator}>
                <IconSymbol name="trending-up" size={12} color="#DC143C" />
                <Text style={styles.changeText}>+8%</Text>
              </View>
            </View>
            <View style={styles.revenueItem}>
              <Text style={styles.revenueValue}>PKR 1,245,680</Text>
              <Text style={styles.revenueLabel}>This Month</Text>
              <View style={styles.changeIndicator}>
                <IconSymbol name="trending-down" size={12} color="#B91C1C" />
                <Text style={styles.changeTextNegative}>-3%</Text>
              </View>
            </View>
            <View style={styles.revenueItem}>
              <Text style={styles.revenueValue}>PKR 14,567,890</Text>
              <Text style={styles.revenueLabel}>This Year</Text>
              <View style={styles.changeIndicator}>
                <IconSymbol name="trending-up" size={12} color="#DC143C" />
                <Text style={styles.changeText}>+15%</Text>
              </View>
            </View>
          </View>
        </GradientCard>

        {/* Order Statistics */}
        <GradientCard style={styles.analyticsCard}>
          <Text style={styles.cardTitle}>Order Statistics</Text>
          <View style={styles.statsGrid}>
            <View style={styles.analyticsStatItem}>
              <IconSymbol name="receipt" size={24} color={PremiumColors.primary} />
              <Text style={styles.analyticsStatValue}>1,234</Text>
              <Text style={styles.analyticsStatLabel}>Total Orders</Text>
            </View>
            <View style={styles.analyticsStatItem}>
              <IconSymbol name="clock" size={24} color={PremiumColors.primaryLight} />
              <Text style={styles.analyticsStatValue}>18 min</Text>
              <Text style={styles.analyticsStatLabel}>Avg Prep Time</Text>
            </View>
            <View style={styles.analyticsStatItem}>
              <IconSymbol name="star" size={24} color="#DC143C" />
              <Text style={styles.analyticsStatValue}>4.8</Text>
              <Text style={styles.analyticsStatLabel}>Avg Rating</Text>
            </View>
            <View style={styles.analyticsStatItem}>
              <IconSymbol name="account-group" size={24} color="#DC143C" />
              <Text style={styles.analyticsStatValue}>892</Text>
              <Text style={styles.analyticsStatLabel}>Customers</Text>
            </View>
          </View>
        </GradientCard>

        {/* Popular Items */}
        <GradientCard style={styles.analyticsCard}>
          <Text style={styles.cardTitle}>Top Selling Items</Text>
          <View style={styles.popularItemsList}>
            {[
              { name: 'Chicken Biryani', orders: 156, revenue: 'PKR 23,400' },
              { name: 'Beef Karahi', orders: 134, revenue: 'PKR 20,100' },
              { name: 'Mutton Pulao', orders: 98, revenue: 'PKR 14,700' },
              { name: 'Fish Curry', orders: 87, revenue: 'PKR 13,050' },
              { name: 'Chicken Tikka', orders: 76, revenue: 'PKR 11,400' }
            ].map((item, index) => (
              <View key={index} style={styles.popularItem}>
                <View style={styles.popularItemRank}>
                  <Text style={styles.rankNumber}>{index + 1}</Text>
                </View>
                <View style={styles.popularItemInfo}>
                  <Text style={styles.popularItemName}>{item.name}</Text>
                  <Text style={styles.popularItemStats}>{item.orders} orders • {item.revenue}</Text>
                </View>
                <View style={styles.popularItemChart}>
                  <View style={[styles.chartBar, { width: `${(item.orders / 156) * 100}%` }]} />
                </View>
              </View>
            ))}
          </View>
        </GradientCard>

        {/* Performance Metrics */}
        <GradientCard style={styles.analyticsCard}>
          <Text style={styles.cardTitle}>Performance Metrics</Text>
          <View style={styles.metricsGrid}>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>94%</Text>
              <Text style={styles.metricLabel}>Order Completion</Text>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: '94%', backgroundColor: '#DC143C' }]} />
              </View>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>87%</Text>
              <Text style={styles.metricLabel}>Customer Satisfaction</Text>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: '87%', backgroundColor: '#EF4444' }]} />
              </View>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>76%</Text>
              <Text style={styles.metricLabel}>On-Time Delivery</Text>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: '76%', backgroundColor: '#B91C1C' }]} />
              </View>
            </View>
          </View>
        </GradientCard>
      </View>
    );
  }

  function renderTeamTab() {
    // Use team members state for display
    const allTeamMembers = teamMembers;

    return (
      <View style={styles.teamContainer}>
        {/* Team Overview */}
        <GradientCard style={styles.teamCard}>
          <Text style={styles.cardTitle}>Team Overview</Text>
          <View style={styles.teamStats}>
            <View style={styles.teamStatItem}>
              <IconSymbol name="account-group" size={24} color={PremiumColors.primary} />
              <Text style={styles.teamStatValue}>{allTeamMembers.length}</Text>
              <Text style={styles.teamStatLabel}>Total Members</Text>
            </View>
            <View style={styles.teamStatItem}>
              <IconSymbol name="crown" size={24} color="#DC143C" />
              <Text style={styles.teamStatValue}>{allTeamMembers.filter(u => u.role === 'owner').length}</Text>
              <Text style={styles.teamStatLabel}>Owners</Text>
            </View>
            <View style={styles.teamStatItem}>
              <IconSymbol name="shield-account" size={24} color="#EF4444" />
              <Text style={styles.teamStatValue}>{allTeamMembers.filter(u => u.role === 'admin').length}</Text>
              <Text style={styles.teamStatLabel}>Admins</Text>
            </View>
            <View style={styles.teamStatItem}>
              <IconSymbol name="account" size={24} color="#B91C1C" />
              <Text style={styles.teamStatValue}>{allTeamMembers.filter(u => u.role === 'staff').length}</Text>
              <Text style={styles.teamStatLabel}>Staff</Text>
            </View>
          </View>
        </GradientCard>

        {/* Team Members List */}
        <GradientCard style={styles.teamCard}>
          <Text style={styles.cardTitle}>Team Members</Text>
          {allTeamMembers.length === 0 ? (
            <View style={styles.emptyTeamState}>
              <IconSymbol name="account-plus" size={48} color={Colors.light.textSecondary} />
              <Text style={styles.emptyStateText}>No team members found</Text>
              <Text style={styles.emptyStateSubtext}>Add team members through Staff Management</Text>
            </View>
          ) : (
            <View style={styles.teamMembersList}>
              {allTeamMembers.map((member) => (
                <View key={member.id} style={[
                  styles.teamMemberCard,
                  member.isActive === false && styles.disabledMemberCard
                ]}>
                  <View style={styles.memberAvatar}>
                    <IconSymbol
                      name={member.role === 'owner' ? 'crown' : member.role === 'admin' ? 'shield-account' : 'account'}
                      size={24}
                      color={member.isActive === false ? '#9CA3AF' :
                             member.role === 'owner' ? '#DC143C' :
                             member.role === 'admin' ? '#EF4444' : '#B91C1C'}
                    />
                  </View>
                  <View style={styles.memberInfo}>
                    <View style={styles.memberNameRow}>
                      <Text style={[
                        styles.memberName,
                        member.isActive === false && styles.disabledText
                      ]}>
                        {'name' in member && member.name ? member.name : member.username}
                      </Text>
                      {member.isActive === false && (
                        <View style={styles.disabledBadge}>
                          <Text style={styles.disabledBadgeText}>DISABLED</Text>
                        </View>
                      )}
                    </View>
                    <Text style={[
                      styles.memberEmail,
                      member.isActive === false && styles.disabledText
                    ]}>
                      {'email' in member && member.email ? member.email : `${member.username}@restaurant.com`}
                    </Text>
                    <View style={styles.memberRoleContainer}>
                      <View style={[styles.roleTag, {
                        backgroundColor: member.isActive === false ? '#9CA3AF' :
                                       member.role === 'owner' ? '#DC143C' :
                                       member.role === 'admin' ? '#EF4444' : '#B91C1C'
                      }]}>
                        <Text style={styles.roleText}>{member.role.toUpperCase()}</Text>
                      </View>
                      <Text style={[
                        styles.memberJoinDate,
                        member.isActive === false && styles.disabledText
                      ]}>
                        Joined {member.createdAt}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.memberActions}>
                    {canManageMember(member) ? (
                      member.isActive === false ? (
                        <TouchableOpacity
                          style={styles.enableButton}
                          onPress={() => handleEnableTeamMember(member.id)}
                        >
                          <IconSymbol name="check-circle" size={20} color="#22C55E" />
                        </TouchableOpacity>
                      ) : (
                        <>
                          <TouchableOpacity
                            style={styles.disableButton}
                            onPress={() => handleDisableTeamMember(member.id)}
                          >
                            <IconSymbol name="pause-circle" size={20} color="#F59E0B" />
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={styles.removeButton}
                            onPress={() => handleRemoveTeamMember(member.id)}
                          >
                            <IconSymbol name="delete" size={20} color="#EF4444" />
                          </TouchableOpacity>
                        </>
                      )
                    ) : (
                      <View style={styles.noActionsContainer}>
                        <Text style={styles.noActionsText}>
                          {member.role === 'owner' ? 'Main Account' : 'No Permission'}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              ))}
            </View>
          )}
        </GradientCard>

        {/* Role Permissions */}
        <GradientCard style={styles.teamCard}>
          <Text style={styles.cardTitle}>Role Permissions</Text>
          <View style={styles.permissionsGrid}>
            <View style={styles.permissionRole}>
              <Text style={styles.permissionRoleTitle}>Owner</Text>
              <Text style={styles.permissionsList}>Full Access • Staff Management • Analytics • Settings</Text>
            </View>
            <View style={styles.permissionRole}>
              <Text style={styles.permissionRoleTitle}>Admin</Text>
              <Text style={styles.permissionsList}>Dashboard • Orders • Menu • Analytics • Settings</Text>
            </View>
            <View style={styles.permissionRole}>
              <Text style={styles.permissionRoleTitle}>Staff</Text>
              <Text style={styles.permissionsList}>Orders Only</Text>
            </View>
          </View>
        </GradientCard>

        {/* Management Permissions */}
        <GradientCard style={styles.teamCard}>
          <Text style={styles.cardTitle}>Team Management Rules</Text>
          <View style={styles.managementRules}>
            <View style={styles.managementRule}>
              <IconSymbol name="crown" size={16} color="#DC143C" />
              <Text style={styles.managementRuleText}>
                <Text style={styles.managementRoleHighlight}>Owner</Text> can manage all Admin and Staff accounts
              </Text>
            </View>
            <View style={styles.managementRule}>
              <IconSymbol name="shield-account" size={16} color="#EF4444" />
              <Text style={styles.managementRuleText}>
                <Text style={styles.managementRoleHighlight}>Admin</Text> can only manage Staff accounts
              </Text>
            </View>
            <View style={styles.managementRule}>
              <IconSymbol name="account" size={16} color="#B91C1C" />
              <Text style={styles.managementRuleText}>
                <Text style={styles.managementRoleHighlight}>Staff</Text> cannot manage any accounts
              </Text>
            </View>
            <View style={styles.managementRule}>
              <IconSymbol name="shield-check" size={16} color="#22C55E" />
              <Text style={styles.managementRuleText}>
                Owner accounts cannot be disabled or removed (Main Account)
              </Text>
            </View>
          </View>

          <View style={styles.currentUserInfo}>
            <Text style={styles.currentUserLabel}>Your Role:</Text>
            <View style={[styles.roleTag, {
              backgroundColor: user?.role === 'owner' ? '#DC143C' :
                             user?.role === 'admin' ? '#EF4444' : '#B91C1C'
            }]}>
              <Text style={styles.roleText}>{user?.role?.toUpperCase()}</Text>
            </View>
          </View>
        </GradientCard>
      </View>
    );
  }


}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F3E5F5', // Elegant dining-themed background
  },
  scrollView: {
    flex: 1,
  },

  // Enhanced Sticky Header Styles
  stickyHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    height: 110,
  },
  stickyHeaderBlur: {
    flex: 1,
    paddingTop: 50,
  },
  stickyHeaderGradient: {
    flex: 1,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    overflow: 'hidden',
  },
  stickyHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  stickyHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  stickyProfileImageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  stickyProfileImage: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 2,
    borderColor: Colors.light.textInverse,
  },
  stickyStatusDot: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: Colors.light.textInverse,
  },
  stickyHeaderInfo: {
    flex: 1,
  },
  stickyRestaurantName: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 2,
  },
  stickyRatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stickyRatingText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    marginLeft: 4,
  },
  stickyHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stickyActionButton: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Hero Section Styles
  heroContainer: {
    height: HEADER_HEIGHT,
    position: 'relative',
  },
  coverPhotoContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: HEADER_HEIGHT,
  },
  coverPhoto: {
    width: '100%',
    height: '100%',
  },
  coverGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
  },
  profileImageContainer: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 10,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: Colors.light.textInverse,
  },
  editProfileImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.light.primary,
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  heroContent: {
    position: 'absolute',
    bottom: 24,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 20,
    padding: 20,
    backdropFilter: 'blur(10px)',
  },
  profileInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  profileImageSection: {
    position: 'relative',
  },
  uploadableProfileRing: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#FFFFFF',
    padding: 3,
    position: 'relative',
  },
  uploadableProfileImage: {
    width: '100%',
    height: '100%',
    borderRadius: 37,
  },
  uploadOverlay: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#DC143C',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  restaurantInfoSection: {
    flex: 1,
    justifyContent: 'center',
  },
  editInput: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#FFFFFF80',
    paddingVertical: 4,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  editTaglineInput: {
    fontSize: 14,
    color: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#FFFFFF80',
    paddingVertical: 4,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    lineHeight: 18,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
    gap: 8,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#DC143C',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#22C55E',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EF4444',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  cancelButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  editContactForm: {
    gap: 16,
  },
  editContactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 8,
  },
  editContactInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    borderBottomWidth: 1,
    borderBottomColor: '#DC143C',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  editContactTextArea: {
    minHeight: 60,
    textAlignVertical: 'top',
  },
  disabledMemberCard: {
    opacity: 0.6,
    backgroundColor: '#F3F4F6',
  },
  memberNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  disabledText: {
    color: '#9CA3AF',
  },
  disabledBadge: {
    backgroundColor: '#EF4444',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  disabledBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  enableButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F0FDF4',
    marginLeft: 4,
  },
  disableButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#FFFBEB',
    marginLeft: 4,
  },
  removeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#FEF2F2',
    marginLeft: 4,
  },
  noActionsContainer: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
  },
  noActionsText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
    textAlign: 'center',
  },
  restaurantName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  restaurantTagline: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    lineHeight: 18,
  },

  quickActions: {
    position: 'absolute',
    top: 60,
    right: 20,
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    backgroundColor: 'rgba(255,255,255,0.25)',
    borderRadius: 18,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },

  // Enhanced Tabs Styles
  tabsContainer: {
    backgroundColor: PremiumColors.white,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -24,
    paddingTop: 24,
    borderTopWidth: 4,
    borderTopColor: PremiumColors.primary,
    shadowColor: PremiumColors.black,
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  tabsScrollView: {
    paddingHorizontal: 20,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 14,
    marginRight: 12,
    borderRadius: 22,
    backgroundColor: Colors.light.background,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  activeTab: {
    backgroundColor: PremiumColors.primary,
    shadowColor: PremiumColors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: PremiumColors.grayDark,
    marginLeft: 8,
  },
  activeTabText: {
    color: PremiumColors.white,
    fontWeight: '700',
  },

  // Tab Content Styles
  tabContent: {
    backgroundColor: PremiumColors.white,
    paddingHorizontal: 20,
    paddingBottom: 100,
  },

  // Overview Tab Styles
  overviewContainer: {
    gap: 16,
  },
  descriptionCard: {
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
    marginBottom: 16,
  },
  cuisineContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  cuisineTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  cuisineEmoji: {
    fontSize: 16,
  },
  cuisineText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.light.textInverse,
  },
  contactCard: {
    marginBottom: 16,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  contactText: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 12,
  },

  // Media Tab Styles
  mediaContainer: {
    gap: 16,
  },

  // Analytics Tab Styles
  analyticsContainer: {
    gap: 16,
  },
  analyticsCard: {
    marginBottom: 16,
  },

  // Team Tab Styles
  teamContainer: {
    gap: 16,
  },
  teamCard: {
    marginBottom: 16,
  },

  // Settings Tab Styles
  settingsContainer: {
    gap: 16,
  },
  settingsCard: {
    marginBottom: 16,
  },

  // Premium Orange & Black Design System Styles
  decorativeBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: PremiumColors.primary,
  },
  profileRing: {
    borderWidth: 3,
    borderColor: PremiumColors.primary,
    borderRadius: 50,
    padding: 3,
  },
  premiumActionButton: {
    backgroundColor: PremiumColors.primary,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 4,
    shadowColor: PremiumColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Logout Button Styles (Staff Only)
  logoutContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 30,
  },
  logoutButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#DC143C',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  logoutGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 12,
  },
  logoutButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
  },

  // Analytics Styles
  revenueGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 16,
  },
  revenueItem: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  revenueValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 4,
  },
  revenueLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginBottom: 8,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  changeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#DC143C',
  },
  changeTextNegative: {
    fontSize: 12,
    fontWeight: '600',
    color: '#B91C1C',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginTop: 16,
  },
  analyticsStatItem: {
    flex: 1,
    minWidth: '40%',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
  },
  analyticsStatValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginTop: 8,
    marginBottom: 4,
  },
  analyticsStatLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  popularItemsList: {
    marginTop: 16,
    gap: 12,
  },
  popularItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    gap: 12,
  },
  popularItemRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: PremiumColors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rankNumber: {
    fontSize: 14,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  popularItemInfo: {
    flex: 1,
  },
  popularItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  popularItemStats: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  popularItemChart: {
    width: 60,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  chartBar: {
    height: '100%',
    backgroundColor: PremiumColors.primary,
    borderRadius: 2,
  },
  metricsGrid: {
    marginTop: 16,
    gap: 16,
  },
  metricItem: {
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 12,
  },
  progressBar: {
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },

  // Team Styles
  teamStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 16,
  },
  teamStatItem: {
    flex: 1,
    minWidth: '40%',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
  },
  teamStatValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginTop: 8,
    marginBottom: 4,
  },
  teamStatLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  emptyTeamState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  teamMembersList: {
    marginTop: 16,
    gap: 12,
  },
  teamMemberCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    gap: 12,
  },
  memberAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  memberEmail: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 8,
  },
  memberRoleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  roleTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  roleText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  memberJoinDate: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  memberActions: {
    padding: 8,
  },
  memberActionButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  permissionsGrid: {
    marginTop: 16,
    gap: 12,
  },
  permissionRole: {
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
  },
  permissionRoleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  permissionsList: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  // Edit Form Styles
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },

});
