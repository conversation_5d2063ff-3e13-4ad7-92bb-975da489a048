import { MenuItem as FormMenuItem } from '@/components/forms/MenuItemForm/types';
import { MenuItem as AppMenuItem } from '@/types';

/**
 * Converts a form MenuItem to an app MenuItem
 * This adapter handles the differences between the comprehensive form data structure
 * and the simpler app data structure
 */
export function convertFormMenuItemToAppMenuItem(formItem: Partial<FormMenuItem>): Omit<AppMenuItem, 'id'> {
  // Get the base price from pricing tiers (use default tier or first tier)
  const basePrice = formItem.price && formItem.price.length > 0 
    ? formItem.price.find(tier => tier.isDefault)?.price || formItem.price[0].price || 0
    : 0;

  // Extract basic ingredients list
  const ingredients = formItem.ingredients?.map(ingredient => ingredient.name) || [];

  // Extract allergen names
  const allergens = formItem.allergens?.map(allergen => allergen.name) || [];

  // Convert customizations to app format
  const customizations = formItem.customizations?.map(group => ({
    id: group.id,
    name: group.name,
    type: group.allowMultiple ? 'multiple' as const : 'single' as const,
    required: group.isRequired,
    options: group.options.map(option => ({
      id: option.id,
      name: option.name,
      price: option.priceAdjustment || 0,
    })),
  })) || [];

  // Get primary image URL
  const primaryImage = formItem.images?.find(img => img.isPrimary)?.url || 
                      formItem.images?.[0]?.url;

  // Determine dietary flags from allergens and tags
  const isVegetarian = formItem.tags?.includes('vegetarian') || 
                      formItem.tags?.includes('Vegetarian') ||
                      !allergens.some(allergen => ['meat', 'fish', 'seafood'].includes(allergen.toLowerCase()));

  const isVegan = formItem.tags?.includes('vegan') || 
                 formItem.tags?.includes('Vegan') ||
                 (!allergens.includes('Dairy') && !allergens.includes('Eggs') && isVegetarian);

  const isGlutenFree = formItem.tags?.includes('gluten-free') || 
                      formItem.tags?.includes('Gluten-Free') ||
                      !allergens.includes('Gluten');

  return {
    name: formItem.name || '',
    description: formItem.description || '',
    price: basePrice,
    category: formItem.category || '',
    image: primaryImage,
    isAvailable: formItem.isActive ?? true,
    preparationTime: formItem.preparationTime || 15,
    ingredients,
    allergens,
    isVegetarian,
    isVegan,
    isGlutenFree,
    customizations,
  };
}

/**
 * Converts an app MenuItem to a form MenuItem for editing
 * This adapter handles the conversion from simple app data structure
 * to the comprehensive form data structure
 */
export function convertAppMenuItemToFormMenuItem(appItem: AppMenuItem): Partial<FormMenuItem> {
  return {
    id: appItem.id,
    name: appItem.name,
    description: appItem.description,
    category: appItem.category,
    price: [{
      id: 'default',
      name: 'Standard',
      price: appItem.price,
      currency: 'USD',
      taxRate: 0.08, // Default 8% tax
      isDefault: true,
    }],
    preparationTime: appItem.preparationTime,
    ingredients: appItem.ingredients?.map((name, index) => ({
      id: `ingredient-${index}`,
      name,
      quantity: 1,
      unit: 'piece',
      isOptional: false,
      allergens: [],
    })) || [],
    allergens: appItem.allergens?.map((name, index) => ({
      id: `allergen-${index}`,
      name,
      severity: 'moderate' as const,
    })) || [],
    nutritionalInfo: {
      calories: 0,
      protein: 0,
      carbohydrates: 0,
      fat: 0,
      fiber: 0,
      sugar: 0,
      sodium: 0,
      isEstimated: true,
    },
    images: appItem.image ? [{
      id: 'primary',
      url: appItem.image,
      isPrimary: true,
      altText: appItem.name,
      metadata: {
        width: 400,
        height: 300,
        size: 0,
        format: 'jpg',
      },
    }] : [],
    availability: {
      isAlwaysAvailable: true,
      customSchedule: [],
      isSeasonalItem: false,
      seasonalAvailability: [],
    },
    customizations: appItem.customizations?.map(customization => ({
      id: customization.id,
      name: customization.name,
      description: '',
      isRequired: customization.required,
      allowMultiple: customization.type === 'multiple',
      minSelections: customization.required ? 1 : 0,
      maxSelections: customization.type === 'multiple' ? 10 : 1,
      options: customization.options.map(option => ({
        id: option.id,
        name: option.name,
        description: '',
        priceAdjustment: option.price,
        isAvailable: true,
        isDefault: false,
      })),
    })) || [],
    tags: [
      ...(appItem.isVegetarian ? ['Vegetarian'] : []),
      ...(appItem.isVegan ? ['Vegan'] : []),
      ...(appItem.isGlutenFree ? ['Gluten-Free'] : []),
    ],
    isActive: appItem.isAvailable,
    isDraft: false,
    version: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'current-user',
    seoOptimized: false,
    popularityScore: 0,
    profitMargin: 0.3, // Default 30% profit margin
  };
}

/**
 * Validates that a form MenuItem has all required fields for conversion
 */
export function validateFormMenuItemForConversion(formItem: Partial<FormMenuItem>): string[] {
  const errors: string[] = [];

  if (!formItem.name?.trim()) {
    errors.push('Name is required');
  }

  if (!formItem.description?.trim()) {
    errors.push('Description is required');
  }

  if (!formItem.category?.trim()) {
    errors.push('Category is required');
  }

  if (!formItem.price || formItem.price.length === 0) {
    errors.push('At least one price tier is required');
  }

  if (formItem.preparationTime === undefined || formItem.preparationTime <= 0) {
    errors.push('Preparation time must be greater than 0');
  }

  return errors;
}
