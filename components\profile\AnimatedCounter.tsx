import React, { useEffect, useRef } from 'react';
import { Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { Colors, Typography } from '@/constants/Theme';

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  prefix?: string;
  suffix?: string;
  style?: any;
  textStyle?: any;
  decimals?: number;
  formatter?: (value: number) => string;
}

export const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  value,
  duration = 1500,
  prefix = '',
  suffix = '',
  style,
  textStyle,
  decimals = 0,
  formatter,
}) => {
  const animatedValue = useSharedValue(0);
  const displayValue = useSharedValue(0);

  const formatNumber = (num: number): string => {
    if (formatter) {
      return formatter(num);
    }
    
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    
    return num.toFixed(decimals);
  };

  const updateDisplayValue = (newValue: number) => {
    displayValue.value = newValue;
  };

  useEffect(() => {
    animatedValue.value = withTiming(value, { duration }, () => {
      runOnJS(updateDisplayValue)(value);
    });
  }, [value, duration]);

  const animatedStyle = useAnimatedStyle(() => {
    const currentValue = interpolate(
      animatedValue.value,
      [0, value],
      [0, value]
    );
    
    return {
      opacity: withTiming(1, { duration: 300 }),
      transform: [
        {
          scale: withTiming(1, { duration: 300 }),
        },
      ],
    };
  });

  const AnimatedText = Animated.createAnimatedComponent(Text);

  return (
    <Animated.View style={[styles.container, style, animatedStyle]}>
      <AnimatedText style={[styles.text, textStyle]}>
        {prefix}
        {formatNumber(interpolate(animatedValue.value, [0, value], [0, value]))}
        {suffix}
      </AnimatedText>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.light.text,
  },
});

// Progress Ring Component
interface ProgressRingProps {
  progress: number; // 0-100
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  children?: React.ReactNode;
  animated?: boolean;
  duration?: number;
}

export const ProgressRing: React.FC<ProgressRingProps> = ({
  progress,
  size = 100,
  strokeWidth = 8,
  color = Colors.light.primary,
  backgroundColor = Colors.light.border,
  children,
  animated = true,
  duration = 1000,
}) => {
  const animatedProgress = useSharedValue(0);
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;

  useEffect(() => {
    if (animated) {
      animatedProgress.value = withTiming(progress, { duration });
    } else {
      animatedProgress.value = progress;
    }
  }, [progress, animated, duration]);

  const animatedStyle = useAnimatedStyle(() => {
    const strokeDashoffset = circumference - (animatedProgress.value / 100) * circumference;
    
    return {
      strokeDashoffset: withTiming(strokeDashoffset, { duration: animated ? duration : 0 }),
    };
  });

  return (
    <Animated.View style={[styles.progressContainer, { width: size, height: size }]}>
      <Animated.View style={styles.svgContainer}>
        {/* Background Circle */}
        <Animated.View
          style={[
            styles.circle,
            {
              width: size,
              height: size,
              borderRadius: size / 2,
              borderWidth: strokeWidth,
              borderColor: backgroundColor,
            },
          ]}
        />
        
        {/* Progress Circle */}
        <Animated.View
          style={[
            styles.progressCircle,
            {
              width: size,
              height: size,
              borderRadius: size / 2,
              borderWidth: strokeWidth,
              borderColor: color,
              borderTopColor: 'transparent',
              borderRightColor: 'transparent',
              borderBottomColor: 'transparent',
            },
            animatedStyle,
          ]}
        />
      </Animated.View>
      
      {children && (
        <Animated.View style={styles.centerContent}>
          {children}
        </Animated.View>
      )}
    </Animated.View>
  );
};

// Gradient Card Component
interface GradientCardProps {
  children: React.ReactNode;
  colors?: string[];
  style?: any;
  borderRadius?: number;
  padding?: number;
  elevation?: number;
}

export const GradientCard: React.FC<GradientCardProps> = ({
  children,
  colors = [Colors.light.primary, Colors.light.primaryLight],
  style,
  borderRadius = 16,
  padding = 16,
  elevation = 4,
}) => {
  return (
    <Animated.View
      style={[
        styles.gradientCard,
        {
          borderRadius,
          padding,
          elevation,
          shadowColor: Colors.light.shadow,
          shadowOffset: { width: 0, height: elevation },
          shadowOpacity: 0.1,
          shadowRadius: elevation * 2,
        },
        style,
      ]}
    >
      {children}
    </Animated.View>
  );
};

// Rating Stars Component
interface RatingStarsProps {
  rating: number;
  maxRating?: number;
  size?: number;
  color?: string;
  emptyColor?: string;
  animated?: boolean;
  onRatingChange?: (rating: number) => void;
  readonly?: boolean;
}

export const RatingStars: React.FC<RatingStarsProps> = ({
  rating,
  maxRating = 5,
  size = 20,
  color = '#FFD700',
  emptyColor = Colors.light.border,
  animated = true,
  onRatingChange,
  readonly = true,
}) => {
  const animatedRating = useSharedValue(0);

  useEffect(() => {
    if (animated) {
      animatedRating.value = withTiming(rating, { duration: 800 });
    } else {
      animatedRating.value = rating;
    }
  }, [rating, animated]);

  const renderStar = (index: number) => {
    const animatedStyle = useAnimatedStyle(() => {
      const progress = Math.max(0, Math.min(1, animatedRating.value - index));
      
      return {
        opacity: withTiming(progress > 0 ? 1 : 0.3, { duration: 200 }),
        transform: [
          {
            scale: withTiming(progress > 0 ? 1 : 0.8, { duration: 200 }),
          },
        ],
      };
    });

    return (
      <Animated.View key={index} style={[styles.star, animatedStyle]}>
        <Text style={[styles.starText, { fontSize: size, color }]}>★</Text>
      </Animated.View>
    );
  };

  return (
    <Animated.View style={styles.starsContainer}>
      {Array.from({ length: maxRating }, (_, index) => renderStar(index))}
    </Animated.View>
  );
};

const progressStyles = StyleSheet.create({
  progressContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  svgContainer: {
    position: 'absolute',
  },
  circle: {
    position: 'absolute',
  },
  progressCircle: {
    position: 'absolute',
    transform: [{ rotate: '-90deg' }],
  },
  centerContent: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  gradientCard: {
    backgroundColor: Colors.light.surface,
  },
  starsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  star: {
    marginHorizontal: 1,
  },
  starText: {
    fontWeight: 'bold',
  },
});

// Merge all styles
Object.assign(styles, progressStyles);
