import AsyncStorage from '@react-native-async-storage/async-storage';

// API Cache configuration
const API_CACHE_PREFIX = 'api_cache_';
const CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes default
const MAX_CACHE_SIZE = 50; // Maximum number of cached responses

interface CacheEntry {
  data: any;
  timestamp: number;
  expiryTime: number;
  etag?: string;
  lastModified?: string;
}

interface RequestConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  cache?: boolean;
  cacheTime?: number;
  retry?: number;
  timeout?: number;
  priority?: 'low' | 'normal' | 'high';
}

interface BatchRequest {
  id: string;
  config: RequestConfig;
  resolve: (value: any) => void;
  reject: (error: any) => void;
}

export class APIOptimizer {
  private static instance: APIOptimizer;
  private cache = new Map<string, CacheEntry>();
  private pendingRequests = new Map<string, Promise<any>>();
  private batchQueue: BatchRequest[] = [];
  private batchTimeout: NodeJS.Timeout | null = null;
  private isOnline = true;
  private offlineQueue: RequestConfig[] = [];

  static getInstance(): APIOptimizer {
    if (!APIOptimizer.instance) {
      APIOptimizer.instance = new APIOptimizer();
    }
    return APIOptimizer.instance;
  }

  constructor() {
    this.initializeNetworkListener();
    this.loadCacheFromStorage();
  }

  // Initialize network connectivity listener
  private initializeNetworkListener(): void {
    // Simple network detection using fetch
    const checkConnection = async () => {
      try {
        const response = await fetch('https://www.google.com/favicon.ico', {
          method: 'HEAD',
          cache: 'no-cache'
        });
        const wasOffline = !this.isOnline;
        this.isOnline = response.ok;

        // Process offline queue when coming back online
        if (wasOffline && this.isOnline) {
          this.processOfflineQueue();
        }
      } catch {
        this.isOnline = false;
      }
    };

    // Check connection every 30 seconds
    setInterval(checkConnection, 30000);
    checkConnection(); // Initial check
  }

  // Load cache from AsyncStorage
  private async loadCacheFromStorage(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(API_CACHE_PREFIX));
      
      for (const key of cacheKeys) {
        const cacheData = await AsyncStorage.getItem(key);
        if (cacheData) {
          const entry: CacheEntry = JSON.parse(cacheData);
          const cacheKey = key.replace(API_CACHE_PREFIX, '');
          
          // Check if cache entry is still valid
          if (Date.now() < entry.expiryTime) {
            this.cache.set(cacheKey, entry);
          } else {
            // Remove expired cache
            await AsyncStorage.removeItem(key);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load API cache:', error);
    }
  }

  // Generate cache key for request
  private generateCacheKey(config: RequestConfig): string {
    const { url, method = 'GET', body } = config;
    const bodyString = body ? JSON.stringify(body) : '';
    return `${method}_${url}_${bodyString}`;
  }

  // Check if request can be cached
  private canCache(config: RequestConfig): boolean {
    return config.cache !== false && (config.method === 'GET' || config.method === undefined);
  }

  // Get cached response
  private async getCachedResponse(cacheKey: string): Promise<any | null> {
    const entry = this.cache.get(cacheKey);
    
    if (entry && Date.now() < entry.expiryTime) {
      return entry.data;
    }
    
    if (entry) {
      // Remove expired cache
      this.cache.delete(cacheKey);
      await AsyncStorage.removeItem(API_CACHE_PREFIX + cacheKey);
    }
    
    return null;
  }

  // Cache response
  private async cacheResponse(
    cacheKey: string, 
    data: any, 
    cacheTime: number = CACHE_EXPIRY_TIME,
    headers?: Headers
  ): Promise<void> {
    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      expiryTime: Date.now() + cacheTime,
      etag: headers?.get('etag') || undefined,
      lastModified: headers?.get('last-modified') || undefined,
    };

    // Manage cache size
    if (this.cache.size >= MAX_CACHE_SIZE) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
      await AsyncStorage.removeItem(API_CACHE_PREFIX + oldestKey);
    }

    this.cache.set(cacheKey, entry);
    
    try {
      await AsyncStorage.setItem(API_CACHE_PREFIX + cacheKey, JSON.stringify(entry));
    } catch (error) {
      console.warn('Failed to cache response:', error);
    }
  }

  // Make optimized API request
  async request(config: RequestConfig): Promise<any> {
    const cacheKey = this.generateCacheKey(config);

    // Check for pending identical request
    if (this.pendingRequests.has(cacheKey)) {
      return this.pendingRequests.get(cacheKey);
    }

    // Check cache for GET requests
    if (this.canCache(config)) {
      const cachedResponse = await this.getCachedResponse(cacheKey);
      if (cachedResponse) {
        return cachedResponse;
      }
    }

    // Handle offline scenario
    if (!this.isOnline) {
      if (this.canCache(config)) {
        // Try to return stale cache for GET requests
        const staleCache = this.cache.get(cacheKey);
        if (staleCache) {
          console.warn('Returning stale cache due to offline status');
          return staleCache.data;
        }
      }
      
      // Queue non-GET requests for later
      if (config.method !== 'GET') {
        this.offlineQueue.push(config);
        throw new Error('Request queued for when online');
      }
      
      throw new Error('No network connection');
    }

    // Create request promise
    const requestPromise = this.executeRequest(config, cacheKey);
    this.pendingRequests.set(cacheKey, requestPromise);

    try {
      const response = await requestPromise;
      return response;
    } finally {
      this.pendingRequests.delete(cacheKey);
    }
  }

  // Execute the actual HTTP request
  private async executeRequest(config: RequestConfig, cacheKey: string): Promise<any> {
    const {
      url,
      method = 'GET',
      headers = {},
      body,
      timeout = 10000,
      retry = 2,
      cacheTime = CACHE_EXPIRY_TIME
    } = config;

    // Add conditional headers for cache validation
    const cachedEntry = this.cache.get(cacheKey);
    if (cachedEntry) {
      if (cachedEntry.etag) {
        headers['If-None-Match'] = cachedEntry.etag;
      }
      if (cachedEntry.lastModified) {
        headers['If-Modified-Since'] = cachedEntry.lastModified;
      }
    }

    // Add compression headers
    headers['Accept-Encoding'] = 'gzip, deflate, br';
    headers['Accept'] = 'application/json';

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    let lastError: Error;

    for (let attempt = 0; attempt <= retry; attempt++) {
      try {
        const response = await fetch(url, {
          method,
          headers,
          body: body ? JSON.stringify(body) : undefined,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Handle 304 Not Modified
        if (response.status === 304 && cachedEntry) {
          return cachedEntry.data;
        }

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // Cache successful GET responses
        if (this.canCache(config)) {
          await this.cacheResponse(cacheKey, data, cacheTime, response.headers);
        }

        return data;

      } catch (error) {
        lastError = error as Error;
        
        if (attempt < retry) {
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    clearTimeout(timeoutId);
    throw lastError!;
  }

  // Batch multiple requests
  batchRequest(config: RequestConfig): Promise<any> {
    return new Promise((resolve, reject) => {
      const batchRequest: BatchRequest = {
        id: Math.random().toString(36),
        config,
        resolve,
        reject
      };

      this.batchQueue.push(batchRequest);

      // Set batch timeout
      if (this.batchTimeout) {
        clearTimeout(this.batchTimeout);
      }

      this.batchTimeout = setTimeout(() => {
        this.processBatch();
      }, 50); // 50ms batch window
    });
  }

  // Process batched requests
  private async processBatch(): Promise<void> {
    const batch = [...this.batchQueue];
    this.batchQueue = [];
    this.batchTimeout = null;

    // Group requests by priority
    const highPriority = batch.filter(req => req.config.priority === 'high');
    const normalPriority = batch.filter(req => req.config.priority === 'normal' || !req.config.priority);
    const lowPriority = batch.filter(req => req.config.priority === 'low');

    // Process in priority order
    for (const group of [highPriority, normalPriority, lowPriority]) {
      await Promise.all(group.map(async (batchRequest) => {
        try {
          const result = await this.request(batchRequest.config);
          batchRequest.resolve(result);
        } catch (error) {
          batchRequest.reject(error);
        }
      }));
    }
  }

  // Process offline queue
  private async processOfflineQueue(): Promise<void> {
    const queue = [...this.offlineQueue];
    this.offlineQueue = [];

    for (const config of queue) {
      try {
        await this.request(config);
      } catch (error) {
        console.warn('Failed to process offline request:', error);
        // Re-queue failed requests
        this.offlineQueue.push(config);
      }
    }
  }

  // Clear cache
  async clearCache(): Promise<void> {
    this.cache.clear();
    
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(API_CACHE_PREFIX));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.warn('Failed to clear API cache:', error);
    }
  }

  // Get cache statistics
  getCacheStats(): {
    size: number;
    hitRate: number;
    totalRequests: number;
    cacheHits: number;
  } {
    // This would be implemented with proper tracking
    return {
      size: this.cache.size,
      hitRate: 0, // Would track this
      totalRequests: 0, // Would track this
      cacheHits: 0, // Would track this
    };
  }
}

// Export singleton instance
export const apiOptimizer = APIOptimizer.getInstance();

// Convenience functions
export const optimizedFetch = (config: RequestConfig) => apiOptimizer.request(config);
export const batchFetch = (config: RequestConfig) => apiOptimizer.batchRequest(config);
export const clearAPICache = () => apiOptimizer.clearCache();
