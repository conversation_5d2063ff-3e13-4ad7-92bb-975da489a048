import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { FormValidationError, Ingredient, MenuItem, NutritionalInfo } from '../../types';
import { generateNutritionalEstimate } from '../../utils/aiHelpers';

interface IngredientsNutritionStepProps {
  formData: Partial<MenuItem>;
  onUpdateField: (field: string, value: any) => void;
  onValidateField: (field: string) => Promise<FormValidationError[]>;
  errors: FormValidationError[];
}

export function IngredientsNutritionStep({
  formData,
  onUpdateField,
  onValidate<PERSON>ield,
  errors,
}: IngredientsNutritionStepProps) {
  const [isCalculatingNutrition, setIsCalculatingNutrition] = useState(false);
  const [ingredientSearch, setIngredientSearch] = useState('');
  const [showNutritionDetails, setShowNutritionDetails] = useState(false);
  
  const fadeAnimation = useSharedValue(0);
  const slideAnimation = useSharedValue(0);

  useEffect(() => {
    fadeAnimation.value = withTiming(1, { duration: 500 });
    slideAnimation.value = withSpring(1, { damping: 20, stiffness: 100 });
    
    // Initialize ingredients if not set
    if (!formData.ingredients) {
      onUpdateField('ingredients', []);
    }
    
    // Initialize allergens if not set
    if (!formData.allergens) {
      onUpdateField('allergens', []);
    }
    
    // Initialize nutrition if not set
    if (!formData.nutritionalInfo) {
      const defaultNutrition: NutritionalInfo = {
        calories: 0,
        protein: 0,
        carbohydrates: 0,
        fat: 0,
        fiber: 0,
        sugar: 0,
        sodium: 0,
        cholesterol: 0,
        servingSize: '1 portion',
        servingUnit: 'portion',
        servingsPerContainer: 1,
      };
      onUpdateField('nutritionalInfo', defaultNutrition);
    }
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
    transform: [{ translateY: (1 - slideAnimation.value) * 50 }],
  }));

  const getFieldError = (fieldName: string) => {
    return errors.find(error => error.field === fieldName);
  };

  const handleAddIngredient = () => {
    if (!ingredientSearch.trim()) return;
    
    const newIngredient: Ingredient = {
      id: Date.now().toString(),
      name: ingredientSearch.trim(),
      quantity: 1,
      unit: 'piece',
      cost: 0,
      category: 'other',
      isOptional: false,
      allergens: [],
      nutritionalValue: {
        calories: 0,
        protein: 0,
        carbohydrates: 0,
        fat: 0,
        fiber: 0,
        sugar: 0,
        sodium: 0,
        cholesterol: 0,
        servingSize: '1 unit',
        servingsPerContainer: 1,
      },
    };
    
    const currentIngredients = formData.ingredients || [];
    onUpdateField('ingredients', [...currentIngredients, newIngredient]);
    onValidateField('ingredients');
    setIngredientSearch('');
  };

  const handleUpdateIngredient = (ingredientId: string, field: string, value: any) => {
    const currentIngredients = formData.ingredients || [];
    const updatedIngredients = currentIngredients.map(ingredient =>
      ingredient.id === ingredientId
        ? { ...ingredient, [field]: value }
        : ingredient
    );
    onUpdateField('ingredients', updatedIngredients);
    onValidateField('ingredients');
  };

  const handleRemoveIngredient = (ingredientId: string) => {
    const currentIngredients = formData.ingredients || [];
    const updatedIngredients = currentIngredients.filter(
      ingredient => ingredient.id !== ingredientId
    );
    onUpdateField('ingredients', updatedIngredients);
  };

  const handleToggleAllergen = (allergen: string) => {
    const currentAllergens = formData.allergens || [];
    const updatedAllergens = currentAllergens.includes(allergen)
      ? currentAllergens.filter(a => a !== allergen)
      : [...currentAllergens, allergen];
    onUpdateField('allergens', updatedAllergens);
    onValidateField('allergens');
  };

  const handleCalculateNutrition = async () => {
    setIsCalculatingNutrition(true);
    try {
      const ingredients = formData.ingredients || [];
      const estimatedNutrition = await generateNutritionalEstimate(ingredients, '1 portion');
      onUpdateField('nutritionalInfo', estimatedNutrition);
      onValidateField('nutritionalInfo');
      Alert.alert('Success', 'Nutritional information calculated successfully!');
    } catch (_error) {
      Alert.alert('Error', 'Failed to calculate nutrition. Please try again.');
    } finally {
      setIsCalculatingNutrition(false);
    }
  };

  const handleNutritionChange = (field: string, value: string) => {
    const currentNutrition = formData.nutritionalInfo || {};
    const numericValue = parseFloat(value) || 0;
    const updatedNutrition = {
      ...currentNutrition,
      [field]: numericValue,
    };
    onUpdateField('nutritionalInfo', updatedNutrition);
    onValidateField('nutritionalInfo');
  };

  const commonAllergens = [
    'Gluten', 'Dairy', 'Eggs', 'Nuts', 'Peanuts', 'Soy', 'Fish', 'Shellfish'
  ];

  const ingredientCategories = [
    'protein', 'vegetable', 'grain', 'dairy', 'spice', 'sauce', 'other'
  ];

  const ingredientsError = getFieldError('ingredients');
  const allergensError = getFieldError('allergens');
  const nutritionError = getFieldError('nutritionalInfo');

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Ingredients & Nutrition</Text>
          <Text style={styles.subtitle}>
            Add ingredients and nutritional information for your menu item
          </Text>
        </View>

        {/* Ingredients Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ingredients</Text>
          
          {/* Add Ingredient */}
          <View style={styles.addIngredientContainer}>
            <TextInput
              style={styles.ingredientSearchInput}
              value={ingredientSearch}
              onChangeText={setIngredientSearch}
              placeholder="Type ingredient name..."
              placeholderTextColor={Colors.light.textTertiary}
              onSubmitEditing={handleAddIngredient}
            />
            <TouchableOpacity
              style={styles.addIngredientButton}
              onPress={handleAddIngredient}
            >
              <IconSymbol
                name="plus"
                size={20}
                color={Colors.light.textInverse}
              />
            </TouchableOpacity>
          </View>

          {/* Ingredients List */}
          {(formData.ingredients || []).map((ingredient, _index) => (
            <IngredientCard
              key={ingredient.id}
              ingredient={ingredient}
              onUpdate={(field, value) => handleUpdateIngredient(ingredient.id, field, value)}
              onRemove={() => handleRemoveIngredient(ingredient.id)}
              categories={ingredientCategories}
            />
          ))}

          {ingredientsError && (
            <Text style={styles.errorText}>{ingredientsError.message}</Text>
          )}
        </View>

        {/* Allergens Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Allergens</Text>
          <Text style={styles.sectionSubtitle}>
            Select all allergens present in this dish
          </Text>
          
          <View style={styles.allergensGrid}>
            {commonAllergens.map((allergen) => (
              <TouchableOpacity
                key={allergen}
                style={[
                  styles.allergenChip,
                  (formData.allergens || []).includes(allergen) && styles.allergenChipSelected,
                ]}
                onPress={() => handleToggleAllergen(allergen)}
              >
                <Text
                  style={[
                    styles.allergenChipText,
                    (formData.allergens || []).includes(allergen) && styles.allergenChipTextSelected,
                  ]}
                >
                  {allergen}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {allergensError && (
            <Text style={styles.errorText}>{allergensError.message}</Text>
          )}
        </View>

        {/* Nutrition Section */}
        <View style={styles.section}>
          <View style={styles.nutritionHeader}>
            <Text style={styles.sectionTitle}>Nutritional Information</Text>
            <TouchableOpacity
              style={[
                styles.calculateButton,
                isCalculatingNutrition && styles.calculateButtonDisabled,
              ]}
              onPress={handleCalculateNutrition}
              disabled={isCalculatingNutrition}
            >
              {isCalculatingNutrition ? (
                <ActivityIndicator size="small" color={Colors.light.textInverse} />
              ) : (
                <IconSymbol
                  name="auto-fix"
                  size={16}
                  color={Colors.light.textInverse}
                />
              )}
              <Text style={styles.calculateButtonText}>
                {isCalculatingNutrition ? 'Calculating...' : 'AI Calculate'}
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.nutritionToggle}
            onPress={() => setShowNutritionDetails(!showNutritionDetails)}
          >
            <Text style={styles.nutritionToggleText}>
              {showNutritionDetails ? 'Hide Details' : 'Show Details'}
            </Text>
            <IconSymbol
              name={showNutritionDetails ? 'chevron.up' : 'chevron.down'}
              size={16}
              color={Colors.light.primary}
            />
          </TouchableOpacity>

          {/* Basic Nutrition */}
          <View style={styles.nutritionGrid}>
            <NutritionField
              label="Calories"
              value={formData.nutritionalInfo?.calories?.toString() || ''}
              onChangeText={(value) => handleNutritionChange('calories', value)}
              unit="kcal"
            />
            <NutritionField
              label="Protein"
              value={formData.nutritionalInfo?.protein?.toString() || ''}
              onChangeText={(value) => handleNutritionChange('protein', value)}
              unit="g"
            />
          </View>

          {showNutritionDetails && (
            <>
              <View style={styles.nutritionGrid}>
                <NutritionField
                  label="Carbs"
                  value={formData.nutritionalInfo?.carbohydrates?.toString() || ''}
                  onChangeText={(value) => handleNutritionChange('carbohydrates', value)}
                  unit="g"
                />
                <NutritionField
                  label="Fat"
                  value={formData.nutritionalInfo?.fat?.toString() || ''}
                  onChangeText={(value) => handleNutritionChange('fat', value)}
                  unit="g"
                />
              </View>
              
              <View style={styles.nutritionGrid}>
                <NutritionField
                  label="Fiber"
                  value={formData.nutritionalInfo?.fiber?.toString() || ''}
                  onChangeText={(value) => handleNutritionChange('fiber', value)}
                  unit="g"
                />
                <NutritionField
                  label="Sugar"
                  value={formData.nutritionalInfo?.sugar?.toString() || ''}
                  onChangeText={(value) => handleNutritionChange('sugar', value)}
                  unit="g"
                />
              </View>
              
              <NutritionField
                label="Sodium"
                value={formData.nutritionalInfo?.sodium?.toString() || ''}
                onChangeText={(value) => handleNutritionChange('sodium', value)}
                unit="mg"
                fullWidth
              />
            </>
          )}

          {nutritionError && (
            <Text style={styles.errorText}>{nutritionError.message}</Text>
          )}
        </View>
      </ScrollView>
    </Animated.View>
  );
}

interface IngredientCardProps {
  ingredient: Ingredient;
  onUpdate: (field: string, value: any) => void;
  onRemove: () => void;
  categories: string[];
}

function IngredientCard({ ingredient, onUpdate, onRemove, categories: _categories }: IngredientCardProps) {
  return (
    <View style={styles.ingredientCard}>
      <View style={styles.ingredientHeader}>
        <Text style={styles.ingredientName}>{ingredient.name}</Text>
        <TouchableOpacity style={styles.removeIngredientButton} onPress={onRemove}>
          <IconSymbol name="trash" size={16} color={Colors.light.error} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.ingredientDetails}>
        <View style={styles.quantityContainer}>
          <TextInput
            style={styles.quantityInput}
            value={ingredient.quantity?.toString() || ''}
            onChangeText={(text) => onUpdate('quantity', parseFloat(text) || 0)}
            placeholder="1"
            keyboardType="decimal-pad"
          />
          <TextInput
            style={styles.unitInput}
            value={ingredient.unit || ''}
            onChangeText={(text) => onUpdate('unit', text)}
            placeholder="unit"
          />
        </View>
        
        <View style={styles.ingredientOptions}>
          <TouchableOpacity
            style={styles.optionalToggle}
            onPress={() => onUpdate('isOptional', !ingredient.isOptional)}
          >
            <Switch
              value={ingredient.isOptional}
              onValueChange={(value) => onUpdate('isOptional', value)}
              trackColor={{
                false: Colors.light.border,
                true: Colors.light.primaryLight,
              }}
              thumbColor={
                ingredient.isOptional ? Colors.light.primary : Colors.light.textTertiary
              }
            />
            <Text style={styles.optionalLabel}>Optional</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

interface NutritionFieldProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  unit: string;
  fullWidth?: boolean;
}

function NutritionField({ label, value, onChangeText, unit, fullWidth }: NutritionFieldProps) {
  return (
    <View style={[styles.nutritionField, fullWidth && styles.nutritionFieldFull]}>
      <Text style={styles.nutritionLabel}>{label}</Text>
      <View style={styles.nutritionInputContainer}>
        <TextInput
          style={styles.nutritionInput}
          value={value}
          onChangeText={onChangeText}
          placeholder="0"
          keyboardType="decimal-pad"
        />
        <Text style={styles.nutritionUnit}>{unit}</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    lineHeight: 22,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 16,
    lineHeight: 18,
  },
  addIngredientContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  ingredientSearchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.light.surface,
  },
  addIngredientButton: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  ingredientCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  ingredientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  ingredientName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    flex: 1,
  },
  removeIngredientButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: Colors.light.errorLight,
  },
  ingredientDetails: {
    gap: 12,
  },
  quantityContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  quantityInput: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    textAlign: 'center',
  },
  unitInput: {
    flex: 2,
    fontSize: 14,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  ingredientOptions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionalToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  optionalLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  allergensGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  allergenChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  allergenChipSelected: {
    backgroundColor: Colors.light.error,
    borderColor: Colors.light.error,
  },
  allergenChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  allergenChipTextSelected: {
    color: Colors.light.textInverse,
  },
  nutritionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  calculateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: Colors.light.primary,
    gap: 4,
  },
  calculateButtonDisabled: {
    opacity: 0.7,
  },
  calculateButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  nutritionToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    marginBottom: 16,
    gap: 4,
  },
  nutritionToggleText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary, // Keep primary color for toggle text
  },
  nutritionGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  nutritionField: {
    flex: 1,
  },
  nutritionFieldFull: {
    flex: 1,
    width: '100%',
  },
  nutritionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  nutritionInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    backgroundColor: Colors.light.surface,
  },
  nutritionInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    paddingHorizontal: 12,
    paddingVertical: 8,
    textAlign: 'center',
  },
  nutritionUnit: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    paddingHorizontal: 8,
    fontWeight: '500',
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.error,
    marginTop: 4,
  },
});

export default IngredientsNutritionStep;
