import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth, usePermissions } from '@/contexts/AuthContext';
import { UserRole } from '@/types/auth';
import { router } from 'expo-router';
import React, { ReactNode } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: UserRole | UserRole[];
  requiredPermission?: string;
  fallback?: ReactNode;
}

export function ProtectedRoute({ 
  children, 
  requiredRole, 
  requiredPermission,
  fallback 
}: ProtectedRouteProps) {
  const { isAuthenticated, hasRole, user } = useAuth();
  const { canAccess } = usePermissions();

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <LoginRedirect />;
  }

  // Check role-based access
  if (requiredRole && !hasRole(requiredRole)) {
    return fallback || <AccessDenied userRole={user?.role} requiredRole={requiredRole} />;
  }

  // Check permission-based access
  if (requiredPermission && !canAccess(requiredPermission)) {
    return fallback || <AccessDenied userRole={user?.role} requiredPermission={requiredPermission} />;
  }

  return <>{children}</>;
}

function LoginRedirect() {
  const handleLoginRedirect = () => {
    router.replace('/login');
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <IconSymbol name="lock.fill" size={64} color={Colors.light.textTertiary} />
        <Text style={styles.title}>Authentication Required</Text>
        <Text style={styles.message}>
          Please log in to access this page
        </Text>
        <TouchableOpacity style={styles.button} onPress={handleLoginRedirect}>
          <Text style={styles.buttonText}>Go to Login</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

interface AccessDeniedProps {
  userRole?: UserRole;
  requiredRole?: UserRole | UserRole[];
  requiredPermission?: string;
}

function AccessDenied({ userRole, requiredRole, requiredPermission }: AccessDeniedProps) {
  const handleGoBack = () => {
    router.back();
  };

  const handleGoToDashboard = () => {
    router.replace('/(tabs)');
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <IconSymbol name="exclamationmark.triangle.fill" size={64} color={Colors.light.warning} />
        <Text style={styles.title}>Access Denied</Text>
        <Text style={styles.message}>
          You don't have permission to access this page.
        </Text>
        <View style={styles.details}>
          <Text style={styles.detailText}>Your role: <Text style={styles.roleText}>{userRole}</Text></Text>
          {requiredRole && (
            <Text style={styles.detailText}>
              Required role: <Text style={styles.roleText}>
                {Array.isArray(requiredRole) ? requiredRole.join(', ') : requiredRole}
              </Text>
            </Text>
          )}
          {requiredPermission && (
            <Text style={styles.detailText}>
              Required permission: <Text style={styles.roleText}>{requiredPermission}</Text>
            </Text>
          )}
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={handleGoBack}>
            <Text style={[styles.buttonText, styles.secondaryButtonText]}>Go Back</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={handleGoToDashboard}>
            <Text style={styles.buttonText}>Dashboard</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: 400,
    width: '100%',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  details: {
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    width: '100%',
  },
  detailText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  roleText: {
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  button: {
    flex: 1,
    backgroundColor: Colors.light.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  buttonText: {
    color: Colors.light.textInverse,
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: Colors.light.text,
  },
});
