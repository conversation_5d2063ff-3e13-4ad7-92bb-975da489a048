import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

interface FormHeaderProps {
  title: string;
  subtitle?: string;
  onCancel: () => void;
  onSaveDraft: () => void;
  isDirty: boolean;
  isSubmitting: boolean;
}

export function FormHeader({
  title,
  subtitle,
  onCancel,
  onSaveDraft,
  isDirty: _isDirty,
  isSubmitting,
}: FormHeaderProps) {
  const subtitleAnimation = useSharedValue(0);
  const draftButtonScale = useSharedValue(1);

  React.useEffect(() => {
    if (subtitle) {
      subtitleAnimation.value = withTiming(1, { duration: 300 });
    }
  }, [subtitle]);

  const animatedSubtitleStyle = useAnimatedStyle(() => ({
    opacity: subtitleAnimation.value,
    transform: [{ translateY: (1 - subtitleAnimation.value) * 10 }],
  }));

  const animatedDraftButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: draftButtonScale.value }],
  }));

  const handleDraftPress = () => {
    draftButtonScale.value = withSpring(0.95, { damping: 15, stiffness: 300 });
    setTimeout(() => {
      draftButtonScale.value = withSpring(1, { damping: 15, stiffness: 300 });
    }, 100);
    onSaveDraft();
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerContent}>
        {/* Cancel Button */}
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onCancel}
          disabled={isSubmitting}
        >
          <IconSymbol
            name="xmark"
            size={24}
            color={isSubmitting ? Colors.light.textTertiary : Colors.light.text}
          />
        </TouchableOpacity>

        {/* Title and Subtitle */}
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{title}</Text>
          {subtitle && (
            <Animated.View style={animatedSubtitleStyle}>
              <Text style={styles.subtitle}>{subtitle}</Text>
            </Animated.View>
          )}
        </View>

        {/* Save Draft Button */}
        <Animated.View style={animatedDraftButtonStyle}>
          <TouchableOpacity
            style={[
              styles.draftButton,
              !isSubmitting && styles.draftButtonActive,
              isSubmitting && styles.draftButtonDisabled,
            ]}
            onPress={handleDraftPress}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator
                size="small"
                color={Colors.light.textTertiary}
              />
            ) : (
              <>
                <IconSymbol
                  name="bookmark"
                  size={16}
                  color={
                    !isSubmitting
                      ? Colors.light.primary
                      : Colors.light.textTertiary
                  }
                />
                <Text
                  style={[
                    styles.draftButtonText,
                    !isSubmitting && styles.draftButtonTextActive,
                  ]}
                >
                  Save Draft
                </Text>
              </>
            )}
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Divider */}
      <View style={styles.divider} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 6, // Reduced from 8
    paddingBottom: 12, // Reduced from 16
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 2, // Reduced from 4
  },
  cancelButton: {
    padding: 6, // Reduced from 8
    borderRadius: 6, // Reduced from 8
    backgroundColor: Colors.light.backgroundSecondary,
    minWidth: 36, // Reduced from 40
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 12, // Reduced from 16
  },
  title: {
    fontSize: 18, // Reduced from 20
    fontWeight: 'bold',
    color: Colors.light.text,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 12, // Reduced from 14
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginTop: 1, // Reduced from 2
  },
  draftButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: Colors.light.primary, // Use primary color for better contrast with white text
    borderWidth: 1,
    borderColor: Colors.light.primary,
    minWidth: 100,
    justifyContent: 'center',
  },
  draftButtonActive: {
    backgroundColor: Colors.light.primaryDark, // Use darker primary for active state
    borderColor: Colors.light.primaryDark,
  },
  draftButtonDisabled: {
    opacity: 0.5,
  },
  draftButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF', // Snow white color
    marginLeft: 4,
  },
  draftButtonTextActive: {
    color: '#FFFFFF', // Snow white color
  },
  divider: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginTop: 16,
    marginHorizontal: -16,
  },
});

export default FormHeader;
