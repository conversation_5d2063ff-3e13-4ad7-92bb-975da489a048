import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { FormValidationError, MenuItem } from '../../types';

interface MarketingStepProps {
  formData: Partial<MenuItem>;
  onUpdateField: (field: string, value: any) => void;
  onValidateField: (field: string) => Promise<FormValidationError[]>;
  errors: FormValidationError[];
}

export function MarketingStep({
  formData,
  onUpdateField,
  onValidateField,
  errors,
}: MarketingStepProps) {
  const [isOptimizingSEO, setIsOptimizingSEO] = useState(false);
  const [tagInput, setTagInput] = useState('');
  
  const fadeAnimation = useSharedValue(0);
  const slideAnimation = useSharedValue(0);

  useEffect(() => {
    fadeAnimation.value = withTiming(1, { duration: 500 });
    slideAnimation.value = withSpring(1, { damping: 20, stiffness: 100 });
    
    // Initialize marketing fields if not set
    if (!formData.tags) {
      onUpdateField('tags', []);
    }
    if (!(formData as any).seoKeywords) {
      onUpdateField('seoKeywords', []);
    }
    if (!(formData as any).socialMediaDescription) {
      onUpdateField('socialMediaDescription', '');
    }
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
    transform: [{ translateY: (1 - slideAnimation.value) * 50 }],
  }));

  const getFieldError = (fieldName: string) => {
    return errors.find(error => error.field === fieldName);
  };

  const handleAddTag = () => {
    if (!tagInput.trim()) return;
    
    const currentTags = formData.tags || [];
    if (currentTags.includes(tagInput.trim())) {
      Alert.alert('Duplicate Tag', 'This tag already exists.');
      return;
    }
    
    onUpdateField('tags', [...currentTags, tagInput.trim()]);
    onValidateField('tags');
    setTagInput('');
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = formData.tags || [];
    const updatedTags = currentTags.filter(tag => tag !== tagToRemove);
    onUpdateField('tags', updatedTags);
  };

  const handleOptimizeSEO = async () => {
    setIsOptimizingSEO(true);
    try {
      // Mock SEO optimization - in production would use AI service
      
      // Mock AI optimization with realistic suggestions
      const menuItemName = formData.name || '';
      const menuItemCategory = formData.category || '';

      // Generate mock SEO data based on item name and category
      const seoData = {
        keywords: `${menuItemName}, ${menuItemCategory}, restaurant, food delivery, Pakistani cuisine, fresh ingredients`,
        socialDescription: `Delicious ${menuItemName} - A perfect ${menuItemCategory.toLowerCase()} made with fresh ingredients. Order now for the best taste experience! 🍽️ #FoodDelivery #${menuItemCategory}`,
        suggestedTags: ['popular', 'fresh', 'tasty', 'recommended']
      };
      
      onUpdateField('seoKeywords', seoData.keywords);
      onUpdateField('socialMediaDescription', seoData.socialDescription);
      
      // Add suggested tags
      const currentTags = formData.tags || [];
      const newTags = [...new Set([...currentTags, ...seoData.suggestedTags])];
      onUpdateField('tags', newTags);
      
      onValidateField('seoKeywords');
      onValidateField('socialMediaDescription');
      onValidateField('tags');
      
      Alert.alert('Success', 'SEO optimization completed successfully!');
    } catch (_error) {
      Alert.alert('Error', 'Failed to optimize SEO. Please try again.');
    } finally {
      setIsOptimizingSEO(false);
    }
  };

  const handleToggleFeature = (field: string, value: boolean) => {
    onUpdateField(field, value);
    onValidateField(field);
  };

  const popularTags = [
    'Spicy', 'Vegetarian', 'Vegan', 'Gluten-Free', 'Healthy', 'Comfort Food',
    'Signature', 'Popular', 'New', 'Limited Time', 'Chef Special', 'Organic'
  ];

  const tagsError = getFieldError('tags');
  const seoError = getFieldError('seoKeywords');
  const socialError = getFieldError('socialMediaDescription');

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Marketing & SEO</Text>
          <Text style={styles.subtitle}>
            Optimize your menu item for better visibility and engagement
          </Text>
        </View>

        {/* AI Optimization Button */}
        <View style={styles.aiOptimizeSection}>
          <TouchableOpacity
            style={[
              styles.optimizeButton,
              isOptimizingSEO && styles.optimizeButtonDisabled,
            ]}
            onPress={handleOptimizeSEO}
            disabled={isOptimizingSEO}
          >
            {isOptimizingSEO ? (
              <ActivityIndicator size="small" color={Colors.light.textInverse} />
            ) : (
              <IconSymbol
                name="auto-fix"
                size={16}
                color={Colors.light.textInverse}
              />
            )}
            <Text style={styles.optimizeButtonText}>
              {isOptimizingSEO ? 'Optimizing...' : 'AI Optimize'}
            </Text>
          </TouchableOpacity>
          <Text style={styles.aiOptimizeDescription}>
            Let AI automatically optimize your tags, keywords, and descriptions
          </Text>
        </View>

        {/* Tags Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tags (Optional)</Text>
          <Text style={styles.sectionSubtitle}>
            Add tags to help customers find your item
          </Text>
          
          {/* Add Tag Input */}
          <View style={styles.addTagContainer}>
            <TextInput
              style={styles.tagInput}
              value={tagInput}
              onChangeText={setTagInput}
              placeholder="Enter a tag..."
              placeholderTextColor={Colors.light.textTertiary}
              onSubmitEditing={handleAddTag}
            />
            <TouchableOpacity
              style={[
                styles.addTagButton,
                !tagInput.trim() && styles.addTagButtonDisabled,
              ]}
              onPress={handleAddTag}
              disabled={!tagInput.trim()}
            >
              <IconSymbol
                name="plus"
                size={18}
                color={!tagInput.trim() ? Colors.light.textTertiary : Colors.light.textInverse}
              />
            </TouchableOpacity>
          </View>

          {/* Current Tags */}
          {(formData.tags || []).length > 0 && (
            <View style={styles.tagsContainer}>
              {(formData.tags || []).map((tag, index) => (
                <View key={index} style={styles.tagChip}>
                  <Text style={styles.tagText}>{tag}</Text>
                  <TouchableOpacity
                    style={styles.removeTagButton}
                    onPress={() => handleRemoveTag(tag)}
                  >
                    <IconSymbol
                      name="xmark"
                      size={12}
                      color={Colors.light.textInverse}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}

          {/* Popular Tags */}
          <View style={styles.popularTagsSection}>
            <Text style={styles.popularTagsTitle}>Popular Tags</Text>
            <View style={styles.popularTagsContainer}>
              {popularTags.map((tag) => (
                <TouchableOpacity
                  key={tag}
                  style={[
                    styles.popularTagChip,
                    (formData.tags || []).includes(tag) && styles.popularTagChipSelected,
                  ]}
                  onPress={() => {
                    if ((formData.tags || []).includes(tag)) {
                      handleRemoveTag(tag);
                    } else {
                      const currentTags = formData.tags || [];
                      onUpdateField('tags', [...currentTags, tag]);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.popularTagText,
                      (formData.tags || []).includes(tag) && styles.popularTagTextSelected,
                    ]}
                  >
                    {tag}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {tagsError && (
            <Text style={styles.errorText}>{tagsError.message}</Text>
          )}
        </View>

        {/* SEO Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>SEO Optimization</Text>

          {/* SEO Keywords */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>SEO Keywords (Optional)</Text>
            <TextInput
              style={styles.textInput}
              value={Array.isArray((formData as any).seoKeywords) ? (formData as any).seoKeywords.join(', ') : ((formData as any).seoKeywords || '')}
              onChangeText={(text) => {
                const keywords = text.split(',').map(k => k.trim()).filter(k => k);
                onUpdateField('seoKeywords', keywords);
                onValidateField('seoKeywords');
              }}
              placeholder="Enter keywords separated by commas"
              placeholderTextColor={Colors.light.textTertiary}
              multiline
            />
            <Text style={styles.helperText}>
              Keywords help search engines understand your menu item
            </Text>
          </View>

          {seoError && (
            <Text style={styles.errorText}>{seoError.message}</Text>
          )}
        </View>

        {/* Social Media Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Social Media</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Social Media Description (Optional)</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={(formData as any).socialMediaDescription || ''}
              onChangeText={(text) => {
                onUpdateField('socialMediaDescription', text);
                onValidateField('socialMediaDescription');
              }}
              placeholder="Write an engaging description for social media posts..."
              placeholderTextColor={Colors.light.textTertiary}
              multiline
              numberOfLines={3}
              maxLength={280}
            />
            <Text style={styles.characterCount}>
              {((formData as any).socialMediaDescription || '').length}/280 characters
            </Text>
          </View>

          {socialError && (
            <Text style={styles.errorText}>{socialError.message}</Text>
          )}
        </View>

        {/* Features Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Special Features</Text>
          
          <View style={styles.featuresContainer}>
            <FeatureToggle
              title="Featured Item"
              description="Highlight this item on your menu"
              value={(formData as any).isFeatured || false}
              onToggle={(value) => handleToggleFeature('isFeatured', value)}
            />
            
            <FeatureToggle
              title="Chef's Special"
              description="Mark as a chef's recommendation"
              value={(formData as any).isChefSpecial || false}
              onToggle={(value) => handleToggleFeature('isChefSpecial', value)}
            />
            

            
            <FeatureToggle
              title="New Item"
              description="Mark as newly added to menu"
              value={(formData as any).isNew || false}
              onToggle={(value) => handleToggleFeature('isNew', value)}
            />
          </View>
        </View>

        {/* Marketing Tips */}
        <View style={styles.tipsContainer}>
          <View style={styles.tipsHeader}>
            <IconSymbol
              name="lightbulb"
              size={20}
              color={Colors.light.success}
            />
            <Text style={styles.tipsTitle}>Marketing Tips</Text>
          </View>
          <View style={styles.tipsList}>
            <Text style={styles.tipItem}>
              • Use descriptive tags that customers search for
            </Text>
            <Text style={styles.tipItem}>
              • Include dietary information in tags (vegan, gluten-free)
            </Text>
            <Text style={styles.tipItem}>
              • Write social descriptions that make people hungry
            </Text>
            <Text style={styles.tipItem}>
              • Use special features sparingly to maintain impact
            </Text>
            <Text style={styles.tipItem}>
              • Update seasonal items regularly
            </Text>
          </View>
        </View>
      </ScrollView>
    </Animated.View>
  );
}

interface FeatureToggleProps {
  title: string;
  description: string;
  value: boolean;
  onToggle: (value: boolean) => void;
}

function FeatureToggle({ title, description, value, onToggle }: FeatureToggleProps) {
  return (
    <View style={styles.featureToggle}>
      <View style={styles.featureContent}>
        <Text style={styles.featureTitle}>{title}</Text>
        <Text style={styles.featureDescription}>{description}</Text>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{
          false: Colors.light.border,
          true: Colors.light.successLight,
        }}
        thumbColor={
          value ? Colors.light.success : Colors.light.textTertiary
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    lineHeight: 22,
  },
  aiOptimizeSection: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
  },
  aiOptimizeDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 18,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 16,
    lineHeight: 18,
  },
  addTagContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  tagInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.light.surface,
  },
  addTagButton: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  addTagButtonDisabled: {
    backgroundColor: Colors.light.border,
    elevation: 0,
    shadowOpacity: 0,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  tagChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: Colors.light.primary,
    gap: 6,
  },
  tagText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  removeTagButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  popularTagsSection: {
    marginTop: 8,
  },
  popularTagsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  popularTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  popularTagChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  popularTagChipSelected: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  popularTagText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.textSecondary,
  },
  popularTagTextSelected: {
    color: Colors.light.textInverse,
    fontWeight: '600',
  },
  seoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  optimizeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: Colors.light.primary,
    gap: 4,
  },
  optimizeButtonDisabled: {
    opacity: 0.7,
  },
  optimizeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  textInput: {
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: Colors.light.surface,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    marginTop: 4,
    fontStyle: 'italic',
  },
  characterCount: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'right',
    marginTop: 4,
  },
  featuresContainer: {
    gap: 16,
  },
  featureToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  featureContent: {
    flex: 1,
    marginRight: 16,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  tipsContainer: {
    backgroundColor: Colors.light.successLight,
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: Colors.light.success,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 8,
  },
  tipsList: {
    gap: 4,
  },
  tipItem: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.error,
    marginTop: 4,
  },
});

export default MarketingStep;
