# API Integration Guide

This document outlines all the components that need API integration with the backend.

## 🔧 Services Overview

### 1. Authentication Service (`services/authService.ts`)
**Endpoints Required:**
- `POST /api/auth/register` - Restaurant registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/verification-status` - Check verification status
- `GET /api/auth/check-username/:username` - Username availability
- `GET /api/auth/check-email/:email` - Email availability

### 2. Registration Service (`services/registrationService.ts`)
**Endpoints Required:**
- `POST /api/auth/register` - Complete registration with documents
- `POST /api/documents/upload` - Upload individual documents
- `GET /api/auth/registration-status` - Get registration status
- `POST /api/auth/resubmit-documents` - Resubmit after rejection

### 3. Restaurant Service (`services/restaurantService.ts`)
**Endpoints Required:**
- `GET /api/restaurant/profile` - Get restaurant profile
- `PUT /api/restaurant/profile` - Update restaurant profile
- `GET /api/restaurant/stats` - Get restaurant statistics
- `GET /api/restaurant/settings` - Get restaurant settings
- `PUT /api/restaurant/settings` - Update restaurant settings
- `PATCH /api/restaurant/toggle-status` - Toggle open/close status
- `POST /api/restaurant/upload-image` - Upload restaurant images
- `DELETE /api/restaurant/delete-image` - Delete restaurant images
- `GET /api/restaurant/analytics/revenue` - Revenue analytics
- `GET /api/restaurant/analytics/top-items` - Top selling items
- `GET /api/restaurant/analytics/customers` - Customer analytics

### 4. Menu Service (`services/menuService.ts`)
**Endpoints Required:**
- `GET /api/menu/categories` - Get menu categories
- `POST /api/menu/categories` - Create category
- `PUT /api/menu/categories/:id` - Update category
- `DELETE /api/menu/categories/:id` - Delete category
- `GET /api/menu/items` - Get menu items
- `GET /api/menu/items/:id` - Get single menu item
- `POST /api/menu/items` - Create menu item
- `PUT /api/menu/items/:id` - Update menu item
- `DELETE /api/menu/items/:id` - Delete menu item
- `PATCH /api/menu/items/:id/availability` - Toggle availability
- `POST /api/menu/items/:id/upload-image` - Upload item image
- `DELETE /api/menu/items/:id/delete-image` - Delete item image
- `PATCH /api/menu/items/reorder` - Reorder items
- `PATCH /api/menu/categories/reorder` - Reorder categories
- `GET /api/menu/items/search` - Search menu items

### 5. Orders Service (`services/ordersService.ts`)
**Endpoints Required:**
- `GET /api/orders` - Get orders with pagination/filters
- `GET /api/orders/:id` - Get single order
- `PATCH /api/orders/:id/status` - Update order status
- `PATCH /api/orders/:id/delivery-time` - Update delivery time
- `PATCH /api/orders/:id/cancel` - Cancel order
- `PATCH /api/orders/:id/refund` - Refund order
- `GET /api/orders/stats` - Order statistics
- `GET /api/orders/active` - Active orders
- `GET /api/orders/search` - Search orders
- `GET /api/orders/customer/:id` - Orders by customer
- `PATCH /api/orders/bulk-update` - Bulk update orders
- `GET /api/orders/analytics` - Order analytics
- `POST /api/orders/:id/print` - Print order receipt

### 6. Staff Service (`services/staffService.ts`)
**Endpoints Required:**
- `GET /api/staff` - Get staff members
- `GET /api/staff/:id` - Get single staff member
- `POST /api/staff` - Create staff member
- `PUT /api/staff/:id` - Update staff member
- `DELETE /api/staff/:id` - Delete staff member
- `PATCH /api/staff/:id/status` - Toggle staff status
- `POST /api/staff/:id/reset-password` - Reset password
- `PATCH /api/staff/:id/permissions` - Update permissions
- `GET /api/staff/stats` - Staff statistics
- `GET /api/staff/activity` - Staff activity log
- `GET /api/staff/permissions` - Available permissions
- `PATCH /api/staff/bulk-status` - Bulk status update
- `DELETE /api/staff/bulk-delete` - Bulk delete
- `GET /api/staff/search` - Search staff
- `GET /api/staff/:id/performance` - Staff performance

### 7. Analytics Service (`services/analyticsService.ts`)
**Endpoints Required:**
- `GET /api/analytics/dashboard` - Dashboard statistics
- `GET /api/analytics/revenue` - Revenue analytics
- `GET /api/analytics/customers` - Customer analytics
- `GET /api/analytics/menu` - Menu analytics
- `GET /api/analytics/orders` - Order analytics
- `GET /api/analytics/financial` - Financial analytics
- `POST /api/analytics/export` - Export analytics
- `GET /api/analytics/realtime` - Real-time stats
- `POST /api/analytics/compare` - Comparative analytics
- `GET /api/analytics/forecast` - Revenue forecast

### 8. Notifications Service (`services/notificationsService.ts`)
**Endpoints Required:**
- `GET /api/notifications` - Get notifications
- `GET /api/notifications/unread-count` - Unread count
- `PATCH /api/notifications/:id/read` - Mark as read
- `PATCH /api/notifications/mark-all-read` - Mark all as read
- `DELETE /api/notifications/:id` - Delete notification
- `DELETE /api/notifications/delete-all` - Delete all
- `GET /api/notifications/settings` - Get settings
- `PUT /api/notifications/settings` - Update settings
- `POST /api/notifications/register-token` - Register push token
- `DELETE /api/notifications/unregister-token` - Unregister token
- `POST /api/notifications/test` - Send test notification
- `GET /api/notifications/history` - Notification history
- `PATCH /api/notifications/bulk-read` - Bulk mark as read
- `DELETE /api/notifications/bulk-delete` - Bulk delete
- `GET /api/notifications/templates` - Get templates
- `PUT /api/notifications/templates/:type` - Update template

## 🎯 Components Requiring API Integration

### 1. Dashboard Components
- **Dashboard Stats** (`app/(tabs)/index.tsx`)
  - Uses: `analyticsService.getDashboardStats()`
  - Real-time updates with polling

### 2. Orders Management
- **Orders List** (`app/(tabs)/orders.tsx`)
  - Uses: `ordersService.getOrders()` with pagination
  - Real-time active orders with `ordersService.getActiveOrders()`
- **Order Details** (Order detail screens)
  - Uses: `ordersService.getOrder(id)`
  - Status updates with `ordersService.updateOrderStatus()`

### 3. Menu Management
- **Menu Categories** (Menu management screens)
  - Uses: `menuService.getCategories()`
  - CRUD operations for categories
- **Menu Items** (Menu item forms)
  - Uses: `menuService.getMenuItems()`
  - CRUD operations for items
  - Image upload with `menuService.uploadItemImage()`

### 4. Staff Management
- **Staff List** (`app/staff-management.tsx`)
  - Uses: `staffService.getStaffMembers()`
  - Staff creation with `staffService.createStaffMember()`
- **Staff Details** (Staff detail screens)
  - Uses: `staffService.getStaffMember(id)`
  - Permission updates

### 5. Analytics
- **Analytics Dashboard** (`app/analytics.tsx`)
  - Uses: `analyticsService.getRevenueAnalytics()`
  - Multiple analytics endpoints for charts

### 6. Restaurant Settings
- **Restaurant Profile** (Settings screens)
  - Uses: `restaurantService.getRestaurant()`
  - Profile updates with `restaurantService.updateRestaurant()`
- **Restaurant Settings** (Settings screens)
  - Uses: `restaurantService.getSettings()`
  - Settings updates

### 7. Authentication
- **Login Screen** (`app/login.tsx`)
  - Uses: `authService.login()`
- **Registration Screen** (`app/(auth)/register.tsx`)
  - Uses: `registrationService.registerRestaurant()`
- **Verification Guard** (`components/auth/VerificationGuard.tsx`)
  - Uses: `authService.checkVerificationStatus()`

## 🔗 Usage Examples

### Using API Hooks in Components

```typescript
import { useRestaurantStats, useUpdateRestaurant } from '@/hooks/useRestaurantApi';

function DashboardComponent() {
  const { data: stats, loading, error, refetch } = useRestaurantStats('today');
  const { mutate: updateRestaurant, loading: updating } = useUpdateRestaurant();

  const handleUpdate = async (data) => {
    const result = await updateRestaurant(data);
    if (result.success) {
      refetch(); // Refresh stats
    }
  };

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} />;

  return (
    <View>
      <Text>Revenue: {stats?.totalRevenue}</Text>
      {/* Component content */}
    </View>
  );
}
```

### Direct Service Usage

```typescript
import { restaurantService } from '@/services';

async function handleRestaurantUpdate(data) {
  try {
    const response = await restaurantService.updateRestaurant(data);
    if (response.success) {
      console.log('Updated:', response.data);
    }
  } catch (error) {
    console.error('Update failed:', error.message);
  }
}
```

## 🚀 Implementation Priority

### Phase 1: Core Authentication
1. Authentication Service
2. Registration Service
3. Verification System

### Phase 2: Basic Operations
1. Restaurant Service
2. Menu Service (basic CRUD)
3. Orders Service (basic operations)

### Phase 3: Advanced Features
1. Staff Management
2. Analytics Service
3. Notifications Service

### Phase 4: Optimization
1. Real-time updates
2. Caching strategies
3. Performance optimization

## 📝 Backend Requirements

### Database Schema
- Users table (authentication)
- Restaurants table (restaurant data)
- Menu categories and items
- Orders and order items
- Staff members
- Notifications
- Analytics data

### File Storage
- Document uploads (registration)
- Image uploads (menu items, restaurant)
- Receipt generation

### Real-time Features
- WebSocket for live order updates
- Push notifications
- Real-time analytics

This comprehensive API integration setup provides a complete backend interface for your restaurant management app! 🍽️
