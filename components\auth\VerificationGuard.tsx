import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { VerificationPending } from './VerificationPending';

interface VerificationGuardProps {
  children: React.ReactNode;
}

export const VerificationGuard: React.FC<VerificationGuardProps> = ({ children }) => {
  const { user, verificationStatus, checkVerificationStatus } = useAuth();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkStatus = async () => {
      if (user) {
        setIsChecking(true);
        try {
          await checkVerificationStatus();
        } catch (error) {
          console.error('Error checking verification status:', error);
        } finally {
          setIsChecking(false);
        }
      } else {
        setIsChecking(false);
      }
    };

    checkStatus();
  }, [user, checkVerificationStatus]);

  // Show loading while checking verification
  if (isChecking) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#DC143C' }}>
        <ActivityIndicator size="large" color="#FFFFFF" />
      </View>
    );
  }

  // Show verification pending screen if user cannot access
  if (!verificationStatus.canAccess && verificationStatus.reason) {
    return (
      <VerificationPending 
        reason={verificationStatus.reason}
        onRetry={checkVerificationStatus}
      />
    );
  }

  // Show main app if verification passed
  return <>{children}</>;
};
