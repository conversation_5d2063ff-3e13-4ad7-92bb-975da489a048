import api, { ApiResponse, PaginatedResponse, handleApiError } from './api';

// Menu types
export interface MenuCategory {
  id: string;
  name: string;
  description?: string;
  displayOrder: number;
  isActive: boolean;
  restaurantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  categoryId: string;
  images: string[];
  isAvailable: boolean;
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  spiceLevel: 'mild' | 'medium' | 'hot' | 'extra-hot';
  preparationTime: number;
  calories?: number;
  ingredients: string[];
  allergens: string[];
  customizations: MenuCustomization[];
  displayOrder: number;
  restaurantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface MenuCustomization {
  id: string;
  name: string;
  type: 'single' | 'multiple';
  required: boolean;
  options: CustomizationOption[];
}

export interface CustomizationOption {
  id: string;
  name: string;
  price: number;
  isDefault: boolean;
}

export interface CreateMenuItemData {
  name: string;
  description: string;
  price: number;
  categoryId: string;
  images?: string[];
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  spiceLevel?: 'mild' | 'medium' | 'hot' | 'extra-hot';
  preparationTime: number;
  calories?: number;
  ingredients?: string[];
  allergens?: string[];
  customizations?: Omit<MenuCustomization, 'id'>[];
}

class MenuService {
  // Categories
  async getCategories(): Promise<ApiResponse<MenuCategory[]>> {
    try {
      const response = await api.get<ApiResponse<MenuCategory[]>>('/menu/categories');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async createCategory(data: Omit<MenuCategory, 'id' | 'restaurantId' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<MenuCategory>> {
    try {
      const response = await api.post<ApiResponse<MenuCategory>>('/menu/categories', data);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async updateCategory(id: string, data: Partial<MenuCategory>): Promise<ApiResponse<MenuCategory>> {
    try {
      const response = await api.put<ApiResponse<MenuCategory>>(`/menu/categories/${id}`, data);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async deleteCategory(id: string): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.delete<ApiResponse<{ message: string }>>(`/menu/categories/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Menu Items
  async getMenuItems(categoryId?: string): Promise<ApiResponse<MenuItem[]>> {
    try {
      const params = categoryId ? { categoryId } : {};
      const response = await api.get<ApiResponse<MenuItem[]>>('/menu/items', { params });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async getMenuItem(id: string): Promise<ApiResponse<MenuItem>> {
    try {
      const response = await api.get<ApiResponse<MenuItem>>(`/menu/items/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async createMenuItem(data: CreateMenuItemData): Promise<ApiResponse<MenuItem>> {
    try {
      const response = await api.post<ApiResponse<MenuItem>>('/menu/items', data);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async updateMenuItem(id: string, data: Partial<CreateMenuItemData>): Promise<ApiResponse<MenuItem>> {
    try {
      const response = await api.put<ApiResponse<MenuItem>>(`/menu/items/${id}`, data);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async deleteMenuItem(id: string): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.delete<ApiResponse<{ message: string }>>(`/menu/items/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async toggleItemAvailability(id: string, isAvailable: boolean): Promise<ApiResponse<MenuItem>> {
    try {
      const response = await api.patch<ApiResponse<MenuItem>>(`/menu/items/${id}/availability`, { isAvailable });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Upload menu item images
  async uploadItemImage(itemId: string, imageUri: string): Promise<ApiResponse<{ url: string }>> {
    try {
      const formData = new FormData();
      formData.append('image', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'menu_item_image.jpg',
      } as any);

      const response = await api.post<ApiResponse<{ url: string }>>(
        `/menu/items/${itemId}/upload-image`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Delete menu item image
  async deleteItemImage(itemId: string, imageUrl: string): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.delete<ApiResponse<{ message: string }>>(`/menu/items/${itemId}/delete-image`, {
        data: { imageUrl }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Bulk operations
  async updateItemsOrder(items: Array<{ id: string; displayOrder: number }>): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.patch<ApiResponse<{ message: string }>>('/menu/items/reorder', { items });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  async updateCategoriesOrder(categories: Array<{ id: string; displayOrder: number }>): Promise<ApiResponse<{ message: string }>> {
    try {
      const response = await api.patch<ApiResponse<{ message: string }>>('/menu/categories/reorder', { categories });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }

  // Search menu items
  async searchMenuItems(query: string): Promise<ApiResponse<MenuItem[]>> {
    try {
      const response = await api.get<ApiResponse<MenuItem[]>>('/menu/items/search', {
        params: { q: query }
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
}

export default new MenuService();
