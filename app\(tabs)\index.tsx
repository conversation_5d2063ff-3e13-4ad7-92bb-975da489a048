import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchDashboardStats } from '@/store/slices/dashboardSlice';
import { fetchRestaurantInfo, updateRestaurantStatus } from '@/store/slices/restaurantSlice';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, Dimensions, RefreshControl, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

const { width } = Dimensions.get('window');

// Premium Red & White Design System
const PremiumColors = {
  primary: '#DC143C',      // Main red (Crimson)
  primaryLight: '#EF4444', // Light red
  black: '#1A1A1A',        // Primary black
  white: '#FFFFFF',        // Pure white
  grayDark: '#6B7280',     // Dark gray
  success: '#DC143C',      // Red for success
  warning: '#DC143C',      // Red for warning
  danger: '#B91C1C',       // Dark red for danger
  orderUrgent: '#B91C1C',    // DARK RED (30+ minutes)
  orderProgress: '#DC143C',  // RED (15-29 minutes)
  orderOnTime: '#EF4444',    // LIGHT RED (0-14 minutes)
};

// Utility functions
const getOrderStatusColor = (minutes: number) => {
  if (minutes >= 30) return PremiumColors.orderUrgent;
  if (minutes >= 15) return PremiumColors.orderProgress;
  return PremiumColors.orderOnTime;
};

const getOrderStatusText = (minutes: number) => {
  if (minutes >= 30) return 'Urgent';
  if (minutes >= 15) return 'In Progress';
  return 'On Time';
};

export default function DashboardScreen() {
  return <DashboardContent />;
}

function DashboardContent() {
  const dispatch = useAppDispatch();
  const { stats, loading } = useAppSelector(state => state.dashboard);
  const { restaurant } = useAppSelector(state => state.restaurant);

  // Enhanced state management
  const [currentTime, setCurrentTime] = useState(new Date());
  const [notifications] = useState([
    { id: '1', title: 'New Order', message: 'Order #ORD-123 received', type: 'order', time: '2 min ago' },
    { id: '3', title: 'Review Alert', message: 'New 5-star review from Sarah M.', type: 'review', time: '1 hour ago' }
  ]);
  const [showNotifications, setShowNotifications] = useState(false);

  useEffect(() => {
    dispatch(fetchDashboardStats());
    dispatch(fetchRestaurantInfo());

    // Update time every minute
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timeInterval);
  }, [dispatch]);

  // Helper functions
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const getRevenueGrowth = (): string => {
    if (!stats) return '0';
    const thisWeekAvg = stats.thisWeek.revenue / 7;
    const todayRevenue = stats.today.revenue;
    return ((todayRevenue - thisWeekAvg) / thisWeekAvg * 100).toFixed(1);
  };

  const getOrderGrowth = (): string => {
    if (!stats) return '0';
    const thisWeekAvg = stats.thisWeek.orders / 7;
    const todayOrders = stats.today.orders;
    return ((todayOrders - thisWeekAvg) / thisWeekAvg * 100).toFixed(1);
  };

  const handleRefresh = () => {
    dispatch(fetchDashboardStats());
    dispatch(fetchRestaurantInfo());
  };

  const toggleRestaurantStatus = () => {
    if (restaurant) {
      dispatch(updateRestaurantStatus({
        isOpen: !restaurant.isOpen,
        acceptingOrders: !restaurant.isOpen
      }));
    }
  };

  const toggleOrderAcceptance = () => {
    if (restaurant) {
      dispatch(updateRestaurantStatus({
        acceptingOrders: !restaurant.acceptingOrders
      }));
    }
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={handleRefresh} />
      }
    >
      {/* Enhanced Header with Gradient Background */}
      <View style={styles.headerContainer}>
        <View style={styles.headerGradient}>
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <View style={styles.greetingContainer}>
                <Text style={styles.greetingText}>{getGreeting()}!</Text>
                <View style={styles.greetingIcon}>
                  <IconSymbol
                    name={currentTime.getHours() < 12 ? "sun.max.fill" : currentTime.getHours() < 17 ? "sun.haze.fill" : "moon.stars.fill"}
                    size={16}
                    color={Colors.light.warning}
                  />
                </View>
              </View>
              <Text style={styles.restaurantName}>{restaurant?.name || 'Restaurant'}</Text>
              <View style={styles.dateTimeContainer}>
                <IconSymbol name="calendar" size={14} color={Colors.light.textSecondary} />
                <Text style={styles.timeText}>{currentTime.toLocaleDateString('en-US', {
                  weekday: 'long',
                  month: 'short',
                  day: 'numeric'
                })}</Text>
              </View>
            </View>
            <View style={styles.headerRight}>
              <TouchableOpacity
                style={styles.notificationButton}
                onPress={() => setShowNotifications(!showNotifications)}
              >
                <View style={styles.iconContainer}>
                  <IconSymbol name="bell.fill" size={22} color={Colors.light.primary} />
                  {notifications.filter(n => n.type === 'order').length > 0 && (
                    <View style={styles.notificationBadge}>
                      <Text style={styles.notificationBadgeText}>
                        {notifications.filter(n => n.type === 'order').length}
                      </Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.statusButton, restaurant?.isOpen ? styles.openButton : styles.closedButton]}
                onPress={toggleRestaurantStatus}
              >
                <IconSymbol
                  name={restaurant?.isOpen ? "checkmark.circle.fill" : "xmark.circle.fill"}
                  size={16}
                  color={Colors.light.textInverse}
                />
                <Text style={styles.statusButtonText}>
                  {restaurant?.isOpen ? 'OPEN' : 'CLOSED'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Notifications Dropdown */}
      {showNotifications && (
        <View style={styles.notificationsDropdown}>
          <View style={styles.notificationsHeader}>
            <Text style={styles.notificationsTitle}>Notifications</Text>
            <TouchableOpacity onPress={() => setShowNotifications(false)}>
              <IconSymbol name="xmark" size={20} color={Colors.light.textSecondary} />
            </TouchableOpacity>
          </View>
          {notifications.map((notification) => (
            <TouchableOpacity key={notification.id} style={styles.notificationItem}>
              <View style={[styles.notificationIcon,
                notification.type === 'order' && styles.orderNotification,
                notification.type === 'inventory' && styles.inventoryNotification,
                notification.type === 'review' && styles.reviewNotification
              ]}>
                <IconSymbol
                  name={
                    notification.type === 'order' ? 'bag.fill' :
                    notification.type === 'inventory' ? 'exclamationmark.triangle.fill' :
                    'star.fill'
                  }
                  size={16}
                  color={Colors.light.textInverse}
                />
              </View>
              <View style={styles.notificationContent}>
                <Text style={styles.notificationTitle}>{notification.title}</Text>
                <Text style={styles.notificationMessage}>{notification.message}</Text>
                <Text style={styles.notificationTime}>{notification.time}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Enhanced Quick Actions Grid - 2x2 Layout */}
      <View style={styles.quickActionsGrid}>
        {/* Row 1 */}
        <View style={styles.quickActionsRow}>
          <TouchableOpacity
            style={[
              styles.quickActionCard,
              restaurant?.acceptingOrders ? styles.acceptingCard : styles.pausedCard
            ]}
            onPress={toggleOrderAcceptance}
          >
            <IconSymbol
              name={restaurant?.acceptingOrders ? "checkmark.circle.fill" : "xmark.circle.fill"}
              size={32}
              color={restaurant?.acceptingOrders ? Colors.light.success : Colors.light.error}
            />
            <Text style={styles.quickActionTitle}>Accepting Orders</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionCard}
            onPress={() => router.push('/(tabs)/menu')}
          >
            <IconSymbol name="menucard.fill" size={32} color={Colors.light.primary} />
            <Text style={styles.quickActionTitle}>Menu Manage</Text>
          </TouchableOpacity>
        </View>

        {/* Row 2 */}
        <View style={styles.quickActionsRow}>
          <TouchableOpacity
            style={styles.quickActionCard}
            onPress={() => router.push('/analytics')}
          >
            <IconSymbol name="chart.bar.xaxis" size={32} color={Colors.light.secondary} />
            <Text style={styles.quickActionTitle}>Analytics View</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionCard}
            onPress={() => {
              setTimeout(() => {
                router.push('/staff-management');
              }, 100);
            }}
          >
            <IconSymbol name="person.3.fill" size={32} color={Colors.light.accent} />
            <Text style={styles.quickActionTitle}>Staff Management</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Performance Metrics */}
      <View style={styles.performanceSection}>
        <Text style={styles.sectionTitle}>Today&apos;s Performance</Text>
        <View style={styles.performanceGrid}>
          <View style={styles.performanceCard}>
            <View style={styles.performanceHeader}>
              <IconSymbol name="dollarsign.circle.fill" size={24} color={Colors.light.success} />
              <View style={[styles.trendIndicator,
                parseFloat(getRevenueGrowth()) >= 0 ? styles.trendUp : styles.trendDown
              ]}>
                <IconSymbol
                  name={parseFloat(getRevenueGrowth()) >= 0 ? "arrow.up" : "arrow.down"}
                  size={12}
                  color={parseFloat(getRevenueGrowth()) >= 0 ? Colors.light.success : Colors.light.error}
                />
                <Text style={[styles.trendText,
                  parseFloat(getRevenueGrowth()) >= 0 ? styles.trendUpText : styles.trendDownText
                ]}>
                  {Math.abs(parseFloat(getRevenueGrowth()))}%
                </Text>
              </View>
            </View>
            <Text style={styles.performanceValue}>${stats?.today.revenue.toFixed(2) || '0.00'}</Text>
            <Text style={styles.performanceLabel}>Revenue</Text>
          </View>

          <View style={styles.performanceCard}>
            <View style={styles.performanceHeader}>
              <IconSymbol name="bag.fill" size={24} color={Colors.light.primary} />
              <View style={[styles.trendIndicator,
                parseFloat(getOrderGrowth()) >= 0 ? styles.trendUp : styles.trendDown
              ]}>
                <IconSymbol
                  name={parseFloat(getOrderGrowth()) >= 0 ? "arrow.up" : "arrow.down"}
                  size={12}
                  color={parseFloat(getOrderGrowth()) >= 0 ? Colors.light.success : Colors.light.error}
                />
                <Text style={[styles.trendText,
                  parseFloat(getOrderGrowth()) >= 0 ? styles.trendUpText : styles.trendDownText
                ]}>
                  {Math.abs(parseFloat(getOrderGrowth()))}%
                </Text>
              </View>
            </View>
            <Text style={styles.performanceValue}>{stats?.today.orders || 0}</Text>
            <Text style={styles.performanceLabel}>Orders</Text>
          </View>
        </View>
      </View>

      {/* Enhanced Order Status Cards */}
      <View style={styles.orderStatusSection}>
        <Text style={styles.sectionTitle}>Order Status</Text>
        <View style={styles.orderStatusGrid}>
          <TouchableOpacity style={[styles.statusCard, { backgroundColor: Colors.light.pending }]}>
            <IconSymbol name="clock.fill" size={32} color={Colors.light.textInverse} />
            <Text style={styles.statusCardNumber}>{stats?.pendingOrders || 0}</Text>
            <Text style={styles.statusCardLabel}>Pending</Text>
            <Text style={styles.statusCardSubtext}>Awaiting confirmation</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.statusCard, { backgroundColor: Colors.light.preparing }]}>
            <IconSymbol name="flame.fill" size={32} color={Colors.light.textInverse} />
            <Text style={styles.statusCardNumber}>{stats?.preparingOrders || 0}</Text>
            <Text style={styles.statusCardLabel}>Preparing</Text>
            <Text style={styles.statusCardSubtext}>In kitchen</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.statusCard, { backgroundColor: Colors.light.ready }]}>
            <IconSymbol name="checkmark.circle.fill" size={32} color={Colors.light.textInverse} />
            <Text style={styles.statusCardNumber}>{stats?.readyOrders || 0}</Text>
            <Text style={styles.statusCardLabel}>Ready</Text>
            <Text style={styles.statusCardSubtext}>For pickup/delivery</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Recent Orders Preview */}
      <View style={styles.recentOrdersSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Orders</Text>
          <TouchableOpacity onPress={() => Alert.alert('Orders', 'Navigate to orders page')}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.recentOrdersList}>
          {[
            { id: 'ORD-001', customer: 'John Doe', items: '2 items', total: 'PKR 2,499', status: 'preparing', time: '5 min ago', minutes: 5 },
            { id: 'ORD-002', customer: 'Sarah Smith', items: '1 item', total: 'PKR 1,250', status: 'ready', time: '12 min ago', minutes: 12 },
            { id: 'ORD-003', customer: 'Mike Johnson', items: '3 items', total: 'PKR 3,175', status: 'pending', time: '18 min ago', minutes: 18 },
            { id: 'ORD-004', customer: 'Lisa Brown', items: '4 items', total: 'PKR 4,200', status: 'urgent', time: '35 min ago', minutes: 35 }
          ].map((order) => (
            <TouchableOpacity key={order.id} style={[
              styles.recentOrderCard,
              {
                backgroundColor: `${getOrderStatusColor(order.minutes)}15`,
                borderLeftWidth: 4,
                borderLeftColor: getOrderStatusColor(order.minutes)
              }
            ]}>
              <View style={styles.orderCardLeft}>
                <View style={[styles.orderStatusDot, {
                  backgroundColor: getOrderStatusColor(order.minutes)
                }]} />
                <View style={styles.pulsingDot}>
                  <View style={[styles.pulseRing, { backgroundColor: getOrderStatusColor(order.minutes) }]} />
                </View>
                <View>
                  <Text style={styles.orderNumber}>{order.id}</Text>
                  <Text style={styles.orderCustomer}>{order.customer}</Text>
                  <Text style={styles.orderDetails}>{order.items} • {order.time}</Text>
                </View>
              </View>
              <View style={styles.orderCardRight}>
                <Text style={styles.orderTotal}>{order.total}</Text>
                <Text style={[styles.orderStatus, {
                  color: getOrderStatusColor(order.minutes),
                  fontWeight: 'bold'
                }]}>{getOrderStatusText(order.minutes)}</Text>
                <Text style={styles.orderTime}>{order.time}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Enhanced Revenue Stats - Three Rows */}
      <View style={styles.revenueSection}>
        <Text style={styles.sectionTitle}>Revenue Overview</Text>

        {/* Today's Revenue */}
        <View style={styles.revenueRowCard}>
          <IconSymbol name="calendar" size={24} color={Colors.light.primary} />
          <View style={styles.revenueRowContent}>
            <Text style={styles.revenueRowLabel}>Today</Text>
            <Text style={styles.revenueRowAmount}>PKR {stats?.today.revenue.toFixed(0) || '0'}</Text>
          </View>
          <Text style={styles.revenueRowOrders}>{stats?.today.orders || 0} orders</Text>
        </View>

        {/* This Week's Revenue */}
        <View style={styles.revenueRowCard}>
          <IconSymbol name="calendar.badge.clock" size={24} color={Colors.light.secondary} />
          <View style={styles.revenueRowContent}>
            <Text style={styles.revenueRowLabel}>This Week</Text>
            <Text style={styles.revenueRowAmount}>PKR {stats?.thisWeek.revenue.toFixed(0) || '0'}</Text>
          </View>
          <Text style={styles.revenueRowOrders}>{stats?.thisWeek.orders || 0} orders</Text>
        </View>

        {/* This Month's Revenue */}
        <View style={styles.revenueRowCard}>
          <IconSymbol name="calendar.badge.plus" size={24} color={Colors.light.accent} />
          <View style={styles.revenueRowContent}>
            <Text style={styles.revenueRowLabel}>This Month</Text>
            <Text style={styles.revenueRowAmount}>PKR {stats?.thisMonth.revenue.toFixed(0) || '0'}</Text>
          </View>
          <Text style={styles.revenueRowOrders}>{stats?.thisMonth.orders || 0} orders</Text>
        </View>
      </View>

      {/* Customer Insights & Key Metrics */}
      <View style={styles.insightsSection}>
        <Text style={styles.sectionTitle}>Key Insights</Text>
        <View style={styles.insightsGrid}>
          <View style={styles.insightCard}>
            <IconSymbol name="chart.bar.fill" size={24} color={Colors.light.primary} />
            <Text style={styles.insightValue}>PKR {stats?.today.averageOrderValue.toFixed(0) || '0'}</Text>
            <Text style={styles.insightLabel}>Avg Order Value</Text>
          </View>

          <View style={styles.insightCard}>
            <IconSymbol name="clock.arrow.circlepath" size={24} color={Colors.light.secondary} />
            <Text style={styles.insightValue}>18 min</Text>
            <Text style={styles.insightLabel}>Avg Prep Time</Text>
          </View>

          <View style={styles.insightCard}>
            <IconSymbol name="star.fill" size={24} color={Colors.light.warning} />
            <Text style={styles.insightValue}>4.8</Text>
            <Text style={styles.insightLabel}>Rating</Text>
          </View>

          <View style={styles.insightCard}>
            <IconSymbol name="person.2.fill" size={24} color={Colors.light.accent} />
            <Text style={styles.insightValue}>89%</Text>
            <Text style={styles.insightLabel}>Customer Return</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F0', // Warm restaurant ambiance background
  },

  // Premium Header Styles
  headerContainer: {
    backgroundColor: 'rgba(255, 248, 240, 0.95)', // Semi-transparent warm background
    borderBottomWidth: 4,
    borderBottomColor: PremiumColors.primary,
    shadowColor: PremiumColors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
    // Add subtle food-themed styling
    borderTopWidth: 1,
    borderTopColor: 'rgba(220, 20, 60, 0.1)',
  },
  headerGradient: {
    backgroundColor: Colors.light.surface,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 20,
    paddingTop: 60,
    paddingBottom: 24,
  },
  headerLeft: {
    flex: 1,
    paddingRight: 16,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  greetingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  greetingText: {
    fontSize: 18,
    color: Colors.light.textSecondary,
    fontWeight: '500',
    marginRight: 8,
  },
  greetingIcon: {
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    borderRadius: 12,
    padding: 4,
  },
  restaurantName: {
    fontSize: 26,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  timeText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
    marginLeft: 6,
  },
  iconContainer: {
    position: 'relative',
  },
  notificationButton: {
    padding: 12,
    borderRadius: 20,
    backgroundColor: Colors.light.background,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notificationBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: Colors.light.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadgeText: {
    color: Colors.light.textInverse,
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  openButton: {
    backgroundColor: Colors.light.success,
  },
  closedButton: {
    backgroundColor: Colors.light.error,
  },
  statusButtonText: {
    color: Colors.light.textInverse,
    fontWeight: 'bold',
    fontSize: 14,
  },
  // Notifications Dropdown
  notificationsDropdown: {
    backgroundColor: Colors.light.surface,
    marginHorizontal: 20,
    marginTop: -10,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 1000,
  },
  notificationsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  notificationsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  notificationIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  orderNotification: {
    backgroundColor: Colors.light.primary,
  },
  inventoryNotification: {
    backgroundColor: Colors.light.warning,
  },
  reviewNotification: {
    backgroundColor: Colors.light.success,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
    color: Colors.light.textTertiary,
  },

  // Enhanced Quick Actions - 2x2 Grid
  quickActionsGrid: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  quickActionsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  quickActionCard: {
    flex: 1,
    backgroundColor: PremiumColors.primary,
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
    shadowColor: PremiumColors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  acceptingCard: {
    backgroundColor: PremiumColors.success,
  },
  pausedCard: {
    backgroundColor: PremiumColors.danger,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 8,
    textAlign: 'center',
  },
  // Performance Metrics
  performanceSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  performanceGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  performanceCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Semi-transparent white for food theme
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: 'rgba(220, 20, 60, 0.1)', // Subtle red border
  },
  performanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  trendIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  trendUp: {
    backgroundColor: '#dcfce7',
  },
  trendDown: {
    backgroundColor: '#fef2f2',
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  trendUpText: {
    color: Colors.light.success,
  },
  trendDownText: {
    color: Colors.light.error,
  },
  performanceValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 4,
  },
  performanceLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },

  // Enhanced Order Status
  orderStatusSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  orderStatusGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statusCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    gap: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusCardNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
  },
  statusCardLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textInverse,
    textAlign: 'center',
  },
  statusCardSubtext: {
    fontSize: 11,
    color: Colors.light.textInverse,
    textAlign: 'center',
    opacity: 0.8,
  },
  // Section Headers
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 16,
    color: Colors.light.primary,
    fontWeight: '600',
  },

  // Recent Orders
  recentOrdersSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  recentOrdersList: {
    gap: 12,
  },
  recentOrderCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Semi-transparent white for food theme
    padding: 16,
    borderRadius: 12,
    shadowColor: PremiumColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'rgba(220, 20, 60, 0.08)', // Very subtle red border
  },
  orderCardLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  orderStatusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 2,
  },
  orderCustomer: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 2,
  },
  orderDetails: {
    fontSize: 12,
    color: Colors.light.textTertiary,
  },
  orderCardRight: {
    alignItems: 'flex-end',
  },
  orderTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 2,
  },
  orderStatus: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },

  // Revenue Section - Three Rows
  revenueSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  revenueRowCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Semi-transparent white for food theme
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: 'rgba(220, 20, 60, 0.08)', // Very subtle red border
  },
  revenueRowContent: {
    flex: 1,
    marginLeft: 12,
  },
  revenueRowLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  revenueRowAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  revenueRowOrders: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    fontWeight: '500',
  },

  // Customer Insights
  insightsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  insightsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  insightCard: {
    width: (width - 52) / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Semi-transparent white for food theme
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: 'rgba(220, 20, 60, 0.08)', // Very subtle red border
  },
  insightValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginTop: 8,
    marginBottom: 4,
  },
  insightLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },



  // Premium Order Status Styles
  pulsingDot: {
    position: 'absolute',
    left: 8,
    top: 8,
    width: 8,
    height: 8,
  },
  pulseRing: {
    width: 8,
    height: 8,
    borderRadius: 4,
    opacity: 0.6,
  },
  orderTime: {
    fontSize: 11,
    color: PremiumColors.grayDark,
    marginTop: 2,
  },
});
