import { IconSymbol } from '@/components/ui/IconSymbol';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Alert, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function ReportBug() {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    stepsToReproduce: '',
    expectedBehavior: '',
    actualBehavior: '',
    severity: 'medium',
    category: '',
    email: '',
  });

  const severityLevels = [
    { value: 'low', label: 'Low - Minor issue', color: '#22C55E' },
    { value: 'medium', label: 'Medium - Affects functionality', color: '#F59E0B' },
    { value: 'high', label: 'High - Major problem', color: '#EF4444' },
    { value: 'critical', label: 'Critical - App crashes', color: '#991B1B' },
  ];

  const categories = [
    'Login/Authentication',
    'Order Management',
    'Menu Management',
    'Analytics',
    'Staff Management',
    'Settings',
    'Performance',
    'User Interface',
    'Notifications',
    'Other'
  ];

  const handleSubmit = () => {
    if (!formData.title.trim() || !formData.description.trim()) {
      Alert.alert('Error', 'Please fill in the title and description fields.');
      return;
    }

    // Here you would typically send the data to your backend
    Alert.alert(
      'Bug Report Submitted!',
      'Thank you for reporting this issue. Our development team will investigate and work on a fix.',
      [
        {
          text: 'OK',
          onPress: () => router.back()
        }
      ]
    );
  };

  const renderCategoryButton = (category: string) => (
    <TouchableOpacity
      key={category}
      style={[
        styles.categoryButton,
        formData.category === category && styles.selectedCategory
      ]}
      onPress={() => setFormData(prev => ({ ...prev, category }))}
    >
      <Text style={[
        styles.categoryText,
        formData.category === category && styles.selectedCategoryText
      ]}>
        {category}
      </Text>
    </TouchableOpacity>
  );

  const renderSeverityButton = (severity: typeof severityLevels[0]) => (
    <TouchableOpacity
      key={severity.value}
      style={[
        styles.severityButton,
        formData.severity === severity.value && { backgroundColor: `${severity.color}20` }
      ]}
      onPress={() => setFormData(prev => ({ ...prev, severity: severity.value }))}
    >
      <View style={[
        styles.severityDot,
        { backgroundColor: severity.color }
      ]} />
      <Text style={[
        styles.severityText,
        formData.severity === severity.value && { color: severity.color }
      ]}>
        {severity.label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#DC143C', '#B91C3C']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <IconSymbol name="chevron.left" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Report a Bug</Text>
          <View style={styles.headerRight} />
        </View>
      </LinearGradient>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Introduction */}
        <View style={styles.section}>
          <View style={styles.introCard}>
            <IconSymbol name="alert" size={32} color="#EF4444" />
            <Text style={styles.introTitle}>Found a Bug?</Text>
            <Text style={styles.introText}>
              Help us improve FoodWay by reporting any issues you encounter. The more details you provide, the faster we can fix it.
            </Text>
          </View>
        </View>

        {/* Bug Title */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Bug Title *</Text>
          <View style={styles.card}>
            <TextInput
              style={styles.input}
              placeholder="Brief description of the bug"
              placeholderTextColor="#9CA3AF"
              value={formData.title}
              onChangeText={(text) => setFormData(prev => ({ ...prev, title: text }))}
              maxLength={100}
            />
            <Text style={styles.charCount}>{formData.title.length}/100</Text>
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description *</Text>
          <View style={styles.card}>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Describe what happened and what you were trying to do when the bug occurred"
              placeholderTextColor="#9CA3AF"
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              maxLength={500}
            />
            <Text style={styles.charCount}>{formData.description.length}/500</Text>
          </View>
        </View>

        {/* Steps to Reproduce */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Steps to Reproduce</Text>
          <View style={styles.card}>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="1. Go to...&#10;2. Click on...&#10;3. Enter...&#10;4. Bug appears"
              placeholderTextColor="#9CA3AF"
              value={formData.stepsToReproduce}
              onChangeText={(text) => setFormData(prev => ({ ...prev, stepsToReproduce: text }))}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              maxLength={300}
            />
            <Text style={styles.charCount}>{formData.stepsToReproduce.length}/300</Text>
          </View>
        </View>

        {/* Expected vs Actual Behavior */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Expected Behavior</Text>
          <View style={styles.card}>
            <TextInput
              style={styles.input}
              placeholder="What should have happened?"
              placeholderTextColor="#9CA3AF"
              value={formData.expectedBehavior}
              onChangeText={(text) => setFormData(prev => ({ ...prev, expectedBehavior: text }))}
              maxLength={200}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Actual Behavior</Text>
          <View style={styles.card}>
            <TextInput
              style={styles.input}
              placeholder="What actually happened?"
              placeholderTextColor="#9CA3AF"
              value={formData.actualBehavior}
              onChangeText={(text) => setFormData(prev => ({ ...prev, actualBehavior: text }))}
              maxLength={200}
            />
          </View>
        </View>

        {/* Severity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Severity Level</Text>
          <View style={styles.card}>
            {severityLevels.map(renderSeverityButton)}
          </View>
        </View>

        {/* Category */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Category</Text>
          <View style={styles.card}>
            <View style={styles.categoryGrid}>
              {categories.map(renderCategoryButton)}
            </View>
          </View>
        </View>

        {/* Contact Email */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Email (Optional)</Text>
          <View style={styles.card}>
            <TextInput
              style={styles.input}
              placeholder="<EMAIL>"
              placeholderTextColor="#9CA3AF"
              value={formData.email}
              onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            <Text style={styles.helperText}>
              We'll contact you if we need more information about this bug.
            </Text>
          </View>
        </View>

        {/* Submit Button */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <IconSymbol name="send" size={20} color="#FFFFFF" />
            <Text style={styles.submitButtonText}>Submit Bug Report</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8F5E8',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerRight: {
    width: 40,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  card: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  introCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  introTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginTop: 12,
    marginBottom: 8,
  },
  introText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  input: {
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  charCount: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'right',
    marginTop: 8,
  },
  helperText: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 8,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  selectedCategory: {
    backgroundColor: '#DC143C',
    borderColor: '#DC143C',
  },
  categoryText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  selectedCategoryText: {
    color: '#FFFFFF',
  },
  severityButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
  },
  severityDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  severityText: {
    fontSize: 16,
    color: '#1F2937',
    fontWeight: '500',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#DC143C',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  submitButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  bottomSpacing: {
    height: 40,
  },
});
