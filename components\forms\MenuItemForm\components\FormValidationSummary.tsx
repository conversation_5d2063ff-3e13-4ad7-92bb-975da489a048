import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import React from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { FormValidationError } from '../types';

interface FormValidationSummaryProps {
  errors: FormValidationError[];
  onErrorPress: (field: string) => void;
}

export function FormValidationSummary({
  errors,
  onErrorPress,
}: FormValidationSummaryProps) {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const expandAnimation = useSharedValue(0);
  const fadeAnimation = useSharedValue(0);

  React.useEffect(() => {
    if (errors.length > 0) {
      fadeAnimation.value = withTiming(1, { duration: 300 });
    } else {
      fadeAnimation.value = withTiming(0, { duration: 300 });
    }
  }, [errors.length]);

  React.useEffect(() => {
    expandAnimation.value = withSpring(isExpanded ? 1 : 0, {
      damping: 20,
      stiffness: 150,
    });
  }, [isExpanded]);

  const animatedContainerStyle = useAnimatedStyle(() => ({
    opacity: fadeAnimation.value,
    transform: [{ translateY: (1 - fadeAnimation.value) * -10 }],
  }));

  const animatedContentStyle = useAnimatedStyle(() => ({
    maxHeight: expandAnimation.value * 200,
    opacity: expandAnimation.value,
  }));

  if (errors.length === 0) {
    return null;
  }

  const errorsByType = {
    error: errors.filter(e => e.severity === 'error'),
    warning: errors.filter(e => e.severity === 'warning'),
    info: errors.filter(e => e.severity === 'info'),
  };

  const totalErrors = errorsByType.error.length;
  const totalWarnings = errorsByType.warning.length;
  const totalInfo = errorsByType.info.length;



  return (
    <Animated.View style={[styles.container, animatedContainerStyle]}>
      <TouchableOpacity
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
        activeOpacity={0.7}
      >
        <View style={styles.headerLeft}>
          <IconSymbol
            name={totalErrors > 0 ? 'exclamationmark.circle' : 'exclamationmark.triangle'}
            size={20}
            color={totalErrors > 0 ? Colors.light.error : Colors.light.warning}
          />
          <View style={styles.summaryText}>
            <Text style={styles.summaryTitle}>
              {totalErrors > 0 ? 'Issues Found' : 'Warnings'}
            </Text>
            <Text style={styles.summarySubtitle}>
              {totalErrors > 0 && `${totalErrors} error${totalErrors > 1 ? 's' : ''}`}
              {totalErrors > 0 && totalWarnings > 0 && ', '}
              {totalWarnings > 0 && `${totalWarnings} warning${totalWarnings > 1 ? 's' : ''}`}
              {totalInfo > 0 && `, ${totalInfo} suggestion${totalInfo > 1 ? 's' : ''}`}
            </Text>
          </View>
        </View>
        
        <IconSymbol
          name={isExpanded ? 'chevron.up' : 'chevron.down'}
          size={16}
          color={Colors.light.textSecondary}
        />
      </TouchableOpacity>

      <Animated.View style={[styles.content, animatedContentStyle]}>
        <ScrollView
          style={styles.errorsList}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled
        >
          {/* Errors */}
          {errorsByType.error.map((error, index) => (
            <ErrorItem
              key={`error-${index}`}
              error={error}
              onPress={() => onErrorPress(error.field)}
            />
          ))}
          
          {/* Warnings */}
          {errorsByType.warning.map((error, index) => (
            <ErrorItem
              key={`warning-${index}`}
              error={error}
              onPress={() => onErrorPress(error.field)}
            />
          ))}
          
          {/* Info */}
          {errorsByType.info.map((error, index) => (
            <ErrorItem
              key={`info-${index}`}
              error={error}
              onPress={() => onErrorPress(error.field)}
            />
          ))}
        </ScrollView>
      </Animated.View>
    </Animated.View>
  );
}

interface ErrorItemProps {
  error: FormValidationError;
  onPress: () => void;
}

function ErrorItem({ error, onPress }: ErrorItemProps) {
  const scaleAnimation = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleAnimation.value }],
  }));

  const handlePress = () => {
    scaleAnimation.value = withSpring(0.98, { damping: 15, stiffness: 300 });
    setTimeout(() => {
      scaleAnimation.value = withSpring(1, { damping: 15, stiffness: 300 });
    }, 100);
    onPress();
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return Colors.light.error;
      case 'warning':
        return Colors.light.warning;
      case 'info':
        return Colors.light.info;
      default:
        return Colors.light.textSecondary;
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'exclamationmark.circle';
      case 'warning':
        return 'exclamationmark.triangle';
      case 'info':
        return 'info.circle';
      default:
        return 'circle';
    }
  };

  const getSeverityBackgroundColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return Colors.light.errorLight;
      case 'warning':
        return Colors.light.warningLight;
      case 'info':
        return Colors.light.infoLight;
      default:
        return Colors.light.backgroundSecondary;
    }
  };

  return (
    <Animated.View style={animatedStyle}>
      <TouchableOpacity
        style={[
          styles.errorItem,
          { backgroundColor: getSeverityBackgroundColor(error.severity) },
        ]}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <IconSymbol
          name={getSeverityIcon(error.severity)}
          size={16}
          color={getSeverityColor(error.severity)}
        />
        <View style={styles.errorContent}>
          <Text style={styles.errorField}>{error.field}</Text>
          <Text style={styles.errorMessage}>{error.message}</Text>
        </View>
        <IconSymbol
          name="chevron.right"
          size={14}
          color={Colors.light.textTertiary}
        />
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    borderRadius: 12,
    backgroundColor: Colors.light.backgroundSecondary,
    borderWidth: 1,
    borderColor: Colors.light.border,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  summaryText: {
    marginLeft: 12,
    flex: 1,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  summarySubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginTop: 2,
  },
  content: {
    overflow: 'hidden',
  },
  errorsList: {
    maxHeight: 200,
  },
  errorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  errorContent: {
    flex: 1,
    marginLeft: 12,
    marginRight: 8,
  },
  errorField: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    textTransform: 'capitalize',
  },
  errorMessage: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    marginTop: 2,
    lineHeight: 18,
  },
});

export default FormValidationSummary;
