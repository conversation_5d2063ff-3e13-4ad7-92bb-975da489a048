import { FontAwesome, Ionicons, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { PremiumColors } from '../../constants/PremiumTheme';

// Icon library types
type IconLibrary = 'MaterialCommunityIcons' | 'MaterialIcons' | 'FontAwesome' | 'Ionicons';

// Icon size standards
export const IconSizes = {
  xs: 12,
  sm: 16,
  md: 24,
  lg: 32,
  xl: 48,
  xxl: 64,
} as const;

export type IconSize = keyof typeof IconSizes | number;

// Icon color presets
export const IconColors = {
  primary: PremiumColors.primary,
  primaryLight: PremiumColors.primaryLight,
  black: PremiumColors.black,
  white: PremiumColors.white,
  success: PremiumColors.success,
  warning: PremiumColors.warning,
  danger: PremiumColors.danger,
  gray: PremiumColors.grayMedium,
  grayDark: PremiumColors.grayDark,
  grayLight: PremiumColors.grayLight,
} as const;

export type IconColor = keyof typeof IconColors | string;

// AppIcon component props
interface AppIconProps {
  name: string;
  size?: IconSize;
  color?: IconColor;
  library?: IconLibrary;
  style?: any;
}

// Main AppIcon component
export const AppIcon: React.FC<AppIconProps> = ({
  name,
  size = 'md',
  color = 'black',
  library = 'MaterialCommunityIcons',
  style,
}) => {
  // Get numeric size
  const iconSize = typeof size === 'number' ? size : IconSizes[size];
  
  // Get color value
  const iconColor = typeof color === 'string' && color in IconColors 
    ? IconColors[color as keyof typeof IconColors] 
    : color as string;

  // Select icon component based on library
  const IconComponent = {
    MaterialCommunityIcons,
    MaterialIcons,
    FontAwesome,
    Ionicons,
  }[library];

  return (
    <IconComponent 
      name={name as any} 
      size={iconSize} 
      color={iconColor} 
      style={style}
    />
  );
};

// Restaurant-specific icon mappings
export const RestaurantIcons = {
  // Authentication
  auth: {
    logo: 'store-outline',
    email: 'email-outline',
    password: 'lock-outline',
    showPassword: 'eye-outline',
    hidePassword: 'eye-off-outline',
    login: 'login-variant',
    register: 'account-plus-outline',
    forgotPassword: 'help-circle-outline',
    phone: 'phone-outline',
    user: 'account-outline',
    restaurant: 'store',
    address: 'map-marker-outline',
    next: 'arrow-right-circle',
    back: 'arrow-left-circle',
    submit: 'check-circle',
  },

  // Navigation
  nav: {
    home: 'home-outline',
    menu: 'food-fork-drink',
    orders: 'clipboard-list-outline',
    analytics: 'chart-line',
    profile: 'account-circle-outline',
    settings: 'cog-outline',
    back: 'arrow-left',
    close: 'close',
    search: 'magnify',
    filter: 'filter-variant',
    sort: 'sort-variant',
    more: 'dots-vertical',
  },

  // Dashboard
  dashboard: {
    revenue: 'currency-usd',
    orders: 'clipboard-list-outline',
    customers: 'account-group-outline',
    rating: 'star-circle-outline',
    newOrder: 'plus-circle',
    viewMenu: 'food-fork-drink',
    analytics: 'chart-line',
    notifications: 'bell-outline',
    time: 'clock-outline',
    trending_up: 'trending-up',
    trending_down: 'trending-down',
  },

  // Menu Management
  menu: {
    addItem: 'plus-circle',
    edit: 'pencil-outline',
    delete: 'delete-outline',
    duplicate: 'content-copy',
    favorite: 'heart-outline',
    favorited: 'heart',
    available: 'check-circle',
    unavailable: 'close-circle',
    price: 'currency-usd',
    time: 'clock-outline',
    calories: 'fire',
    spicy: 'chili-hot',
    vegetarian: 'leaf',
    vegan: 'sprout-outline',
    glutenFree: 'wheat-off',
    addPhoto: 'camera-plus-outline',
    gallery: 'image-multiple-outline',
  },

  // Food Categories
  categories: {
    burgers: 'hamburger',
    pizza: 'pizza',
    pasta: 'pasta',
    salads: 'salad',
    drinks: 'cup-outline',
    desserts: 'cake-variant',
    appetizers: 'food-apple-outline',
    mains: 'silverware-fork-knife',
    breakfast: 'coffee-outline',
    lunch: 'food-variant',
    dinner: 'silverware-fork-knife',
    snacks: 'food-croissant',
    seafood: 'fish',
    bbq: 'grill-outline',
    chinese: 'noodles',
    italian: 'pasta',
    indian: 'chili-hot',
    mexican: 'taco',
    american: 'hamburger',
    japanese: 'sushi',
  },

  // Order Management
  orders: {
    pending: 'clock-outline',
    confirmed: 'check-circle-outline',
    preparing: 'chef-hat',
    ready: 'bell-ring-outline',
    delivered: 'truck-delivery-outline',
    cancelled: 'close-circle-outline',
    dineIn: 'silverware-fork-knife',
    takeout: 'bag-personal-outline',
    delivery: 'truck-delivery',
    paid: 'credit-card-check-outline',
    unpaid: 'credit-card-clock-outline',
    cash: 'cash',
    card: 'credit-card-outline',
    viewDetails: 'eye-outline',
    printReceipt: 'printer-outline',
    callCustomer: 'phone-outline',
    messageCustomer: 'message-outline',
  },

  // Customer Management
  customers: {
    profile: 'account-circle-outline',
    addCustomer: 'account-plus-outline',
    search: 'account-search-outline',
    phone: 'phone-outline',
    email: 'email-outline',
    address: 'map-marker-outline',
    birthday: 'cake-variant-outline',
    orders: 'clipboard-list-outline',
    spending: 'currency-usd',
    lastOrder: 'history',
    loyalty: 'star-circle-outline',
    vip: 'crown-outline',
    regular: 'account-check-outline',
    new: 'account-plus-outline',
    reviews: 'star-outline',
    feedback: 'comment-text-outline',
  },

  // Analytics
  analytics: {
    chart: 'chart-line',
    dateRange: 'calendar-range',
    export: 'download-outline',
    lineChart: 'chart-line',
    barChart: 'chart-bar',
    pieChart: 'chart-pie',
    today: 'calendar-today',
    week: 'calendar-week',
    month: 'calendar-month',
    year: 'calendar-year',
    target: 'target',
    goal: 'flag-checkered',
    growth: 'chart-line-stacked',
  },

  // Settings
  settings: {
    profile: 'account-circle',
    editProfile: 'account-edit',
    changePassword: 'lock-reset',
    restaurant: 'store-settings',
    hours: 'clock-edit-outline',
    location: 'map-marker-edit',
    contact: 'phone-settings-outline',
    notifications: 'bell-settings-outline',
    theme: 'palette-outline',
    language: 'translate',
    privacy: 'shield-account-outline',
    payment: 'credit-card-settings-outline',
    taxes: 'calculator-variant',
    receipts: 'receipt-outline',
    inventory: 'package-variant-closed',
    help: 'help-circle-outline',
    support: 'headset',
    feedback: 'comment-quote-outline',
    rateApp: 'star-outline',
    logout: 'logout-variant',
    deleteAccount: 'account-remove-outline',
    backup: 'backup-restore',
    toggleOn: 'toggle-switch',
    toggleOff: 'toggle-switch-off-outline',
    arrowRight: 'chevron-right',
    externalLink: 'open-in-new',
  },

  // Actions
  actions: {
    add: 'plus',
    edit: 'pencil',
    delete: 'delete',
    save: 'content-save',
    cancel: 'close',
    confirm: 'check',
    refresh: 'refresh',
    share: 'share-variant',
    download: 'download',
    upload: 'upload',
    copy: 'content-copy',
    paste: 'content-paste',
    cut: 'content-cut',
    undo: 'undo',
    redo: 'redo',
  },

  // Status
  status: {
    success: 'check-circle',
    error: 'alert-circle',
    warning: 'alert-triangle',
    info: 'information',
    loading: 'loading',
    online: 'wifi',
    offline: 'wifi-off',
    sync: 'sync',
    syncing: 'sync-circle',
  },
} as const;

// Helper function to get restaurant icon
export const getRestaurantIcon = (category: keyof typeof RestaurantIcons, icon: string) => {
  return RestaurantIcons[category]?.[icon as keyof typeof RestaurantIcons[typeof category]] || icon;
};

// Animated icon wrapper (for future micro-interactions)
export const AnimatedIcon: React.FC<AppIconProps & { animated?: boolean }> = ({
  animated = false,
  ...props
}) => {
  // For now, return regular icon - can be enhanced with animations later
  return <AppIcon {...props} />;
};

export default AppIcon;
